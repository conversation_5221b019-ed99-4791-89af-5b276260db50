2025-08-28 17:07:12 - model_training - INFO - 模型名称: Random Forest
2025-08-28 17:07:12 - model_training - INFO - 准确率: 0.8500
2025-08-28 17:07:12 - model_training - INFO - AUC: 0.9501
2025-08-28 17:07:12 - model_training - INFO - AUPRC: 0.9410
2025-08-28 17:07:12 - model_training - INFO - 混淆矩阵:
2025-08-28 17:07:12 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-28 17:07:12 - model_training - INFO - 
分类报告:
2025-08-28 17:07:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-28 17:07:12 - model_training - INFO - 训练时间: 0.22 秒
2025-08-28 17:07:12 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-28 17:07:12 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_170712\models\RandomForest_single_170712.joblib
2025-08-28 17:07:12 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-28 17:07:12 - model_training - WARNING - [XGBoost] PyTorch未安装，无法检测GPU，使用CPU模式
2025-08-28 17:07:12 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-28 17:07:13 - model_training - INFO - 模型名称: XGBoost
2025-08-28 17:07:13 - model_training - INFO - 准确率: 0.8250
2025-08-28 17:07:13 - model_training - INFO - AUC: 0.9437
2025-08-28 17:07:13 - model_training - INFO - AUPRC: 0.9387
2025-08-28 17:07:13 - model_training - INFO - 混淆矩阵:
2025-08-28 17:07:13 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-28 17:07:13 - model_training - INFO - 
分类报告:
2025-08-28 17:07:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-28 17:07:13 - model_training - INFO - 训练时间: 0.06 秒
2025-08-28 17:07:13 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-28 17:07:13 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_170712\models\XGBoost_single_170713.joblib
2025-08-28 17:07:13 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-28 17:07:13 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-28 17:07:13 - model_training - INFO - 模型名称: LightGBM
2025-08-28 17:07:13 - model_training - INFO - 准确率: 0.8750
2025-08-28 17:07:13 - model_training - INFO - AUC: 0.9642
2025-08-28 17:07:13 - model_training - INFO - AUPRC: 0.9541
2025-08-28 17:07:13 - model_training - INFO - 混淆矩阵:
2025-08-28 17:07:13 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-28 17:07:13 - model_training - INFO - 
分类报告:
2025-08-28 17:07:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-28 17:07:13 - model_training - INFO - 训练时间: 0.09 秒
2025-08-28 17:07:13 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-28 17:07:13 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_170712\models\LightGBM_single_170713.joblib
2025-08-28 17:07:13 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-28 17:07:13 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-28 17:07:14 - model_training - INFO - 模型名称: CatBoost
2025-08-28 17:07:14 - model_training - INFO - 准确率: 0.8000
2025-08-28 17:07:14 - model_training - INFO - AUC: 0.9540
2025-08-28 17:07:14 - model_training - INFO - AUPRC: 0.9486
2025-08-28 17:07:14 - model_training - INFO - 混淆矩阵:
2025-08-28 17:07:14 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-28 17:07:14 - model_training - INFO - 
分类报告:
2025-08-28 17:07:14 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-28 17:07:14 - model_training - INFO - 训练时间: 1.25 秒
2025-08-28 17:07:14 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8000
2025-08-28 17:07:14 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_170712\models\CatBoost_single_170714.joblib
2025-08-28 17:07:14 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-28 17:07:16 - model_training - INFO - 模型名称: Logistic Regression
2025-08-28 17:07:16 - model_training - INFO - 准确率: 0.7750
2025-08-28 17:07:16 - model_training - INFO - AUC: 0.9028
2025-08-28 17:07:16 - model_training - INFO - AUPRC: 0.8685
2025-08-28 17:07:16 - model_training - INFO - 混淆矩阵:
2025-08-28 17:07:16 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-28 17:07:16 - model_training - INFO - 
分类报告:
2025-08-28 17:07:16 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-28 17:07:16 - model_training - INFO - 训练时间: 2.23 秒
2025-08-28 17:07:16 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-28 17:07:16 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_170712\models\Logistic_single_170716.joblib
2025-08-28 17:07:16 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-28 17:07:16 - model_training - INFO - 模型名称: SVM
2025-08-28 17:07:16 - model_training - INFO - 准确率: 0.8250
2025-08-28 17:07:16 - model_training - INFO - AUC: 0.9258
2025-08-28 17:07:16 - model_training - INFO - AUPRC: 0.9259
2025-08-28 17:07:16 - model_training - INFO - 混淆矩阵:
2025-08-28 17:07:16 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-28 17:07:16 - model_training - INFO - 
分类报告:
2025-08-28 17:07:16 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-28 17:07:16 - model_training - INFO - 训练时间: 0.01 秒
2025-08-28 17:07:16 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-28 17:07:16 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_170712\models\SVM_single_170716.joblib
2025-08-28 17:07:16 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\SVM_results.joblib
2025-08-28 17:23:39 - model_training - INFO - 模型名称: KNN
2025-08-28 17:23:39 - model_training - INFO - 准确率: 0.8000
2025-08-28 17:23:39 - model_training - INFO - AUC: 0.9054
2025-08-28 17:23:39 - model_training - INFO - AUPRC: 0.8760
2025-08-28 17:23:39 - model_training - INFO - 混淆矩阵:
2025-08-28 17:23:39 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-28 17:23:39 - model_training - INFO - 
分类报告:
2025-08-28 17:23:39 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-28 17:23:39 - model_training - INFO - 训练时间: 982.64 秒
2025-08-28 17:23:39 - model_training - INFO - 模型 KNN 性能: 准确率=0.8000
2025-08-28 17:23:39 - model_training - INFO - 模型 KNN 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_170712\models\KNN_single_172339.joblib
2025-08-28 17:23:39 - model_training - INFO - 模型 KNN 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\KNN_results.joblib
2025-08-28 17:50:51 - model_training - INFO - 模型名称: Random Forest
2025-08-28 17:50:51 - model_training - INFO - 准确率: 0.8500
2025-08-28 17:50:51 - model_training - INFO - AUC: 0.9501
2025-08-28 17:50:51 - model_training - INFO - AUPRC: 0.9410
2025-08-28 17:50:51 - model_training - INFO - 混淆矩阵:
2025-08-28 17:50:51 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-28 17:50:51 - model_training - INFO - 
分类报告:
2025-08-28 17:50:51 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-28 17:50:51 - model_training - INFO - 训练时间: 0.19 秒
2025-08-28 17:50:51 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-28 17:50:51 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_175051\models\RandomForest_single_175051.joblib
2025-08-28 17:50:51 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-28 17:50:51 - model_training - WARNING - [XGBoost] PyTorch未安装，无法检测GPU，使用CPU模式
2025-08-28 17:50:51 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-28 17:50:51 - model_training - INFO - 模型名称: XGBoost
2025-08-28 17:50:51 - model_training - INFO - 准确率: 0.8250
2025-08-28 17:50:51 - model_training - INFO - AUC: 0.9437
2025-08-28 17:50:51 - model_training - INFO - AUPRC: 0.9387
2025-08-28 17:50:51 - model_training - INFO - 混淆矩阵:
2025-08-28 17:50:51 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-28 17:50:51 - model_training - INFO - 
分类报告:
2025-08-28 17:50:51 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-28 17:50:51 - model_training - INFO - 训练时间: 0.05 秒
2025-08-28 17:50:51 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-28 17:50:51 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_175051\models\XGBoost_single_175051.joblib
2025-08-28 17:50:51 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-28 17:50:51 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-28 17:50:51 - model_training - INFO - 模型名称: LightGBM
2025-08-28 17:50:51 - model_training - INFO - 准确率: 0.8750
2025-08-28 17:50:51 - model_training - INFO - AUC: 0.9642
2025-08-28 17:50:51 - model_training - INFO - AUPRC: 0.9541
2025-08-28 17:50:51 - model_training - INFO - 混淆矩阵:
2025-08-28 17:50:51 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-28 17:50:51 - model_training - INFO - 
分类报告:
2025-08-28 17:50:51 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-28 17:50:51 - model_training - INFO - 训练时间: 0.08 秒
2025-08-28 17:50:51 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-28 17:50:51 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_175051\models\LightGBM_single_175051.joblib
2025-08-28 17:50:51 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-28 17:50:51 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-28 17:50:53 - model_training - INFO - 模型名称: CatBoost
2025-08-28 17:50:53 - model_training - INFO - 准确率: 0.8000
2025-08-28 17:50:53 - model_training - INFO - AUC: 0.9540
2025-08-28 17:50:53 - model_training - INFO - AUPRC: 0.9486
2025-08-28 17:50:53 - model_training - INFO - 混淆矩阵:
2025-08-28 17:50:53 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-28 17:50:53 - model_training - INFO - 
分类报告:
2025-08-28 17:50:53 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-28 17:50:53 - model_training - INFO - 训练时间: 1.14 秒
2025-08-28 17:50:53 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8000
2025-08-28 17:50:53 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_175051\models\CatBoost_single_175053.joblib
2025-08-28 17:50:53 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-28 17:50:55 - model_training - INFO - 模型名称: Logistic Regression
2025-08-28 17:50:55 - model_training - INFO - 准确率: 0.7750
2025-08-28 17:50:55 - model_training - INFO - AUC: 0.9028
2025-08-28 17:50:55 - model_training - INFO - AUPRC: 0.8685
2025-08-28 17:50:55 - model_training - INFO - 混淆矩阵:
2025-08-28 17:50:55 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-28 17:50:55 - model_training - INFO - 
分类报告:
2025-08-28 17:50:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-28 17:50:55 - model_training - INFO - 训练时间: 2.33 秒
2025-08-28 17:50:55 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-28 17:50:55 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_175051\models\Logistic_single_175055.joblib
2025-08-28 17:50:55 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-28 17:50:55 - model_training - INFO - 模型名称: SVM
2025-08-28 17:50:55 - model_training - INFO - 准确率: 0.8250
2025-08-28 17:50:55 - model_training - INFO - AUC: 0.9258
2025-08-28 17:50:55 - model_training - INFO - AUPRC: 0.9259
2025-08-28 17:50:55 - model_training - INFO - 混淆矩阵:
2025-08-28 17:50:55 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-28 17:50:55 - model_training - INFO - 
分类报告:
2025-08-28 17:50:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-28 17:50:55 - model_training - INFO - 训练时间: 0.01 秒
2025-08-28 17:50:55 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-28 17:50:55 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_01_updated\training_sessions\20250828_175051\models\SVM_single_175055.joblib
2025-08-28 17:50:55 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_01_updated\cache\SVM_results.joblib
2025-08-28 23:59:54 - model_training - INFO - XGBoost使用GPU加速
2025-08-28 23:59:54 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.083 以处理不平衡
2025-08-28 23:59:54 - model_training - INFO - 模型名称: XGBoost
2025-08-28 23:59:54 - model_training - INFO - 准确率: 1.0000
2025-08-28 23:59:54 - model_training - INFO - AUC: 1.0000
2025-08-28 23:59:54 - model_training - INFO - AUPRC: 1.0000
2025-08-28 23:59:54 - model_training - INFO - 混淆矩阵:
2025-08-28 23:59:54 - model_training - INFO - 
[[ 9  0]
 [ 0 11]]
2025-08-28 23:59:54 - model_training - INFO - 
分类报告:
2025-08-28 23:59:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      1.00      1.00         9
           1       1.00      1.00      1.00        11

    accuracy                           1.00        20
   macro avg       1.00      1.00      1.00        20
weighted avg       1.00      1.00      1.00        20

2025-08-28 23:59:54 - model_training - INFO - 训练时间: 0.13 秒
2025-08-28 23:59:54 - model_training - INFO - 模型 XGBoost 性能: 准确率=1.0000
2025-08-28 23:59:54 - model_training - INFO - 模型 XGBoost 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-28 23:59:54 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_feature_names.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 00:53:11 - model_training - INFO - 准确率: 0.8500
2025-08-29 00:53:11 - model_training - INFO - AUC: 0.8760
2025-08-29 00:53:11 - model_training - INFO - AUPRC: 0.7701
2025-08-29 00:53:11 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:11 - model_training - INFO - 
[[18  5]
 [ 1 16]]
2025-08-29 00:53:11 - model_training - INFO - 
分类报告:
2025-08-29 00:53:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 00:53:11 - model_training - INFO - 训练时间: 0.13 秒
2025-08-29 00:53:11 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8500
2025-08-29 00:53:11 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\DecisionTree_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型名称: Random Forest
2025-08-29 00:53:11 - model_training - INFO - 准确率: 0.9000
2025-08-29 00:53:11 - model_training - INFO - AUC: 0.9655
2025-08-29 00:53:11 - model_training - INFO - AUPRC: 0.9497
2025-08-29 00:53:11 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:11 - model_training - INFO - 
[[19  4]
 [ 0 17]]
2025-08-29 00:53:11 - model_training - INFO - 
分类报告:
2025-08-29 00:53:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.83      0.90        23
           1       0.81      1.00      0.89        17

    accuracy                           0.90        40
   macro avg       0.90      0.91      0.90        40
weighted avg       0.92      0.90      0.90        40

2025-08-29 00:53:11 - model_training - INFO - 训练时间: 0.19 秒
2025-08-29 00:53:11 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9000
2025-08-29 00:53:11 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\RandomForest_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 00:53:11 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 00:53:11 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 00:53:11 - model_training - INFO - 模型名称: XGBoost
2025-08-29 00:53:11 - model_training - INFO - 准确率: 0.9000
2025-08-29 00:53:11 - model_training - INFO - AUC: 0.9565
2025-08-29 00:53:11 - model_training - INFO - AUPRC: 0.9248
2025-08-29 00:53:11 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:11 - model_training - INFO - 
[[19  4]
 [ 0 17]]
2025-08-29 00:53:11 - model_training - INFO - 
分类报告:
2025-08-29 00:53:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.83      0.90        23
           1       0.81      1.00      0.89        17

    accuracy                           0.90        40
   macro avg       0.90      0.91      0.90        40
weighted avg       0.92      0.90      0.90        40

2025-08-29 00:53:11 - model_training - INFO - 训练时间: 0.15 秒
2025-08-29 00:53:11 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-29 00:53:11 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\XGBoost_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 00:53:11 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 00:53:11 - model_training - INFO - 模型名称: LightGBM
2025-08-29 00:53:11 - model_training - INFO - 准确率: 0.9500
2025-08-29 00:53:11 - model_training - INFO - AUC: 0.9770
2025-08-29 00:53:11 - model_training - INFO - AUPRC: 0.9608
2025-08-29 00:53:11 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:11 - model_training - INFO - 
[[21  2]
 [ 0 17]]
2025-08-29 00:53:11 - model_training - INFO - 
分类报告:
2025-08-29 00:53:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.91      0.95        23
           1       0.89      1.00      0.94        17

    accuracy                           0.95        40
   macro avg       0.95      0.96      0.95        40
weighted avg       0.96      0.95      0.95        40

2025-08-29 00:53:11 - model_training - INFO - 训练时间: 0.14 秒
2025-08-29 00:53:11 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.9500
2025-08-29 00:53:11 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\LightGBM_single_005311.joblib
2025-08-29 00:53:11 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 00:53:11 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 00:53:11 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 00:53:29 - model_training - INFO - 模型名称: CatBoost
2025-08-29 00:53:29 - model_training - INFO - 准确率: 0.9500
2025-08-29 00:53:29 - model_training - INFO - AUC: 0.9923
2025-08-29 00:53:29 - model_training - INFO - AUPRC: 0.9896
2025-08-29 00:53:29 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:29 - model_training - INFO - 
[[21  2]
 [ 0 17]]
2025-08-29 00:53:29 - model_training - INFO - 
分类报告:
2025-08-29 00:53:29 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.91      0.95        23
           1       0.89      1.00      0.94        17

    accuracy                           0.95        40
   macro avg       0.95      0.96      0.95        40
weighted avg       0.96      0.95      0.95        40

2025-08-29 00:53:29 - model_training - INFO - 训练时间: 17.99 秒
2025-08-29 00:53:29 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.9500
2025-08-29 00:53:29 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\CatBoost_single_005329.joblib
2025-08-29 00:53:29 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 00:53:29 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 00:53:29 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 00:53:29 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 00:53:29 - model_training - INFO - 准确率: 0.8750
2025-08-29 00:53:29 - model_training - INFO - AUC: 0.9642
2025-08-29 00:53:29 - model_training - INFO - AUPRC: 0.9582
2025-08-29 00:53:29 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:29 - model_training - INFO - 
[[20  3]
 [ 2 15]]
2025-08-29 00:53:29 - model_training - INFO - 
分类报告:
2025-08-29 00:53:30 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.87      0.89        23
           1       0.83      0.88      0.86        17

    accuracy                           0.88        40
   macro avg       0.87      0.88      0.87        40
weighted avg       0.88      0.88      0.88        40

2025-08-29 00:53:30 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 00:53:30 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8750
2025-08-29 00:53:30 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\Logistic_single_005330.joblib
2025-08-29 00:53:30 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 00:53:30 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 00:53:30 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 00:53:30 - model_training - INFO - 模型名称: SVM
2025-08-29 00:53:30 - model_training - INFO - 准确率: 0.8500
2025-08-29 00:53:30 - model_training - INFO - AUC: 0.9514
2025-08-29 00:53:30 - model_training - INFO - AUPRC: 0.9405
2025-08-29 00:53:30 - model_training - INFO - 混淆矩阵:
2025-08-29 00:53:30 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 00:53:30 - model_training - INFO - 
分类报告:
2025-08-29 00:53:30 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 00:53:30 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 00:53:30 - model_training - INFO - 模型 SVM 性能: 准确率=0.8500
2025-08-29 00:53:30 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_005311\models\SVM_single_005330.joblib
2025-08-29 00:53:30 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 00:53:30 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 00:53:30 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 01:48:01 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 01:48:01 - model_training - INFO - 准确率: 0.8500
2025-08-29 01:48:01 - model_training - INFO - AUC: 0.9322
2025-08-29 01:48:01 - model_training - INFO - AUPRC: 0.8634
2025-08-29 01:48:01 - model_training - INFO - 混淆矩阵:
2025-08-29 01:48:01 - model_training - INFO - 
[[22  1]
 [ 5 12]]
2025-08-29 01:48:01 - model_training - INFO - 
分类报告:
2025-08-29 01:48:01 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.81      0.96      0.88        23
           1       0.92      0.71      0.80        17

    accuracy                           0.85        40
   macro avg       0.87      0.83      0.84        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 01:48:01 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 01:48:01 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8500
2025-08-29 01:48:01 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\DecisionTree_single_014801.joblib
2025-08-29 01:48:01 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 01:48:02 - model_training - INFO - 模型名称: Random Forest
2025-08-29 01:48:02 - model_training - INFO - 准确率: 0.8250
2025-08-29 01:48:02 - model_training - INFO - AUC: 0.9540
2025-08-29 01:48:02 - model_training - INFO - AUPRC: 0.9324
2025-08-29 01:48:02 - model_training - INFO - 混淆矩阵:
2025-08-29 01:48:02 - model_training - INFO - 
[[21  2]
 [ 5 12]]
2025-08-29 01:48:02 - model_training - INFO - 
分类报告:
2025-08-29 01:48:02 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.81      0.91      0.86        23
           1       0.86      0.71      0.77        17

    accuracy                           0.82        40
   macro avg       0.83      0.81      0.82        40
weighted avg       0.83      0.82      0.82        40

2025-08-29 01:48:02 - model_training - INFO - 训练时间: 0.27 秒
2025-08-29 01:48:02 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8250
2025-08-29 01:48:02 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\RandomForest_single_014802.joblib
2025-08-29 01:48:02 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 01:48:02 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 01:48:02 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 01:48:02 - model_training - INFO - 模型名称: XGBoost
2025-08-29 01:48:02 - model_training - INFO - 准确率: 0.9000
2025-08-29 01:48:02 - model_training - INFO - AUC: 0.9616
2025-08-29 01:48:02 - model_training - INFO - AUPRC: 0.9397
2025-08-29 01:48:02 - model_training - INFO - 混淆矩阵:
2025-08-29 01:48:02 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-29 01:48:02 - model_training - INFO - 
分类报告:
2025-08-29 01:48:02 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-29 01:48:02 - model_training - INFO - 训练时间: 0.16 秒
2025-08-29 01:48:02 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-29 01:48:02 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\XGBoost_single_014802.joblib
2025-08-29 01:48:02 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 01:48:02 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 01:48:02 - model_training - INFO - 模型名称: LightGBM
2025-08-29 01:48:02 - model_training - INFO - 准确率: 0.9000
2025-08-29 01:48:02 - model_training - INFO - AUC: 0.9591
2025-08-29 01:48:02 - model_training - INFO - AUPRC: 0.9353
2025-08-29 01:48:02 - model_training - INFO - 混淆矩阵:
2025-08-29 01:48:02 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-29 01:48:02 - model_training - INFO - 
分类报告:
2025-08-29 01:48:02 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-29 01:48:02 - model_training - INFO - 训练时间: 0.14 秒
2025-08-29 01:48:02 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.9000
2025-08-29 01:48:02 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\LightGBM_single_014802.joblib
2025-08-29 01:48:02 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 01:48:02 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 01:48:02 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 01:48:03 - model_training - INFO - 模型名称: CatBoost
2025-08-29 01:48:03 - model_training - INFO - 准确率: 0.9000
2025-08-29 01:48:03 - model_training - INFO - AUC: 0.9642
2025-08-29 01:48:03 - model_training - INFO - AUPRC: 0.9462
2025-08-29 01:48:03 - model_training - INFO - 混淆矩阵:
2025-08-29 01:48:03 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-29 01:48:03 - model_training - INFO - 
分类报告:
2025-08-29 01:48:03 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-29 01:48:03 - model_training - INFO - 训练时间: 0.79 秒
2025-08-29 01:48:03 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.9000
2025-08-29 01:48:03 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\CatBoost_single_014803.joblib
2025-08-29 01:48:03 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 01:48:03 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 01:48:03 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 01:48:04 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 01:48:04 - model_training - INFO - 准确率: 0.9250
2025-08-29 01:48:04 - model_training - INFO - AUC: 0.9693
2025-08-29 01:48:04 - model_training - INFO - AUPRC: 0.9655
2025-08-29 01:48:04 - model_training - INFO - 混淆矩阵:
2025-08-29 01:48:04 - model_training - INFO - 
[[21  2]
 [ 1 16]]
2025-08-29 01:48:04 - model_training - INFO - 
分类报告:
2025-08-29 01:48:04 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.91      0.93        23
           1       0.89      0.94      0.91        17

    accuracy                           0.93        40
   macro avg       0.92      0.93      0.92        40
weighted avg       0.93      0.93      0.93        40

2025-08-29 01:48:04 - model_training - INFO - 训练时间: 1.26 秒
2025-08-29 01:48:04 - model_training - INFO - 模型 Logistic 性能: 准确率=0.9250
2025-08-29 01:48:04 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\Logistic_single_014804.joblib
2025-08-29 01:48:04 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 01:48:04 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 01:48:04 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 01:48:04 - model_training - INFO - 模型名称: SVM
2025-08-29 01:48:04 - model_training - INFO - 准确率: 0.9000
2025-08-29 01:48:04 - model_training - INFO - AUC: 0.9744
2025-08-29 01:48:04 - model_training - INFO - AUPRC: 0.9746
2025-08-29 01:48:04 - model_training - INFO - 混淆矩阵:
2025-08-29 01:48:04 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-29 01:48:04 - model_training - INFO - 
分类报告:
2025-08-29 01:48:04 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-29 01:48:04 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 01:48:04 - model_training - INFO - 模型 SVM 性能: 准确率=0.9000
2025-08-29 01:48:04 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\SVM_single_014804.joblib
2025-08-29 01:48:04 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 01:48:04 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 01:48:04 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 01:48:04 - model_training - INFO - 模型名称: KNN
2025-08-29 01:48:05 - model_training - INFO - 准确率: 0.8250
2025-08-29 01:48:05 - model_training - INFO - AUC: 0.9437
2025-08-29 01:48:05 - model_training - INFO - AUPRC: 0.9274
2025-08-29 01:48:05 - model_training - INFO - 混淆矩阵:
2025-08-29 01:48:05 - model_training - INFO - 
[[22  1]
 [ 6 11]]
2025-08-29 01:48:05 - model_training - INFO - 
分类报告:
2025-08-29 01:48:05 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.96      0.86        23
           1       0.92      0.65      0.76        17

    accuracy                           0.82        40
   macro avg       0.85      0.80      0.81        40
weighted avg       0.84      0.82      0.82        40

2025-08-29 01:48:05 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 01:48:05 - model_training - INFO - 模型 KNN 性能: 准确率=0.8250
2025-08-29 01:48:05 - model_training - INFO - 模型 KNN 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\KNN_single_014805.joblib
2025-08-29 01:48:05 - model_training - INFO - 模型 KNN 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\KNN_results.joblib
2025-08-29 01:48:05 - model_training - INFO - 模型名称: Naive Bayes
2025-08-29 01:48:05 - model_training - INFO - 准确率: 0.8000
2025-08-29 01:48:05 - model_training - INFO - AUC: 0.9309
2025-08-29 01:48:05 - model_training - INFO - AUPRC: 0.9145
2025-08-29 01:48:05 - model_training - INFO - 混淆矩阵:
2025-08-29 01:48:05 - model_training - INFO - 
[[21  2]
 [ 6 11]]
2025-08-29 01:48:05 - model_training - INFO - 
分类报告:
2025-08-29 01:48:05 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.91      0.84        23
           1       0.85      0.65      0.73        17

    accuracy                           0.80        40
   macro avg       0.81      0.78      0.79        40
weighted avg       0.81      0.80      0.79        40

2025-08-29 01:48:05 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 01:48:05 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8000
2025-08-29 01:48:05 - model_training - INFO - 模型 NaiveBayes 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\NaiveBayes_single_014805.joblib
2025-08-29 01:48:05 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NaiveBayes_results.joblib
2025-08-29 01:48:05 - model_training - INFO - [NeuralNet] 使用传入的scaler实例
2025-08-29 01:48:05 - model_training - INFO - [NeuralNet] 使用Pipeline进行标准化
2025-08-29 01:48:05 - model_training - INFO - 模型名称: Neural Network
2025-08-29 01:48:05 - model_training - INFO - 准确率: 0.9250
2025-08-29 01:48:05 - model_training - INFO - AUC: 0.9821
2025-08-29 01:48:05 - model_training - INFO - AUPRC: 0.9812
2025-08-29 01:48:05 - model_training - INFO - 混淆矩阵:
2025-08-29 01:48:05 - model_training - INFO - 
[[21  2]
 [ 1 16]]
2025-08-29 01:48:05 - model_training - INFO - 
分类报告:
2025-08-29 01:48:05 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.91      0.93        23
           1       0.89      0.94      0.91        17

    accuracy                           0.93        40
   macro avg       0.92      0.93      0.92        40
weighted avg       0.93      0.93      0.93        40

2025-08-29 01:48:05 - model_training - INFO - 训练时间: 0.23 秒
2025-08-29 01:48:05 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.9250
2025-08-29 01:48:05 - model_training - INFO - 模型 NeuralNet 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\NeuralNet_single_014805.joblib
2025-08-29 01:48:05 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NeuralNet_results.joblib
2025-08-29 02:00:39 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 02:00:39 - model_training - INFO - 准确率: 0.7250
2025-08-29 02:00:39 - model_training - INFO - AUC: 0.8210
2025-08-29 02:00:39 - model_training - INFO - AUPRC: 0.6807
2025-08-29 02:00:39 - model_training - INFO - 混淆矩阵:
2025-08-29 02:00:39 - model_training - INFO - 
[[15  8]
 [ 3 14]]
2025-08-29 02:00:39 - model_training - INFO - 
分类报告:
2025-08-29 02:00:39 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-29 02:00:39 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 02:00:39 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-29 02:00:39 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\DecisionTree_single_020039.joblib
2025-08-29 02:00:39 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 02:00:40 - model_training - INFO - 模型名称: Random Forest
2025-08-29 02:00:40 - model_training - INFO - 准确率: 0.8500
2025-08-29 02:00:40 - model_training - INFO - AUC: 0.9501
2025-08-29 02:00:40 - model_training - INFO - AUPRC: 0.9410
2025-08-29 02:00:40 - model_training - INFO - 混淆矩阵:
2025-08-29 02:00:40 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 02:00:40 - model_training - INFO - 
分类报告:
2025-08-29 02:00:40 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 02:00:40 - model_training - INFO - 训练时间: 0.19 秒
2025-08-29 02:00:40 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 02:00:40 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\RandomForest_single_020040.joblib
2025-08-29 02:00:40 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 02:00:40 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 02:00:40 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 02:00:41 - model_training - INFO - 模型名称: XGBoost
2025-08-29 02:00:41 - model_training - INFO - 准确率: 0.8250
2025-08-29 02:00:41 - model_training - INFO - AUC: 0.9437
2025-08-29 02:00:41 - model_training - INFO - AUPRC: 0.9387
2025-08-29 02:00:41 - model_training - INFO - 混淆矩阵:
2025-08-29 02:00:41 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-29 02:00:41 - model_training - INFO - 
分类报告:
2025-08-29 02:00:41 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 02:00:41 - model_training - INFO - 训练时间: 0.17 秒
2025-08-29 02:00:41 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-29 02:00:41 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\XGBoost_single_020041.joblib
2025-08-29 02:00:41 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 02:00:41 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 02:00:41 - model_training - INFO - 模型名称: LightGBM
2025-08-29 02:00:41 - model_training - INFO - 准确率: 0.8750
2025-08-29 02:00:41 - model_training - INFO - AUC: 0.9642
2025-08-29 02:00:41 - model_training - INFO - AUPRC: 0.9541
2025-08-29 02:00:41 - model_training - INFO - 混淆矩阵:
2025-08-29 02:00:41 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-29 02:00:41 - model_training - INFO - 
分类报告:
2025-08-29 02:00:41 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 02:00:41 - model_training - INFO - 训练时间: 0.13 秒
2025-08-29 02:00:41 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 02:00:41 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\LightGBM_single_020041.joblib
2025-08-29 02:00:41 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 02:00:42 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 02:00:42 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 02:01:02 - model_training - INFO - 模型名称: CatBoost
2025-08-29 02:01:02 - model_training - INFO - 准确率: 0.8500
2025-08-29 02:01:02 - model_training - INFO - AUC: 0.9668
2025-08-29 02:01:02 - model_training - INFO - AUPRC: 0.9616
2025-08-29 02:01:02 - model_training - INFO - 混淆矩阵:
2025-08-29 02:01:02 - model_training - INFO - 
[[18  5]
 [ 1 16]]
2025-08-29 02:01:02 - model_training - INFO - 
分类报告:
2025-08-29 02:01:02 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 02:01:02 - model_training - INFO - 训练时间: 19.80 秒
2025-08-29 02:01:02 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8500
2025-08-29 02:01:02 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\CatBoost_single_020102.joblib
2025-08-29 02:01:02 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 02:01:02 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 02:01:02 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 02:01:02 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 02:01:02 - model_training - INFO - 准确率: 0.7750
2025-08-29 02:01:02 - model_training - INFO - AUC: 0.9028
2025-08-29 02:01:02 - model_training - INFO - AUPRC: 0.8685
2025-08-29 02:01:02 - model_training - INFO - 混淆矩阵:
2025-08-29 02:01:02 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-29 02:01:02 - model_training - INFO - 
分类报告:
2025-08-29 02:01:02 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 02:01:02 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 02:01:02 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 02:01:02 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\Logistic_single_020102.joblib
2025-08-29 02:01:02 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 02:01:03 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 02:01:03 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 02:01:03 - model_training - INFO - 模型名称: SVM
2025-08-29 02:01:03 - model_training - INFO - 准确率: 0.8250
2025-08-29 02:01:03 - model_training - INFO - AUC: 0.9258
2025-08-29 02:01:03 - model_training - INFO - AUPRC: 0.9259
2025-08-29 02:01:03 - model_training - INFO - 混淆矩阵:
2025-08-29 02:01:03 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-29 02:01:03 - model_training - INFO - 
分类报告:
2025-08-29 02:01:03 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-29 02:01:03 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 02:01:03 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-29 02:01:03 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\SVM_single_020103.joblib
2025-08-29 02:01:03 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 02:01:03 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 02:01:03 - model_training - INFO - 准确率: 0.7250
2025-08-29 02:01:03 - model_training - INFO - AUC: 0.8210
2025-08-29 02:01:03 - model_training - INFO - AUPRC: 0.6807
2025-08-29 02:01:03 - model_training - INFO - 混淆矩阵:
2025-08-29 02:01:03 - model_training - INFO - 
[[15  8]
 [ 3 14]]
2025-08-29 02:01:03 - model_training - INFO - 
分类报告:
2025-08-29 02:01:04 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-29 02:01:04 - model_training - INFO - 训练时间: 0.16 秒
2025-08-29 02:01:04 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-29 02:01:04 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\DecisionTree_single_020104.joblib
2025-08-29 02:01:04 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 02:01:04 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 02:01:04 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 02:01:05 - model_training - INFO - 模型名称: Random Forest
2025-08-29 02:01:05 - model_training - INFO - 准确率: 0.8500
2025-08-29 02:01:05 - model_training - INFO - AUC: 0.9501
2025-08-29 02:01:05 - model_training - INFO - AUPRC: 0.9410
2025-08-29 02:01:05 - model_training - INFO - 混淆矩阵:
2025-08-29 02:01:05 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 02:01:05 - model_training - INFO - 
分类报告:
2025-08-29 02:01:05 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 02:01:05 - model_training - INFO - 训练时间: 0.20 秒
2025-08-29 02:01:05 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 02:01:05 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\RandomForest_single_020105.joblib
2025-08-29 02:01:05 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 02:01:06 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 02:01:06 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 02:01:06 - model_training - INFO - 模型名称: XGBoost
2025-08-29 02:01:06 - model_training - INFO - 准确率: 0.8250
2025-08-29 02:01:06 - model_training - INFO - AUC: 0.9437
2025-08-29 02:01:06 - model_training - INFO - AUPRC: 0.9387
2025-08-29 02:01:06 - model_training - INFO - 混淆矩阵:
2025-08-29 02:01:06 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-29 02:01:06 - model_training - INFO - 
分类报告:
2025-08-29 02:01:06 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 02:01:06 - model_training - INFO - 训练时间: 0.15 秒
2025-08-29 02:01:06 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-29 02:01:06 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\XGBoost_single_020106.joblib
2025-08-29 02:01:06 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 02:01:07 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 02:01:07 - model_training - INFO - 模型名称: LightGBM
2025-08-29 02:01:07 - model_training - INFO - 准确率: 0.8750
2025-08-29 02:01:07 - model_training - INFO - AUC: 0.9642
2025-08-29 02:01:07 - model_training - INFO - AUPRC: 0.9541
2025-08-29 02:01:07 - model_training - INFO - 混淆矩阵:
2025-08-29 02:01:07 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-29 02:01:07 - model_training - INFO - 
分类报告:
2025-08-29 02:01:07 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 02:01:07 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 02:01:07 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 02:01:07 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\LightGBM_single_020107.joblib
2025-08-29 02:01:07 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 02:01:07 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 02:01:07 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 02:01:28 - model_training - INFO - 模型名称: CatBoost
2025-08-29 02:01:28 - model_training - INFO - 准确率: 0.8500
2025-08-29 02:01:28 - model_training - INFO - AUC: 0.9668
2025-08-29 02:01:28 - model_training - INFO - AUPRC: 0.9616
2025-08-29 02:01:28 - model_training - INFO - 混淆矩阵:
2025-08-29 02:01:28 - model_training - INFO - 
[[18  5]
 [ 1 16]]
2025-08-29 02:01:28 - model_training - INFO - 
分类报告:
2025-08-29 02:01:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 02:01:28 - model_training - INFO - 训练时间: 20.38 秒
2025-08-29 02:01:28 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8500
2025-08-29 02:01:28 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\CatBoost_single_020128.joblib
2025-08-29 02:01:28 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 02:01:28 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 02:01:28 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 02:01:28 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 02:01:28 - model_training - INFO - 准确率: 0.7750
2025-08-29 02:01:28 - model_training - INFO - AUC: 0.9028
2025-08-29 02:01:28 - model_training - INFO - AUPRC: 0.8685
2025-08-29 02:01:28 - model_training - INFO - 混淆矩阵:
2025-08-29 02:01:28 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-29 02:01:28 - model_training - INFO - 
分类报告:
2025-08-29 02:01:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 02:01:28 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 02:01:28 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 02:01:28 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\Logistic_single_020128.joblib
2025-08-29 02:01:28 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 02:01:29 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 02:01:29 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 02:01:29 - model_training - INFO - 模型名称: SVM
2025-08-29 02:01:29 - model_training - INFO - 准确率: 0.8250
2025-08-29 02:01:29 - model_training - INFO - AUC: 0.9258
2025-08-29 02:01:29 - model_training - INFO - AUPRC: 0.9259
2025-08-29 02:01:29 - model_training - INFO - 混淆矩阵:
2025-08-29 02:01:29 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-29 02:01:29 - model_training - INFO - 
分类报告:
2025-08-29 02:01:29 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-29 02:01:29 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 02:01:29 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-29 02:01:29 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_014801\models\SVM_single_020129.joblib
2025-08-29 02:01:29 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 02:01:30 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 02:01:30 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 02:42:34 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 02:42:34 - model_training - INFO - 准确率: 0.7250
2025-08-29 02:42:34 - model_training - INFO - AUC: 0.8210
2025-08-29 02:42:34 - model_training - INFO - AUPRC: 0.6807
2025-08-29 02:42:34 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:34 - model_training - INFO - 
[[15  8]
 [ 3 14]]
2025-08-29 02:42:34 - model_training - INFO - 
分类报告:
2025-08-29 02:42:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-29 02:42:34 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 02:42:34 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-29 02:42:34 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 02:42:34 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_feature_names.joblib
2025-08-29 02:42:34 - model_training - INFO - 模型名称: Random Forest
2025-08-29 02:42:34 - model_training - INFO - 准确率: 0.8500
2025-08-29 02:42:34 - model_training - INFO - AUC: 0.9501
2025-08-29 02:42:34 - model_training - INFO - AUPRC: 0.9410
2025-08-29 02:42:34 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:34 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 02:42:34 - model_training - INFO - 
分类报告:
2025-08-29 02:42:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 02:42:34 - model_training - INFO - 训练时间: 0.18 秒
2025-08-29 02:42:34 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 02:42:34 - model_training - INFO - 模型 RandomForest 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 02:42:34 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_feature_names.joblib
2025-08-29 02:42:35 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 02:42:35 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 02:42:35 - model_training - INFO - 模型名称: XGBoost
2025-08-29 02:42:35 - model_training - INFO - 准确率: 0.8250
2025-08-29 02:42:35 - model_training - INFO - AUC: 0.9437
2025-08-29 02:42:35 - model_training - INFO - AUPRC: 0.9387
2025-08-29 02:42:35 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:35 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-29 02:42:35 - model_training - INFO - 
分类报告:
2025-08-29 02:42:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 02:42:35 - model_training - INFO - 训练时间: 0.13 秒
2025-08-29 02:42:35 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-29 02:42:35 - model_training - INFO - 模型 XGBoost 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 02:42:35 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_feature_names.joblib
2025-08-29 02:42:36 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 02:42:36 - model_training - INFO - 模型名称: LightGBM
2025-08-29 02:42:36 - model_training - INFO - 准确率: 0.8750
2025-08-29 02:42:36 - model_training - INFO - AUC: 0.9642
2025-08-29 02:42:36 - model_training - INFO - AUPRC: 0.9541
2025-08-29 02:42:36 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:36 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-29 02:42:36 - model_training - INFO - 
分类报告:
2025-08-29 02:42:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 02:42:36 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 02:42:36 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 02:42:36 - model_training - INFO - 模型 LightGBM 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 02:42:36 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_feature_names.joblib
2025-08-29 02:42:36 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 02:42:36 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 02:42:56 - model_training - INFO - 模型名称: CatBoost
2025-08-29 02:42:56 - model_training - INFO - 准确率: 0.8500
2025-08-29 02:42:56 - model_training - INFO - AUC: 0.9668
2025-08-29 02:42:56 - model_training - INFO - AUPRC: 0.9616
2025-08-29 02:42:56 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:56 - model_training - INFO - 
[[18  5]
 [ 1 16]]
2025-08-29 02:42:56 - model_training - INFO - 
分类报告:
2025-08-29 02:42:56 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 02:42:56 - model_training - INFO - 训练时间: 19.46 秒
2025-08-29 02:42:56 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8500
2025-08-29 02:42:56 - model_training - INFO - 模型 CatBoost 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 02:42:56 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_feature_names.joblib
2025-08-29 02:42:57 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 02:42:57 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 02:42:57 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 02:42:57 - model_training - INFO - 准确率: 0.7750
2025-08-29 02:42:57 - model_training - INFO - AUC: 0.9028
2025-08-29 02:42:57 - model_training - INFO - AUPRC: 0.8685
2025-08-29 02:42:57 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:57 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-29 02:42:57 - model_training - INFO - 
分类报告:
2025-08-29 02:42:57 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 02:42:57 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 02:42:57 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 02:42:57 - model_training - INFO - 模型 Logistic 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 02:42:57 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_feature_names.joblib
2025-08-29 02:42:58 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 02:42:58 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 02:42:58 - model_training - INFO - 模型名称: SVM
2025-08-29 02:42:58 - model_training - INFO - 准确率: 0.8250
2025-08-29 02:42:58 - model_training - INFO - AUC: 0.9258
2025-08-29 02:42:58 - model_training - INFO - AUPRC: 0.9259
2025-08-29 02:42:58 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:58 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-29 02:42:58 - model_training - INFO - 
分类报告:
2025-08-29 02:42:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-29 02:42:58 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 02:42:58 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-29 02:42:58 - model_training - INFO - 模型 SVM 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 02:42:58 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\SVM_feature_names.joblib
2025-08-29 02:42:58 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 02:42:58 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 03:12:17 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 03:12:17 - model_training - INFO - 准确率: 0.9000
2025-08-29 03:12:17 - model_training - INFO - AUC: 0.9107
2025-08-29 03:12:17 - model_training - INFO - AUPRC: 0.8655
2025-08-29 03:12:17 - model_training - INFO - 混淆矩阵:
2025-08-29 03:12:17 - model_training - INFO - 
[[11  3]
 [ 0 16]]
2025-08-29 03:12:17 - model_training - INFO - 
分类报告:
2025-08-29 03:12:17 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.79      0.88        14
           1       0.84      1.00      0.91        16

    accuracy                           0.90        30
   macro avg       0.92      0.89      0.90        30
weighted avg       0.92      0.90      0.90        30

2025-08-29 03:12:17 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 03:12:17 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.9000
2025-08-29 03:12:17 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 03:12:17 - model_training - INFO - 特征名称已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_feature_names.joblib
2025-08-29 03:12:18 - model_training - INFO - 模型名称: Random Forest
2025-08-29 03:12:18 - model_training - INFO - 准确率: 0.9333
2025-08-29 03:12:18 - model_training - INFO - AUC: 0.9911
2025-08-29 03:12:18 - model_training - INFO - AUPRC: 0.9924
2025-08-29 03:12:18 - model_training - INFO - 混淆矩阵:
2025-08-29 03:12:18 - model_training - INFO - 
[[12  2]
 [ 0 16]]
2025-08-29 03:12:18 - model_training - INFO - 
分类报告:
2025-08-29 03:12:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.86      0.92        14
           1       0.89      1.00      0.94        16

    accuracy                           0.93        30
   macro avg       0.94      0.93      0.93        30
weighted avg       0.94      0.93      0.93        30

2025-08-29 03:12:18 - model_training - INFO - 训练时间: 0.17 秒
2025-08-29 03:12:18 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9333
2025-08-29 03:12:18 - model_training - INFO - 模型 RandomForest 的结果已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 03:12:18 - model_training - INFO - 特征名称已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\RandomForest_feature_names.joblib
2025-08-29 03:25:32 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 03:25:32 - model_training - INFO - 准确率: 0.8750
2025-08-29 03:25:32 - model_training - INFO - AUC: 0.9540
2025-08-29 03:25:32 - model_training - INFO - AUPRC: 0.9201
2025-08-29 03:25:32 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:32 - model_training - INFO - 
[[20  3]
 [ 2 15]]
2025-08-29 03:25:32 - model_training - INFO - 
分类报告:
2025-08-29 03:25:32 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.87      0.89        23
           1       0.83      0.88      0.86        17

    accuracy                           0.88        40
   macro avg       0.87      0.88      0.87        40
weighted avg       0.88      0.88      0.88        40

2025-08-29 03:25:32 - model_training - INFO - 训练时间: 0.24 秒
2025-08-29 03:25:32 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8750
2025-08-29 03:25:32 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\DecisionTree_single_032532.joblib
2025-08-29 03:25:32 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 03:25:33 - model_training - INFO - 模型名称: Random Forest
2025-08-29 03:25:33 - model_training - INFO - 准确率: 0.9250
2025-08-29 03:25:33 - model_training - INFO - AUC: 0.9923
2025-08-29 03:25:33 - model_training - INFO - AUPRC: 0.9903
2025-08-29 03:25:33 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:33 - model_training - INFO - 
[[21  2]
 [ 1 16]]
2025-08-29 03:25:33 - model_training - INFO - 
分类报告:
2025-08-29 03:25:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.91      0.93        23
           1       0.89      0.94      0.91        17

    accuracy                           0.93        40
   macro avg       0.92      0.93      0.92        40
weighted avg       0.93      0.93      0.93        40

2025-08-29 03:25:33 - model_training - INFO - 训练时间: 0.21 秒
2025-08-29 03:25:33 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9250
2025-08-29 03:25:33 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\RandomForest_single_032533.joblib
2025-08-29 03:25:33 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 03:25:33 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 03:25:33 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 03:25:33 - model_training - INFO - 模型名称: XGBoost
2025-08-29 03:25:33 - model_training - INFO - 准确率: 0.9500
2025-08-29 03:25:33 - model_training - INFO - AUC: 0.9949
2025-08-29 03:25:33 - model_training - INFO - AUPRC: 0.9938
2025-08-29 03:25:33 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:33 - model_training - INFO - 
[[22  1]
 [ 1 16]]
2025-08-29 03:25:33 - model_training - INFO - 
分类报告:
2025-08-29 03:25:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.96      0.96      0.96        23
           1       0.94      0.94      0.94        17

    accuracy                           0.95        40
   macro avg       0.95      0.95      0.95        40
weighted avg       0.95      0.95      0.95        40

2025-08-29 03:25:33 - model_training - INFO - 训练时间: 0.16 秒
2025-08-29 03:25:33 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9500
2025-08-29 03:25:33 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\XGBoost_single_032533.joblib
2025-08-29 03:25:33 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 03:25:33 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 03:25:33 - model_training - INFO - 模型名称: LightGBM
2025-08-29 03:25:33 - model_training - INFO - 准确率: 0.9500
2025-08-29 03:25:33 - model_training - INFO - AUC: 0.9974
2025-08-29 03:25:33 - model_training - INFO - AUPRC: 0.9967
2025-08-29 03:25:33 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:33 - model_training - INFO - 
[[22  1]
 [ 1 16]]
2025-08-29 03:25:33 - model_training - INFO - 
分类报告:
2025-08-29 03:25:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.96      0.96      0.96        23
           1       0.94      0.94      0.94        17

    accuracy                           0.95        40
   macro avg       0.95      0.95      0.95        40
weighted avg       0.95      0.95      0.95        40

2025-08-29 03:25:33 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 03:25:33 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.9500
2025-08-29 03:25:33 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\LightGBM_single_032533.joblib
2025-08-29 03:25:33 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 03:25:33 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 03:25:33 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 03:25:34 - model_training - INFO - 模型名称: CatBoost
2025-08-29 03:25:34 - model_training - INFO - 准确率: 0.9250
2025-08-29 03:25:34 - model_training - INFO - AUC: 0.9949
2025-08-29 03:25:34 - model_training - INFO - AUPRC: 0.9938
2025-08-29 03:25:34 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:34 - model_training - INFO - 
[[21  2]
 [ 1 16]]
2025-08-29 03:25:34 - model_training - INFO - 
分类报告:
2025-08-29 03:25:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.91      0.93        23
           1       0.89      0.94      0.91        17

    accuracy                           0.93        40
   macro avg       0.92      0.93      0.92        40
weighted avg       0.93      0.93      0.93        40

2025-08-29 03:25:34 - model_training - INFO - 训练时间: 0.65 秒
2025-08-29 03:25:34 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.9250
2025-08-29 03:25:34 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\CatBoost_single_032534.joblib
2025-08-29 03:25:34 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 03:25:34 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 03:25:34 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 03:25:35 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 03:25:35 - model_training - INFO - 准确率: 0.9500
2025-08-29 03:25:35 - model_training - INFO - AUC: 0.9872
2025-08-29 03:25:35 - model_training - INFO - AUPRC: 0.9840
2025-08-29 03:25:35 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:35 - model_training - INFO - 
[[22  1]
 [ 1 16]]
2025-08-29 03:25:35 - model_training - INFO - 
分类报告:
2025-08-29 03:25:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.96      0.96      0.96        23
           1       0.94      0.94      0.94        17

    accuracy                           0.95        40
   macro avg       0.95      0.95      0.95        40
weighted avg       0.95      0.95      0.95        40

2025-08-29 03:25:35 - model_training - INFO - 训练时间: 1.40 秒
2025-08-29 03:25:35 - model_training - INFO - 模型 Logistic 性能: 准确率=0.9500
2025-08-29 03:25:35 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\Logistic_single_032535.joblib
2025-08-29 03:25:35 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 03:25:35 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 03:25:35 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 03:25:35 - model_training - INFO - 模型名称: SVM
2025-08-29 03:25:35 - model_training - INFO - 准确率: 0.9000
2025-08-29 03:25:35 - model_training - INFO - AUC: 0.9821
2025-08-29 03:25:35 - model_training - INFO - AUPRC: 0.9770
2025-08-29 03:25:35 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:35 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-29 03:25:35 - model_training - INFO - 
分类报告:
2025-08-29 03:25:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-29 03:25:35 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 03:25:35 - model_training - INFO - 模型 SVM 性能: 准确率=0.9000
2025-08-29 03:25:35 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\SVM_single_032535.joblib
2025-08-29 03:25:35 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 03:25:35 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 03:25:35 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 03:25:35 - model_training - INFO - 模型名称: KNN
2025-08-29 03:25:35 - model_training - INFO - 准确率: 0.9000
2025-08-29 03:25:35 - model_training - INFO - AUC: 0.9847
2025-08-29 03:25:35 - model_training - INFO - AUPRC: 0.9804
2025-08-29 03:25:35 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:35 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-29 03:25:35 - model_training - INFO - 
分类报告:
2025-08-29 03:25:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-29 03:25:35 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 03:25:35 - model_training - INFO - 模型 KNN 性能: 准确率=0.9000
2025-08-29 03:25:35 - model_training - INFO - 模型 KNN 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\KNN_single_032535.joblib
2025-08-29 03:25:35 - model_training - INFO - 模型 KNN 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\KNN_results.joblib
2025-08-29 03:25:35 - model_training - INFO - 模型名称: Naive Bayes
2025-08-29 03:25:35 - model_training - INFO - 准确率: 0.8500
2025-08-29 03:25:35 - model_training - INFO - AUC: 0.9540
2025-08-29 03:25:35 - model_training - INFO - AUPRC: 0.9376
2025-08-29 03:25:35 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:35 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-08-29 03:25:35 - model_training - INFO - 
分类报告:
2025-08-29 03:25:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-08-29 03:25:35 - model_training - INFO - 训练时间: 0.10 秒
2025-08-29 03:25:35 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8500
2025-08-29 03:25:35 - model_training - INFO - 模型 NaiveBayes 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\NaiveBayes_single_032535.joblib
2025-08-29 03:25:35 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NaiveBayes_results.joblib
2025-08-29 03:25:36 - model_training - INFO - [NeuralNet] 使用传入的scaler实例
2025-08-29 03:25:36 - model_training - INFO - [NeuralNet] 使用Pipeline进行标准化
2025-08-29 03:25:36 - model_training - INFO - 模型名称: Neural Network
2025-08-29 03:25:36 - model_training - INFO - 准确率: 0.8750
2025-08-29 03:25:36 - model_training - INFO - AUC: 0.9847
2025-08-29 03:25:36 - model_training - INFO - AUPRC: 0.9769
2025-08-29 03:25:36 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:36 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-29 03:25:36 - model_training - INFO - 
分类报告:
2025-08-29 03:25:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-29 03:25:36 - model_training - INFO - 训练时间: 0.23 秒
2025-08-29 03:25:36 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-29 03:25:36 - model_training - INFO - 模型 NeuralNet 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\NeuralNet_single_032536.joblib
2025-08-29 03:25:36 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NeuralNet_results.joblib
2025-08-29 03:28:30 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 03:28:30 - model_training - INFO - 准确率: 0.7250
2025-08-29 03:28:30 - model_training - INFO - AUC: 0.8210
2025-08-29 03:28:30 - model_training - INFO - AUPRC: 0.6807
2025-08-29 03:28:30 - model_training - INFO - 混淆矩阵:
2025-08-29 03:28:30 - model_training - INFO - 
[[15  8]
 [ 3 14]]
2025-08-29 03:28:30 - model_training - INFO - 
分类报告:
2025-08-29 03:28:30 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-29 03:28:30 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 03:28:30 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-29 03:28:30 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\DecisionTree_single_032830.joblib
2025-08-29 03:28:30 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 03:28:30 - model_training - INFO - 模型名称: Random Forest
2025-08-29 03:28:30 - model_training - INFO - 准确率: 0.8500
2025-08-29 03:28:30 - model_training - INFO - AUC: 0.9501
2025-08-29 03:28:30 - model_training - INFO - AUPRC: 0.9410
2025-08-29 03:28:30 - model_training - INFO - 混淆矩阵:
2025-08-29 03:28:30 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 03:28:30 - model_training - INFO - 
分类报告:
2025-08-29 03:28:30 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 03:28:30 - model_training - INFO - 训练时间: 0.18 秒
2025-08-29 03:28:30 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 03:28:30 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\RandomForest_single_032830.joblib
2025-08-29 03:28:30 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 03:28:31 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 03:28:31 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 03:28:31 - model_training - INFO - 模型名称: XGBoost
2025-08-29 03:28:31 - model_training - INFO - 准确率: 0.8250
2025-08-29 03:28:31 - model_training - INFO - AUC: 0.9437
2025-08-29 03:28:31 - model_training - INFO - AUPRC: 0.9387
2025-08-29 03:28:31 - model_training - INFO - 混淆矩阵:
2025-08-29 03:28:31 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-29 03:28:31 - model_training - INFO - 
分类报告:
2025-08-29 03:28:31 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 03:28:31 - model_training - INFO - 训练时间: 0.13 秒
2025-08-29 03:28:31 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-29 03:28:31 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\XGBoost_single_032831.joblib
2025-08-29 03:28:31 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 03:28:32 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 03:28:32 - model_training - INFO - 模型名称: LightGBM
2025-08-29 03:28:32 - model_training - INFO - 准确率: 0.8750
2025-08-29 03:28:32 - model_training - INFO - AUC: 0.9642
2025-08-29 03:28:32 - model_training - INFO - AUPRC: 0.9541
2025-08-29 03:28:32 - model_training - INFO - 混淆矩阵:
2025-08-29 03:28:32 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-29 03:28:32 - model_training - INFO - 
分类报告:
2025-08-29 03:28:32 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 03:28:32 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 03:28:32 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 03:28:32 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\LightGBM_single_032832.joblib
2025-08-29 03:28:32 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 03:28:33 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 03:28:33 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 03:28:53 - model_training - INFO - 模型名称: CatBoost
2025-08-29 03:28:53 - model_training - INFO - 准确率: 0.8500
2025-08-29 03:28:53 - model_training - INFO - AUC: 0.9668
2025-08-29 03:28:53 - model_training - INFO - AUPRC: 0.9616
2025-08-29 03:28:53 - model_training - INFO - 混淆矩阵:
2025-08-29 03:28:53 - model_training - INFO - 
[[18  5]
 [ 1 16]]
2025-08-29 03:28:53 - model_training - INFO - 
分类报告:
2025-08-29 03:28:53 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 03:28:53 - model_training - INFO - 训练时间: 20.78 秒
2025-08-29 03:28:53 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8500
2025-08-29 03:28:53 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\CatBoost_single_032853.joblib
2025-08-29 03:28:53 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 03:28:54 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 03:28:54 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 03:28:54 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 03:28:54 - model_training - INFO - 准确率: 0.7750
2025-08-29 03:28:54 - model_training - INFO - AUC: 0.9028
2025-08-29 03:28:54 - model_training - INFO - AUPRC: 0.8685
2025-08-29 03:28:54 - model_training - INFO - 混淆矩阵:
2025-08-29 03:28:54 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-29 03:28:54 - model_training - INFO - 
分类报告:
2025-08-29 03:28:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 03:28:54 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 03:28:54 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 03:28:54 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\Logistic_single_032854.joblib
2025-08-29 03:28:54 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 03:28:55 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 03:28:55 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 03:28:55 - model_training - INFO - 模型名称: SVM
2025-08-29 03:28:55 - model_training - INFO - 准确率: 0.8250
2025-08-29 03:28:55 - model_training - INFO - AUC: 0.9258
2025-08-29 03:28:55 - model_training - INFO - AUPRC: 0.9259
2025-08-29 03:28:55 - model_training - INFO - 混淆矩阵:
2025-08-29 03:28:55 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-29 03:28:55 - model_training - INFO - 
分类报告:
2025-08-29 03:28:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-29 03:28:55 - model_training - INFO - 训练时间: 0.13 秒
2025-08-29 03:28:55 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-29 03:28:55 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\SVM_single_032855.joblib
2025-08-29 03:28:55 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 03:28:56 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 03:28:56 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 03:34:44 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 03:34:44 - model_training - INFO - 准确率: 0.8250
2025-08-29 03:34:44 - model_training - INFO - AUC: 0.8646
2025-08-29 03:34:44 - model_training - INFO - AUPRC: 0.9087
2025-08-29 03:34:44 - model_training - INFO - 混淆矩阵:
2025-08-29 03:34:44 - model_training - INFO - 
[[14  2]
 [ 5 19]]
2025-08-29 03:34:44 - model_training - INFO - 
分类报告:
2025-08-29 03:34:44 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.74      0.88      0.80        16
           1       0.90      0.79      0.84        24

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 03:34:44 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 03:34:44 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8250
2025-08-29 03:34:44 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 03:34:44 - model_training - INFO - 特征名称已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_feature_names.joblib
2025-08-29 03:34:45 - model_training - INFO - 模型名称: Random Forest
2025-08-29 03:34:45 - model_training - INFO - 准确率: 0.8500
2025-08-29 03:34:45 - model_training - INFO - AUC: 0.9570
2025-08-29 03:34:45 - model_training - INFO - AUPRC: 0.9728
2025-08-29 03:34:45 - model_training - INFO - 混淆矩阵:
2025-08-29 03:34:45 - model_training - INFO - 
[[15  1]
 [ 5 19]]
2025-08-29 03:34:45 - model_training - INFO - 
分类报告:
2025-08-29 03:34:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.75      0.94      0.83        16
           1       0.95      0.79      0.86        24

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 03:34:45 - model_training - INFO - 训练时间: 0.19 秒
2025-08-29 03:34:45 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 03:34:45 - model_training - INFO - 模型 RandomForest 的结果已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 03:34:45 - model_training - INFO - 特征名称已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\RandomForest_feature_names.joblib
2025-08-29 03:34:45 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 03:34:45 - model_training - INFO - 模型名称: KNN
2025-08-29 03:34:45 - model_training - INFO - 准确率: 0.7500
2025-08-29 03:34:45 - model_training - INFO - AUC: 0.8490
2025-08-29 03:34:45 - model_training - INFO - AUPRC: 0.8751
2025-08-29 03:34:45 - model_training - INFO - 混淆矩阵:
2025-08-29 03:34:45 - model_training - INFO - 
[[13  3]
 [ 7 17]]
2025-08-29 03:34:45 - model_training - INFO - 
分类报告:
2025-08-29 03:34:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.65      0.81      0.72        16
           1       0.85      0.71      0.77        24

    accuracy                           0.75        40
   macro avg       0.75      0.76      0.75        40
weighted avg       0.77      0.75      0.75        40

2025-08-29 03:34:45 - model_training - INFO - 训练时间: 0.10 秒
2025-08-29 03:34:45 - model_training - INFO - 模型 KNN 性能: 准确率=0.7500
2025-08-29 03:34:45 - model_training - INFO - 模型 KNN 的结果已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\KNN_results.joblib
2025-08-29 03:34:45 - model_training - INFO - 特征名称已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\KNN_feature_names.joblib
2025-08-29 03:34:46 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 03:34:46 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 03:34:46 - model_training - INFO - 准确率: 0.7500
2025-08-29 03:34:46 - model_training - INFO - AUC: 0.8854
2025-08-29 03:34:46 - model_training - INFO - AUPRC: 0.9181
2025-08-29 03:34:46 - model_training - INFO - 混淆矩阵:
2025-08-29 03:34:46 - model_training - INFO - 
[[13  3]
 [ 7 17]]
2025-08-29 03:34:46 - model_training - INFO - 
分类报告:
2025-08-29 03:34:46 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.65      0.81      0.72        16
           1       0.85      0.71      0.77        24

    accuracy                           0.75        40
   macro avg       0.75      0.76      0.75        40
weighted avg       0.77      0.75      0.75        40

2025-08-29 03:34:46 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 03:34:46 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7500
2025-08-29 03:34:46 - model_training - INFO - 模型 Logistic 的结果已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 03:34:46 - model_training - INFO - 特征名称已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\Logistic_feature_names.joblib
2025-08-29 04:24:25 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 04:24:25 - model_training - INFO - 准确率: 0.7500
2025-08-29 04:24:25 - model_training - INFO - AUC: 0.8133
2025-08-29 04:24:25 - model_training - INFO - AUPRC: 0.6690
2025-08-29 04:24:25 - model_training - INFO - 混淆矩阵:
2025-08-29 04:24:25 - model_training - INFO - 
[[16  7]
 [ 3 14]]
2025-08-29 04:24:25 - model_training - INFO - 
分类报告:
2025-08-29 04:24:25 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.70      0.76        23
           1       0.67      0.82      0.74        17

    accuracy                           0.75        40
   macro avg       0.75      0.76      0.75        40
weighted avg       0.77      0.75      0.75        40

2025-08-29 04:24:25 - model_training - INFO - 训练时间: 0.10 秒
2025-08-29 04:24:25 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7500
2025-08-29 04:24:25 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\DecisionTree_single_042425.joblib
2025-08-29 04:24:25 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 04:24:26 - model_training - INFO - 模型名称: Random Forest
2025-08-29 04:24:26 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:24:26 - model_training - INFO - AUC: 0.9514
2025-08-29 04:24:26 - model_training - INFO - AUPRC: 0.9454
2025-08-29 04:24:26 - model_training - INFO - 混淆矩阵:
2025-08-29 04:24:26 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-29 04:24:26 - model_training - INFO - 
分类报告:
2025-08-29 04:24:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 04:24:26 - model_training - INFO - 训练时间: 0.22 秒
2025-08-29 04:24:26 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8250
2025-08-29 04:24:26 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\RandomForest_single_042426.joblib
2025-08-29 04:24:26 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 04:24:26 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 04:24:26 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:24:26 - model_training - INFO - 模型名称: XGBoost
2025-08-29 04:24:26 - model_training - INFO - 准确率: 0.8000
2025-08-29 04:24:26 - model_training - INFO - AUC: 0.9540
2025-08-29 04:24:26 - model_training - INFO - AUPRC: 0.9500
2025-08-29 04:24:26 - model_training - INFO - 混淆矩阵:
2025-08-29 04:24:26 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-29 04:24:26 - model_training - INFO - 
分类报告:
2025-08-29 04:24:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-29 04:24:26 - model_training - INFO - 训练时间: 0.19 秒
2025-08-29 04:24:26 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8000
2025-08-29 04:24:26 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\XGBoost_single_042426.joblib
2025-08-29 04:24:26 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 04:24:26 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:24:26 - model_training - INFO - 模型名称: LightGBM
2025-08-29 04:24:26 - model_training - INFO - 准确率: 0.8750
2025-08-29 04:24:26 - model_training - INFO - AUC: 0.9616
2025-08-29 04:24:26 - model_training - INFO - AUPRC: 0.9517
2025-08-29 04:24:26 - model_training - INFO - 混淆矩阵:
2025-08-29 04:24:26 - model_training - INFO - 
[[20  3]
 [ 2 15]]
2025-08-29 04:24:26 - model_training - INFO - 
分类报告:
2025-08-29 04:24:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.87      0.89        23
           1       0.83      0.88      0.86        17

    accuracy                           0.88        40
   macro avg       0.87      0.88      0.87        40
weighted avg       0.88      0.88      0.88        40

2025-08-29 04:24:26 - model_training - INFO - 训练时间: 0.13 秒
2025-08-29 04:24:26 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 04:24:26 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\LightGBM_single_042426.joblib
2025-08-29 04:24:26 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 04:24:26 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 04:24:26 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 04:24:27 - model_training - INFO - 模型名称: CatBoost
2025-08-29 04:24:27 - model_training - INFO - 准确率: 0.8750
2025-08-29 04:24:27 - model_training - INFO - AUC: 0.9565
2025-08-29 04:24:27 - model_training - INFO - AUPRC: 0.9455
2025-08-29 04:24:27 - model_training - INFO - 混淆矩阵:
2025-08-29 04:24:27 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-29 04:24:27 - model_training - INFO - 
分类报告:
2025-08-29 04:24:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 04:24:27 - model_training - INFO - 训练时间: 0.77 秒
2025-08-29 04:24:27 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8750
2025-08-29 04:24:27 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\CatBoost_single_042427.joblib
2025-08-29 04:24:27 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 04:24:27 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 04:24:27 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 04:24:27 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 04:24:27 - model_training - INFO - 准确率: 0.7750
2025-08-29 04:24:27 - model_training - INFO - AUC: 0.9028
2025-08-29 04:24:27 - model_training - INFO - AUPRC: 0.8685
2025-08-29 04:24:27 - model_training - INFO - 混淆矩阵:
2025-08-29 04:24:27 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-29 04:24:27 - model_training - INFO - 
分类报告:
2025-08-29 04:24:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 04:24:27 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 04:24:27 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 04:24:27 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\Logistic_single_042427.joblib
2025-08-29 04:24:27 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 04:24:27 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 04:24:27 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 04:24:27 - model_training - INFO - 模型名称: SVM
2025-08-29 04:24:27 - model_training - INFO - 准确率: 0.7500
2025-08-29 04:24:27 - model_training - INFO - AUC: 0.8951
2025-08-29 04:24:27 - model_training - INFO - AUPRC: 0.8984
2025-08-29 04:24:27 - model_training - INFO - 混淆矩阵:
2025-08-29 04:24:27 - model_training - INFO - 
[[16  7]
 [ 3 14]]
2025-08-29 04:24:27 - model_training - INFO - 
分类报告:
2025-08-29 04:24:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.70      0.76        23
           1       0.67      0.82      0.74        17

    accuracy                           0.75        40
   macro avg       0.75      0.76      0.75        40
weighted avg       0.77      0.75      0.75        40

2025-08-29 04:24:27 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 04:24:27 - model_training - INFO - 模型 SVM 性能: 准确率=0.7500
2025-08-29 04:24:27 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\SVM_single_042427.joblib
2025-08-29 04:24:27 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 04:24:27 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 04:24:27 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 04:24:27 - model_training - INFO - 模型名称: KNN
2025-08-29 04:24:27 - model_training - INFO - 准确率: 0.7750
2025-08-29 04:24:27 - model_training - INFO - AUC: 0.8977
2025-08-29 04:24:27 - model_training - INFO - AUPRC: 0.8959
2025-08-29 04:24:27 - model_training - INFO - 混淆矩阵:
2025-08-29 04:24:27 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-29 04:24:27 - model_training - INFO - 
分类报告:
2025-08-29 04:24:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-29 04:24:27 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 04:24:27 - model_training - INFO - 模型 KNN 性能: 准确率=0.7750
2025-08-29 04:24:27 - model_training - INFO - 模型 KNN 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\KNN_single_042427.joblib
2025-08-29 04:24:27 - model_training - INFO - 模型 KNN 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\KNN_results.joblib
2025-08-29 04:24:27 - model_training - INFO - 模型名称: Naive Bayes
2025-08-29 04:24:27 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:24:27 - model_training - INFO - AUC: 0.9156
2025-08-29 04:24:27 - model_training - INFO - AUPRC: 0.8954
2025-08-29 04:24:27 - model_training - INFO - 混淆矩阵:
2025-08-29 04:24:27 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-29 04:24:27 - model_training - INFO - 
分类报告:
2025-08-29 04:24:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-29 04:24:27 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 04:24:27 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8500
2025-08-29 04:24:28 - model_training - INFO - 模型 NaiveBayes 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NaiveBayes_single_042427.joblib
2025-08-29 04:24:28 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NaiveBayes_results.joblib
2025-08-29 04:24:28 - model_training - INFO - [NeuralNet] 使用传入的scaler实例
2025-08-29 04:24:28 - model_training - INFO - [NeuralNet] 使用Pipeline进行标准化
2025-08-29 04:24:28 - model_training - INFO - 模型名称: Neural Network
2025-08-29 04:24:28 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:24:28 - model_training - INFO - AUC: 0.9361
2025-08-29 04:24:28 - model_training - INFO - AUPRC: 0.9019
2025-08-29 04:24:28 - model_training - INFO - 混淆矩阵:
2025-08-29 04:24:28 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:24:28 - model_training - INFO - 
分类报告:
2025-08-29 04:24:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:24:28 - model_training - INFO - 训练时间: 0.23 秒
2025-08-29 04:24:28 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8500
2025-08-29 04:24:28 - model_training - INFO - 模型 NeuralNet 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NeuralNet_single_042428.joblib
2025-08-29 04:24:28 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NeuralNet_results.joblib
2025-08-29 04:25:05 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 04:25:05 - model_training - INFO - 准确率: 0.7250
2025-08-29 04:25:05 - model_training - INFO - AUC: 0.8210
2025-08-29 04:25:05 - model_training - INFO - AUPRC: 0.6807
2025-08-29 04:25:05 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:05 - model_training - INFO - 
[[15  8]
 [ 3 14]]
2025-08-29 04:25:05 - model_training - INFO - 
分类报告:
2025-08-29 04:25:05 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-29 04:25:05 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 04:25:05 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-29 04:25:05 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\DecisionTree_single_042505.joblib
2025-08-29 04:25:05 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 04:25:06 - model_training - INFO - 模型名称: Random Forest
2025-08-29 04:25:06 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:25:06 - model_training - INFO - AUC: 0.9501
2025-08-29 04:25:06 - model_training - INFO - AUPRC: 0.9410
2025-08-29 04:25:06 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:06 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:25:06 - model_training - INFO - 
分类报告:
2025-08-29 04:25:06 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:25:06 - model_training - INFO - 训练时间: 0.19 秒
2025-08-29 04:25:06 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 04:25:06 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\RandomForest_single_042506.joblib
2025-08-29 04:25:06 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 04:25:07 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 04:25:07 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:25:07 - model_training - INFO - 模型名称: XGBoost
2025-08-29 04:25:07 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:25:07 - model_training - INFO - AUC: 0.9437
2025-08-29 04:25:07 - model_training - INFO - AUPRC: 0.9387
2025-08-29 04:25:07 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:07 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-29 04:25:07 - model_training - INFO - 
分类报告:
2025-08-29 04:25:07 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 04:25:07 - model_training - INFO - 训练时间: 0.15 秒
2025-08-29 04:25:07 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-29 04:25:07 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\XGBoost_single_042507.joblib
2025-08-29 04:25:07 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 04:25:07 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:25:07 - model_training - INFO - 模型名称: LightGBM
2025-08-29 04:25:07 - model_training - INFO - 准确率: 0.8750
2025-08-29 04:25:07 - model_training - INFO - AUC: 0.9642
2025-08-29 04:25:07 - model_training - INFO - AUPRC: 0.9541
2025-08-29 04:25:07 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:07 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-29 04:25:07 - model_training - INFO - 
分类报告:
2025-08-29 04:25:07 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 04:25:07 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 04:25:07 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 04:25:07 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\LightGBM_single_042507.joblib
2025-08-29 04:25:07 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 04:25:08 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 04:25:08 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 04:25:14 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 04:25:14 - model_training - INFO - 准确率: 0.7250
2025-08-29 04:25:14 - model_training - INFO - AUC: 0.8210
2025-08-29 04:25:14 - model_training - INFO - AUPRC: 0.6807
2025-08-29 04:25:14 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:14 - model_training - INFO - 
[[15  8]
 [ 3 14]]
2025-08-29 04:25:14 - model_training - INFO - 
分类报告:
2025-08-29 04:25:14 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-29 04:25:14 - model_training - INFO - 训练时间: 0.13 秒
2025-08-29 04:25:14 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-29 04:25:14 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\DecisionTree_single_042514.joblib
2025-08-29 04:25:14 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 04:25:15 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 04:25:15 - model_training - INFO - 准确率: 0.7250
2025-08-29 04:25:15 - model_training - INFO - AUC: 0.8210
2025-08-29 04:25:15 - model_training - INFO - AUPRC: 0.6807
2025-08-29 04:25:15 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:15 - model_training - INFO - 
[[15  8]
 [ 3 14]]
2025-08-29 04:25:15 - model_training - INFO - 
分类报告:
2025-08-29 04:25:15 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-29 04:25:15 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 04:25:15 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-29 04:25:15 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\DecisionTree_single_042515.joblib
2025-08-29 04:25:15 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 04:25:15 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 04:25:15 - model_training - INFO - 准确率: 0.7250
2025-08-29 04:25:15 - model_training - INFO - AUC: 0.8210
2025-08-29 04:25:15 - model_training - INFO - AUPRC: 0.6807
2025-08-29 04:25:15 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:15 - model_training - INFO - 
[[15  8]
 [ 3 14]]
2025-08-29 04:25:15 - model_training - INFO - 
分类报告:
2025-08-29 04:25:15 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-29 04:25:15 - model_training - INFO - 训练时间: 0.15 秒
2025-08-29 04:25:15 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-29 04:25:15 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\DecisionTree_single_042515.joblib
2025-08-29 04:25:15 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 04:25:15 - model_training - INFO - 模型名称: Random Forest
2025-08-29 04:25:15 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:25:15 - model_training - INFO - AUC: 0.9501
2025-08-29 04:25:15 - model_training - INFO - AUPRC: 0.9410
2025-08-29 04:25:15 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:15 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:25:15 - model_training - INFO - 
分类报告:
2025-08-29 04:25:15 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:25:15 - model_training - INFO - 训练时间: 0.49 秒
2025-08-29 04:25:15 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 04:25:15 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\RandomForest_single_042515.joblib
2025-08-29 04:25:16 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 04:25:18 - model_training - INFO - 模型名称: Random Forest
2025-08-29 04:25:18 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:25:18 - model_training - INFO - AUC: 0.9501
2025-08-29 04:25:18 - model_training - INFO - AUPRC: 0.9410
2025-08-29 04:25:18 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:18 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:25:18 - model_training - INFO - 
分类报告:
2025-08-29 04:25:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:25:18 - model_training - INFO - 训练时间: 0.48 秒
2025-08-29 04:25:18 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 04:25:18 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\RandomForest_single_042518.joblib
2025-08-29 04:25:18 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 04:25:19 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 04:25:19 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:25:19 - model_training - INFO - 模型名称: XGBoost
2025-08-29 04:25:19 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:25:19 - model_training - INFO - AUC: 0.9437
2025-08-29 04:25:19 - model_training - INFO - AUPRC: 0.9387
2025-08-29 04:25:19 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:19 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-29 04:25:19 - model_training - INFO - 
分类报告:
2025-08-29 04:25:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 04:25:19 - model_training - INFO - 训练时间: 0.33 秒
2025-08-29 04:25:19 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-29 04:25:19 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\XGBoost_single_042519.joblib
2025-08-29 04:25:19 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 04:25:19 - model_training - INFO - 模型名称: Random Forest
2025-08-29 04:25:19 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:25:19 - model_training - INFO - AUC: 0.9501
2025-08-29 04:25:19 - model_training - INFO - AUPRC: 0.9410
2025-08-29 04:25:19 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:19 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:25:19 - model_training - INFO - 
分类报告:
2025-08-29 04:25:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:25:19 - model_training - INFO - 训练时间: 0.48 秒
2025-08-29 04:25:19 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 04:25:19 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\RandomForest_single_042519.joblib
2025-08-29 04:25:20 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 04:25:21 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 04:25:21 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:25:21 - model_training - INFO - 模型名称: XGBoost
2025-08-29 04:25:21 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:25:21 - model_training - INFO - AUC: 0.9437
2025-08-29 04:25:21 - model_training - INFO - AUPRC: 0.9387
2025-08-29 04:25:21 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:21 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-29 04:25:21 - model_training - INFO - 
分类报告:
2025-08-29 04:25:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 04:25:22 - model_training - INFO - 训练时间: 0.25 秒
2025-08-29 04:25:22 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-29 04:25:22 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\XGBoost_single_042522.joblib
2025-08-29 04:25:22 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 04:25:23 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:25:23 - model_training - INFO - 模型名称: LightGBM
2025-08-29 04:25:23 - model_training - INFO - 准确率: 0.8750
2025-08-29 04:25:23 - model_training - INFO - AUC: 0.9642
2025-08-29 04:25:23 - model_training - INFO - AUPRC: 0.9541
2025-08-29 04:25:23 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:23 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-29 04:25:23 - model_training - INFO - 
分类报告:
2025-08-29 04:25:23 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 04:25:23 - model_training - INFO - 训练时间: 0.23 秒
2025-08-29 04:25:23 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 04:25:23 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\LightGBM_single_042523.joblib
2025-08-29 04:25:23 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 04:25:23 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 04:25:23 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:25:23 - model_training - INFO - 模型名称: XGBoost
2025-08-29 04:25:23 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:25:23 - model_training - INFO - AUC: 0.9437
2025-08-29 04:25:23 - model_training - INFO - AUPRC: 0.9387
2025-08-29 04:25:23 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:23 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-29 04:25:23 - model_training - INFO - 
分类报告:
2025-08-29 04:25:23 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 04:25:23 - model_training - INFO - 训练时间: 0.27 秒
2025-08-29 04:25:23 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-29 04:25:23 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\XGBoost_single_042523.joblib
2025-08-29 04:25:23 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 04:25:25 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:25:25 - model_training - INFO - 模型名称: LightGBM
2025-08-29 04:25:25 - model_training - INFO - 准确率: 0.8750
2025-08-29 04:25:25 - model_training - INFO - AUC: 0.9642
2025-08-29 04:25:25 - model_training - INFO - AUPRC: 0.9541
2025-08-29 04:25:25 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:25 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-29 04:25:25 - model_training - INFO - 
分类报告:
2025-08-29 04:25:25 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 04:25:25 - model_training - INFO - 训练时间: 0.23 秒
2025-08-29 04:25:25 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 04:25:25 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\LightGBM_single_042525.joblib
2025-08-29 04:25:25 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 04:25:27 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 04:25:27 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 04:25:27 - model_training - ERROR - [CatBoost] 模型训练失败: catboost/cuda/cuda_lib/devices_provider.h:190: Error: device already requested 0
2025-08-29 04:25:27 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 04:25:27 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 04:25:27 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 04:25:27 - model_training - INFO - 准确率: 0.7750
2025-08-29 04:25:27 - model_training - INFO - AUC: 0.9028
2025-08-29 04:25:27 - model_training - INFO - AUPRC: 0.8685
2025-08-29 04:25:27 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:27 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-29 04:25:27 - model_training - INFO - 
分类报告:
2025-08-29 04:25:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 04:25:27 - model_training - INFO - 训练时间: 0.17 秒
2025-08-29 04:25:27 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 04:25:27 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\Logistic_single_042527.joblib
2025-08-29 04:25:27 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 04:25:27 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:25:27 - model_training - INFO - 模型名称: LightGBM
2025-08-29 04:25:27 - model_training - INFO - 准确率: 0.8750
2025-08-29 04:25:27 - model_training - INFO - AUC: 0.9642
2025-08-29 04:25:27 - model_training - INFO - AUPRC: 0.9541
2025-08-29 04:25:27 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:27 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-29 04:25:27 - model_training - INFO - 
分类报告:
2025-08-29 04:25:27 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 04:25:27 - model_training - INFO - 训练时间: 0.23 秒
2025-08-29 04:25:27 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 04:25:27 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\LightGBM_single_042527.joblib
2025-08-29 04:25:27 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 04:25:28 - model_training - INFO - 模型名称: CatBoost
2025-08-29 04:25:28 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:25:28 - model_training - INFO - AUC: 0.9668
2025-08-29 04:25:28 - model_training - INFO - AUPRC: 0.9616
2025-08-29 04:25:28 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:28 - model_training - INFO - 
[[18  5]
 [ 1 16]]
2025-08-29 04:25:28 - model_training - INFO - 
分类报告:
2025-08-29 04:25:29 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 04:25:29 - model_training - INFO - 训练时间: 20.57 秒
2025-08-29 04:25:29 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8500
2025-08-29 04:25:29 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\CatBoost_single_042529.joblib
2025-08-29 04:25:29 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 04:25:29 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 04:25:29 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 04:25:31 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 04:25:31 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 04:25:31 - model_training - INFO - 模型名称: SVM
2025-08-29 04:25:31 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:25:31 - model_training - INFO - AUC: 0.9258
2025-08-29 04:25:31 - model_training - INFO - AUPRC: 0.9259
2025-08-29 04:25:31 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:31 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-29 04:25:31 - model_training - INFO - 
分类报告:
2025-08-29 04:25:31 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-29 04:25:31 - model_training - INFO - 训练时间: 0.17 秒
2025-08-29 04:25:31 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-29 04:25:31 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\SVM_single_042531.joblib
2025-08-29 04:25:31 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 04:25:31 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 04:25:31 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 04:25:31 - model_training - ERROR - [CatBoost] 模型训练失败: catboost/cuda/cuda_lib/devices_provider.h:190: Error: device already requested 0
2025-08-29 04:25:32 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 04:25:32 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 04:25:32 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 04:25:32 - model_training - INFO - 准确率: 0.7750
2025-08-29 04:25:32 - model_training - INFO - AUC: 0.9028
2025-08-29 04:25:32 - model_training - INFO - AUPRC: 0.8685
2025-08-29 04:25:32 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:32 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-29 04:25:32 - model_training - INFO - 
分类报告:
2025-08-29 04:25:32 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 04:25:32 - model_training - INFO - 训练时间: 0.16 秒
2025-08-29 04:25:32 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 04:25:32 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\Logistic_single_042532.joblib
2025-08-29 04:25:32 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 04:25:32 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 04:25:32 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 04:25:32 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 04:25:32 - model_training - INFO - 准确率: 0.7750
2025-08-29 04:25:32 - model_training - INFO - AUC: 0.9028
2025-08-29 04:25:32 - model_training - INFO - AUPRC: 0.8685
2025-08-29 04:25:32 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:32 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-29 04:25:32 - model_training - INFO - 
分类报告:
2025-08-29 04:25:32 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 04:25:32 - model_training - INFO - 训练时间: 0.21 秒
2025-08-29 04:25:32 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 04:25:32 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\Logistic_single_042532.joblib
2025-08-29 04:25:32 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 04:25:35 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 04:25:35 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 04:25:35 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 04:25:35 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 04:25:35 - model_training - INFO - 模型名称: SVM
2025-08-29 04:25:35 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:25:35 - model_training - INFO - AUC: 0.9258
2025-08-29 04:25:35 - model_training - INFO - AUPRC: 0.9259
2025-08-29 04:25:35 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:35 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-29 04:25:35 - model_training - INFO - 
分类报告:
2025-08-29 04:25:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-29 04:25:35 - model_training - INFO - 训练时间: 0.24 秒
2025-08-29 04:25:35 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-29 04:25:35 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\SVM_single_042535.joblib
2025-08-29 04:25:35 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 04:25:35 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 04:25:35 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 04:25:36 - model_training - INFO - 模型名称: SVM
2025-08-29 04:25:36 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:25:36 - model_training - INFO - AUC: 0.9258
2025-08-29 04:25:36 - model_training - INFO - AUPRC: 0.9259
2025-08-29 04:25:36 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:36 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-29 04:25:36 - model_training - INFO - 
分类报告:
2025-08-29 04:25:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-29 04:25:36 - model_training - INFO - 训练时间: 0.21 秒
2025-08-29 04:25:36 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-29 04:25:36 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\SVM_single_042536.joblib
2025-08-29 04:25:36 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 04:25:38 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 04:25:38 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 04:25:38 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 04:25:38 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 04:25:50 - model_training - INFO - 模型名称: Naive Bayes
2025-08-29 04:25:50 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:25:50 - model_training - INFO - AUC: 0.9156
2025-08-29 04:25:50 - model_training - INFO - AUPRC: 0.8954
2025-08-29 04:25:50 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:50 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-29 04:25:50 - model_training - INFO - 
分类报告:
2025-08-29 04:25:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-29 04:25:50 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 04:25:50 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8500
2025-08-29 04:25:50 - model_training - INFO - 模型 NaiveBayes 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NaiveBayes_single_042550.joblib
2025-08-29 04:25:50 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NaiveBayes_results.joblib
2025-08-29 04:25:51 - model_training - INFO - [NeuralNet] 使用传入的scaler实例
2025-08-29 04:25:51 - model_training - INFO - [NeuralNet] 使用Pipeline进行标准化
2025-08-29 04:25:51 - model_training - INFO - 模型名称: Neural Network
2025-08-29 04:25:51 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:25:51 - model_training - INFO - AUC: 0.9437
2025-08-29 04:25:51 - model_training - INFO - AUPRC: 0.9159
2025-08-29 04:25:51 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:51 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:25:51 - model_training - INFO - 
分类报告:
2025-08-29 04:25:51 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:25:51 - model_training - INFO - 训练时间: 0.34 秒
2025-08-29 04:25:51 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8500
2025-08-29 04:25:51 - model_training - INFO - 模型 NeuralNet 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NeuralNet_single_042551.joblib
2025-08-29 04:25:51 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NeuralNet_results.joblib
2025-08-29 04:25:52 - model_training - INFO - 模型名称: CatBoost
2025-08-29 04:25:52 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:25:52 - model_training - INFO - AUC: 0.9668
2025-08-29 04:25:52 - model_training - INFO - AUPRC: 0.9616
2025-08-29 04:25:52 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:52 - model_training - INFO - 
[[18  5]
 [ 1 16]]
2025-08-29 04:25:52 - model_training - INFO - 
分类报告:
2025-08-29 04:25:52 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 04:25:52 - model_training - INFO - 训练时间: 23.44 秒
2025-08-29 04:25:52 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8500
2025-08-29 04:25:52 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\CatBoost_single_042552.joblib
2025-08-29 04:25:52 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 04:25:53 - model_training - INFO - 模型名称: Naive Bayes
2025-08-29 04:25:53 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:25:53 - model_training - INFO - AUC: 0.9156
2025-08-29 04:25:53 - model_training - INFO - AUPRC: 0.8954
2025-08-29 04:25:53 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:53 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-29 04:25:53 - model_training - INFO - 
分类报告:
2025-08-29 04:25:53 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-29 04:25:53 - model_training - INFO - 训练时间: 0.21 秒
2025-08-29 04:25:53 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8500
2025-08-29 04:25:53 - model_training - INFO - 模型 NaiveBayes 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NaiveBayes_single_042553.joblib
2025-08-29 04:25:53 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NaiveBayes_results.joblib
2025-08-29 04:25:53 - model_training - INFO - 模型名称: Naive Bayes
2025-08-29 04:25:53 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:25:53 - model_training - INFO - AUC: 0.9156
2025-08-29 04:25:53 - model_training - INFO - AUPRC: 0.8954
2025-08-29 04:25:53 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:53 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-29 04:25:53 - model_training - INFO - 
分类报告:
2025-08-29 04:25:53 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-29 04:25:53 - model_training - INFO - 训练时间: 0.16 秒
2025-08-29 04:25:53 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8500
2025-08-29 04:25:53 - model_training - INFO - 模型 NaiveBayes 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NaiveBayes_single_042553.joblib
2025-08-29 04:25:53 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NaiveBayes_results.joblib
2025-08-29 04:25:56 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 04:25:56 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 04:25:56 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 04:25:56 - model_training - INFO - 准确率: 0.7750
2025-08-29 04:25:56 - model_training - INFO - AUC: 0.9028
2025-08-29 04:25:56 - model_training - INFO - AUPRC: 0.8685
2025-08-29 04:25:56 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:56 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-29 04:25:56 - model_training - INFO - 
分类报告:
2025-08-29 04:25:56 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 04:25:56 - model_training - INFO - 训练时间: 0.24 秒
2025-08-29 04:25:56 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 04:25:56 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\Logistic_single_042556.joblib
2025-08-29 04:25:56 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 04:25:57 - model_training - INFO - [NeuralNet] 使用传入的scaler实例
2025-08-29 04:25:57 - model_training - INFO - [NeuralNet] 使用Pipeline进行标准化
2025-08-29 04:25:57 - model_training - INFO - [NeuralNet] 使用传入的scaler实例
2025-08-29 04:25:57 - model_training - INFO - [NeuralNet] 使用Pipeline进行标准化
2025-08-29 04:25:59 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 04:25:59 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 04:25:59 - model_training - INFO - 模型名称: SVM
2025-08-29 04:25:59 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:25:59 - model_training - INFO - AUC: 0.9258
2025-08-29 04:25:59 - model_training - INFO - AUPRC: 0.9259
2025-08-29 04:25:59 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:59 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-29 04:25:59 - model_training - INFO - 
分类报告:
2025-08-29 04:25:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-29 04:25:59 - model_training - INFO - 训练时间: 0.14 秒
2025-08-29 04:25:59 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-29 04:25:59 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\SVM_single_042559.joblib
2025-08-29 04:25:59 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 04:25:59 - model_training - INFO - 模型名称: Neural Network
2025-08-29 04:25:59 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:25:59 - model_training - INFO - AUC: 0.9437
2025-08-29 04:25:59 - model_training - INFO - AUPRC: 0.9159
2025-08-29 04:25:59 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:59 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:25:59 - model_training - INFO - 
分类报告:
2025-08-29 04:25:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:25:59 - model_training - INFO - 训练时间: 1.82 秒
2025-08-29 04:25:59 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8500
2025-08-29 04:25:59 - model_training - INFO - 模型 NeuralNet 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NeuralNet_single_042559.joblib
2025-08-29 04:25:59 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NeuralNet_results.joblib
2025-08-29 04:25:59 - model_training - INFO - 模型名称: Neural Network
2025-08-29 04:25:59 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:25:59 - model_training - INFO - AUC: 0.9437
2025-08-29 04:25:59 - model_training - INFO - AUPRC: 0.9159
2025-08-29 04:25:59 - model_training - INFO - 混淆矩阵:
2025-08-29 04:25:59 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:25:59 - model_training - INFO - 
分类报告:
2025-08-29 04:25:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:25:59 - model_training - INFO - 训练时间: 1.86 秒
2025-08-29 04:25:59 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8500
2025-08-29 04:25:59 - model_training - INFO - 模型 NeuralNet 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NeuralNet_single_042559.joblib
2025-08-29 04:25:59 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NeuralNet_results.joblib
2025-08-29 04:26:02 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 04:26:02 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 04:26:17 - model_training - INFO - 模型名称: Naive Bayes
2025-08-29 04:26:17 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:26:17 - model_training - INFO - AUC: 0.9156
2025-08-29 04:26:17 - model_training - INFO - AUPRC: 0.8954
2025-08-29 04:26:17 - model_training - INFO - 混淆矩阵:
2025-08-29 04:26:17 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-29 04:26:17 - model_training - INFO - 
分类报告:
2025-08-29 04:26:17 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-29 04:26:17 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 04:26:17 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8500
2025-08-29 04:26:17 - model_training - INFO - 模型 NaiveBayes 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NaiveBayes_single_042617.joblib
2025-08-29 04:26:17 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NaiveBayes_results.joblib
2025-08-29 04:26:17 - model_training - INFO - [NeuralNet] 使用传入的scaler实例
2025-08-29 04:26:17 - model_training - INFO - [NeuralNet] 使用Pipeline进行标准化
2025-08-29 04:26:17 - model_training - INFO - 模型名称: Neural Network
2025-08-29 04:26:17 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:26:17 - model_training - INFO - AUC: 0.9437
2025-08-29 04:26:17 - model_training - INFO - AUPRC: 0.9159
2025-08-29 04:26:17 - model_training - INFO - 混淆矩阵:
2025-08-29 04:26:17 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:26:17 - model_training - INFO - 
分类报告:
2025-08-29 04:26:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:26:18 - model_training - INFO - 训练时间: 0.25 秒
2025-08-29 04:26:18 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8500
2025-08-29 04:26:18 - model_training - INFO - 模型 NeuralNet 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NeuralNet_single_042618.joblib
2025-08-29 04:26:18 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NeuralNet_results.joblib
2025-08-29 04:29:40 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 04:29:40 - model_training - INFO - 准确率: 0.7250
2025-08-29 04:29:40 - model_training - INFO - AUC: 0.8210
2025-08-29 04:29:40 - model_training - INFO - AUPRC: 0.6807
2025-08-29 04:29:40 - model_training - INFO - 混淆矩阵:
2025-08-29 04:29:40 - model_training - INFO - 
[[15  8]
 [ 3 14]]
2025-08-29 04:29:40 - model_training - INFO - 
分类报告:
2025-08-29 04:29:40 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-29 04:29:40 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 04:29:40 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-29 04:29:40 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\DecisionTree_single_042940.joblib
2025-08-29 04:29:40 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 04:29:41 - model_training - INFO - 模型名称: Random Forest
2025-08-29 04:29:41 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:29:41 - model_training - INFO - AUC: 0.9501
2025-08-29 04:29:41 - model_training - INFO - AUPRC: 0.9410
2025-08-29 04:29:41 - model_training - INFO - 混淆矩阵:
2025-08-29 04:29:41 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:29:41 - model_training - INFO - 
分类报告:
2025-08-29 04:29:41 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:29:41 - model_training - INFO - 训练时间: 0.18 秒
2025-08-29 04:29:41 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 04:29:41 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\RandomForest_single_042941.joblib
2025-08-29 04:29:41 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 04:29:42 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 04:29:42 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:29:42 - model_training - INFO - 模型名称: XGBoost
2025-08-29 04:29:42 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:29:42 - model_training - INFO - AUC: 0.9437
2025-08-29 04:29:42 - model_training - INFO - AUPRC: 0.9387
2025-08-29 04:29:42 - model_training - INFO - 混淆矩阵:
2025-08-29 04:29:42 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-29 04:29:42 - model_training - INFO - 
分类报告:
2025-08-29 04:29:42 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 04:29:42 - model_training - INFO - 训练时间: 0.15 秒
2025-08-29 04:29:42 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-29 04:29:42 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\XGBoost_single_042942.joblib
2025-08-29 04:29:42 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 04:29:43 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:29:43 - model_training - INFO - 模型名称: LightGBM
2025-08-29 04:29:43 - model_training - INFO - 准确率: 0.8750
2025-08-29 04:29:43 - model_training - INFO - AUC: 0.9642
2025-08-29 04:29:43 - model_training - INFO - AUPRC: 0.9541
2025-08-29 04:29:43 - model_training - INFO - 混淆矩阵:
2025-08-29 04:29:43 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-29 04:29:43 - model_training - INFO - 
分类报告:
2025-08-29 04:29:43 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 04:29:43 - model_training - INFO - 训练时间: 0.14 秒
2025-08-29 04:29:43 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 04:29:43 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\LightGBM_single_042943.joblib
2025-08-29 04:29:43 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 04:29:43 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 04:29:43 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 04:30:04 - model_training - INFO - 模型名称: CatBoost
2025-08-29 04:30:04 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:30:04 - model_training - INFO - AUC: 0.9668
2025-08-29 04:30:04 - model_training - INFO - AUPRC: 0.9616
2025-08-29 04:30:04 - model_training - INFO - 混淆矩阵:
2025-08-29 04:30:04 - model_training - INFO - 
[[18  5]
 [ 1 16]]
2025-08-29 04:30:04 - model_training - INFO - 
分类报告:
2025-08-29 04:30:04 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 04:30:04 - model_training - INFO - 训练时间: 20.46 秒
2025-08-29 04:30:04 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8500
2025-08-29 04:30:04 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\CatBoost_single_043004.joblib
2025-08-29 04:30:04 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 04:30:05 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 04:30:05 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 04:30:05 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 04:30:05 - model_training - INFO - 准确率: 0.7750
2025-08-29 04:30:05 - model_training - INFO - AUC: 0.9028
2025-08-29 04:30:05 - model_training - INFO - AUPRC: 0.8685
2025-08-29 04:30:05 - model_training - INFO - 混淆矩阵:
2025-08-29 04:30:05 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-29 04:30:05 - model_training - INFO - 
分类报告:
2025-08-29 04:30:05 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 04:30:05 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 04:30:05 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 04:30:05 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\Logistic_single_043005.joblib
2025-08-29 04:30:05 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 04:30:05 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 04:30:05 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 04:30:05 - model_training - INFO - 模型名称: SVM
2025-08-29 04:30:05 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:30:05 - model_training - INFO - AUC: 0.9258
2025-08-29 04:30:05 - model_training - INFO - AUPRC: 0.9259
2025-08-29 04:30:05 - model_training - INFO - 混淆矩阵:
2025-08-29 04:30:05 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-29 04:30:05 - model_training - INFO - 
分类报告:
2025-08-29 04:30:05 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-29 04:30:05 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 04:30:05 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-29 04:30:05 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\SVM_single_043005.joblib
2025-08-29 04:30:05 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 04:30:06 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 04:30:06 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 04:30:21 - model_training - INFO - 模型名称: Naive Bayes
2025-08-29 04:30:21 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:30:21 - model_training - INFO - AUC: 0.9156
2025-08-29 04:30:21 - model_training - INFO - AUPRC: 0.8954
2025-08-29 04:30:21 - model_training - INFO - 混淆矩阵:
2025-08-29 04:30:21 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-29 04:30:21 - model_training - INFO - 
分类报告:
2025-08-29 04:30:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-29 04:30:21 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 04:30:21 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8500
2025-08-29 04:30:21 - model_training - INFO - 模型 NaiveBayes 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NaiveBayes_single_043021.joblib
2025-08-29 04:30:21 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NaiveBayes_results.joblib
2025-08-29 04:30:22 - model_training - INFO - [NeuralNet] 使用传入的scaler实例
2025-08-29 04:30:22 - model_training - INFO - [NeuralNet] 使用Pipeline进行标准化
2025-08-29 04:30:22 - model_training - INFO - 模型名称: Neural Network
2025-08-29 04:30:22 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:30:22 - model_training - INFO - AUC: 0.9437
2025-08-29 04:30:22 - model_training - INFO - AUPRC: 0.9159
2025-08-29 04:30:22 - model_training - INFO - 混淆矩阵:
2025-08-29 04:30:22 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:30:22 - model_training - INFO - 
分类报告:
2025-08-29 04:30:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:30:22 - model_training - INFO - 训练时间: 0.36 秒
2025-08-29 04:30:22 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8500
2025-08-29 04:30:22 - model_training - INFO - 模型 NeuralNet 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NeuralNet_single_043022.joblib
2025-08-29 04:30:22 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NeuralNet_results.joblib
2025-08-29 04:33:09 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 04:33:09 - model_training - INFO - 准确率: 0.7250
2025-08-29 04:33:09 - model_training - INFO - AUC: 0.8210
2025-08-29 04:33:09 - model_training - INFO - AUPRC: 0.6807
2025-08-29 04:33:09 - model_training - INFO - 混淆矩阵:
2025-08-29 04:33:09 - model_training - INFO - 
[[15  8]
 [ 3 14]]
2025-08-29 04:33:09 - model_training - INFO - 
分类报告:
2025-08-29 04:33:09 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-29 04:33:09 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 04:33:09 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-29 04:33:09 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\DecisionTree_single_043309.joblib
2025-08-29 04:33:09 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 04:33:10 - model_training - INFO - 模型名称: Random Forest
2025-08-29 04:33:10 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:33:10 - model_training - INFO - AUC: 0.9501
2025-08-29 04:33:10 - model_training - INFO - AUPRC: 0.9410
2025-08-29 04:33:10 - model_training - INFO - 混淆矩阵:
2025-08-29 04:33:10 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:33:10 - model_training - INFO - 
分类报告:
2025-08-29 04:33:10 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:33:10 - model_training - INFO - 训练时间: 0.18 秒
2025-08-29 04:33:10 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 04:33:10 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\RandomForest_single_043310.joblib
2025-08-29 04:33:10 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 04:33:11 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 04:33:11 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:33:11 - model_training - INFO - 模型名称: XGBoost
2025-08-29 04:33:11 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:33:11 - model_training - INFO - AUC: 0.9437
2025-08-29 04:33:11 - model_training - INFO - AUPRC: 0.9387
2025-08-29 04:33:11 - model_training - INFO - 混淆矩阵:
2025-08-29 04:33:11 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-29 04:33:11 - model_training - INFO - 
分类报告:
2025-08-29 04:33:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 04:33:11 - model_training - INFO - 训练时间: 0.16 秒
2025-08-29 04:33:11 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-29 04:33:11 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\XGBoost_single_043311.joblib
2025-08-29 04:33:11 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 04:33:12 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:33:12 - model_training - INFO - 模型名称: LightGBM
2025-08-29 04:33:12 - model_training - INFO - 准确率: 0.8750
2025-08-29 04:33:12 - model_training - INFO - AUC: 0.9642
2025-08-29 04:33:12 - model_training - INFO - AUPRC: 0.9541
2025-08-29 04:33:12 - model_training - INFO - 混淆矩阵:
2025-08-29 04:33:12 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-29 04:33:12 - model_training - INFO - 
分类报告:
2025-08-29 04:33:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 04:33:12 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 04:33:12 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 04:33:12 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\LightGBM_single_043312.joblib
2025-08-29 04:33:12 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 04:33:12 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 04:33:12 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 04:33:33 - model_training - INFO - 模型名称: CatBoost
2025-08-29 04:33:33 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:33:33 - model_training - INFO - AUC: 0.9668
2025-08-29 04:33:33 - model_training - INFO - AUPRC: 0.9616
2025-08-29 04:33:33 - model_training - INFO - 混淆矩阵:
2025-08-29 04:33:33 - model_training - INFO - 
[[18  5]
 [ 1 16]]
2025-08-29 04:33:33 - model_training - INFO - 
分类报告:
2025-08-29 04:33:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 04:33:33 - model_training - INFO - 训练时间: 20.81 秒
2025-08-29 04:33:33 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8500
2025-08-29 04:33:33 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\CatBoost_single_043333.joblib
2025-08-29 04:33:33 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 04:33:34 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 04:33:34 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 04:33:34 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 04:33:34 - model_training - INFO - 准确率: 0.7750
2025-08-29 04:33:34 - model_training - INFO - AUC: 0.9028
2025-08-29 04:33:34 - model_training - INFO - AUPRC: 0.8685
2025-08-29 04:33:34 - model_training - INFO - 混淆矩阵:
2025-08-29 04:33:34 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-29 04:33:34 - model_training - INFO - 
分类报告:
2025-08-29 04:33:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 04:33:34 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 04:33:34 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 04:33:34 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\Logistic_single_043334.joblib
2025-08-29 04:33:34 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 04:33:35 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 04:33:35 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 04:33:35 - model_training - INFO - 模型名称: SVM
2025-08-29 04:33:35 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:33:35 - model_training - INFO - AUC: 0.9258
2025-08-29 04:33:35 - model_training - INFO - AUPRC: 0.9259
2025-08-29 04:33:35 - model_training - INFO - 混淆矩阵:
2025-08-29 04:33:35 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-29 04:33:35 - model_training - INFO - 
分类报告:
2025-08-29 04:33:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-29 04:33:35 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 04:33:35 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-29 04:33:35 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\SVM_single_043335.joblib
2025-08-29 04:33:35 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 04:33:35 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 04:33:35 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 04:33:51 - model_training - INFO - 模型名称: Naive Bayes
2025-08-29 04:33:51 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:33:51 - model_training - INFO - AUC: 0.9156
2025-08-29 04:33:51 - model_training - INFO - AUPRC: 0.8954
2025-08-29 04:33:51 - model_training - INFO - 混淆矩阵:
2025-08-29 04:33:51 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-29 04:33:51 - model_training - INFO - 
分类报告:
2025-08-29 04:33:51 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-29 04:33:51 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 04:33:51 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8500
2025-08-29 04:33:51 - model_training - INFO - 模型 NaiveBayes 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NaiveBayes_single_043351.joblib
2025-08-29 04:33:51 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NaiveBayes_results.joblib
2025-08-29 04:33:51 - model_training - INFO - [NeuralNet] 使用传入的scaler实例
2025-08-29 04:33:51 - model_training - INFO - [NeuralNet] 使用Pipeline进行标准化
2025-08-29 04:33:52 - model_training - INFO - 模型名称: Neural Network
2025-08-29 04:33:52 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:33:52 - model_training - INFO - AUC: 0.9437
2025-08-29 04:33:52 - model_training - INFO - AUPRC: 0.9159
2025-08-29 04:33:52 - model_training - INFO - 混淆矩阵:
2025-08-29 04:33:52 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:33:52 - model_training - INFO - 
分类报告:
2025-08-29 04:33:52 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:33:52 - model_training - INFO - 训练时间: 0.33 秒
2025-08-29 04:33:52 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8500
2025-08-29 04:33:52 - model_training - INFO - 模型 NeuralNet 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NeuralNet_single_043352.joblib
2025-08-29 04:33:52 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NeuralNet_results.joblib
2025-08-29 04:34:58 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 04:34:58 - model_training - INFO - 准确率: 0.7250
2025-08-29 04:34:58 - model_training - INFO - AUC: 0.8210
2025-08-29 04:34:58 - model_training - INFO - AUPRC: 0.6807
2025-08-29 04:34:58 - model_training - INFO - 混淆矩阵:
2025-08-29 04:34:58 - model_training - INFO - 
[[15  8]
 [ 3 14]]
2025-08-29 04:34:58 - model_training - INFO - 
分类报告:
2025-08-29 04:34:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-29 04:34:58 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 04:34:58 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-29 04:34:58 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\DecisionTree_single_043458.joblib
2025-08-29 04:34:58 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 04:34:59 - model_training - INFO - 模型名称: Random Forest
2025-08-29 04:34:59 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:34:59 - model_training - INFO - AUC: 0.9501
2025-08-29 04:34:59 - model_training - INFO - AUPRC: 0.9410
2025-08-29 04:34:59 - model_training - INFO - 混淆矩阵:
2025-08-29 04:34:59 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:34:59 - model_training - INFO - 
分类报告:
2025-08-29 04:34:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:34:59 - model_training - INFO - 训练时间: 0.20 秒
2025-08-29 04:34:59 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 04:34:59 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\RandomForest_single_043459.joblib
2025-08-29 04:34:59 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 04:34:59 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 04:34:59 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:34:59 - model_training - INFO - 模型名称: XGBoost
2025-08-29 04:34:59 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:34:59 - model_training - INFO - AUC: 0.9437
2025-08-29 04:34:59 - model_training - INFO - AUPRC: 0.9387
2025-08-29 04:34:59 - model_training - INFO - 混淆矩阵:
2025-08-29 04:34:59 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-29 04:34:59 - model_training - INFO - 
分类报告:
2025-08-29 04:34:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 04:34:59 - model_training - INFO - 训练时间: 0.15 秒
2025-08-29 04:34:59 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-29 04:34:59 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\XGBoost_single_043459.joblib
2025-08-29 04:34:59 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 04:35:00 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 04:35:00 - model_training - INFO - 模型名称: LightGBM
2025-08-29 04:35:00 - model_training - INFO - 准确率: 0.8750
2025-08-29 04:35:00 - model_training - INFO - AUC: 0.9642
2025-08-29 04:35:00 - model_training - INFO - AUPRC: 0.9541
2025-08-29 04:35:00 - model_training - INFO - 混淆矩阵:
2025-08-29 04:35:00 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-29 04:35:00 - model_training - INFO - 
分类报告:
2025-08-29 04:35:00 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 04:35:00 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 04:35:00 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 04:35:00 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\LightGBM_single_043500.joblib
2025-08-29 04:35:00 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 04:35:01 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 04:35:01 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 04:35:21 - model_training - INFO - 模型名称: CatBoost
2025-08-29 04:35:21 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:35:21 - model_training - INFO - AUC: 0.9668
2025-08-29 04:35:21 - model_training - INFO - AUPRC: 0.9616
2025-08-29 04:35:21 - model_training - INFO - 混淆矩阵:
2025-08-29 04:35:21 - model_training - INFO - 
[[18  5]
 [ 1 16]]
2025-08-29 04:35:21 - model_training - INFO - 
分类报告:
2025-08-29 04:35:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 04:35:21 - model_training - INFO - 训练时间: 20.61 秒
2025-08-29 04:35:21 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8500
2025-08-29 04:35:21 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\CatBoost_single_043521.joblib
2025-08-29 04:35:21 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 04:35:22 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 04:35:22 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 04:35:22 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 04:35:22 - model_training - INFO - 准确率: 0.7750
2025-08-29 04:35:22 - model_training - INFO - AUC: 0.9028
2025-08-29 04:35:22 - model_training - INFO - AUPRC: 0.8685
2025-08-29 04:35:22 - model_training - INFO - 混淆矩阵:
2025-08-29 04:35:22 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-29 04:35:22 - model_training - INFO - 
分类报告:
2025-08-29 04:35:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 04:35:22 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 04:35:22 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 04:35:22 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\Logistic_single_043522.joblib
2025-08-29 04:35:22 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 04:35:23 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 04:35:23 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 04:35:23 - model_training - INFO - 模型名称: SVM
2025-08-29 04:35:23 - model_training - INFO - 准确率: 0.8250
2025-08-29 04:35:23 - model_training - INFO - AUC: 0.9258
2025-08-29 04:35:23 - model_training - INFO - AUPRC: 0.9259
2025-08-29 04:35:23 - model_training - INFO - 混淆矩阵:
2025-08-29 04:35:23 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-29 04:35:23 - model_training - INFO - 
分类报告:
2025-08-29 04:35:23 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-29 04:35:23 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 04:35:23 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-29 04:35:23 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\SVM_single_043523.joblib
2025-08-29 04:35:23 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 04:35:24 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 04:35:24 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 04:35:39 - model_training - INFO - 模型名称: Naive Bayes
2025-08-29 04:35:39 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:35:39 - model_training - INFO - AUC: 0.9156
2025-08-29 04:35:39 - model_training - INFO - AUPRC: 0.8954
2025-08-29 04:35:39 - model_training - INFO - 混淆矩阵:
2025-08-29 04:35:39 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-29 04:35:39 - model_training - INFO - 
分类报告:
2025-08-29 04:35:39 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-29 04:35:39 - model_training - INFO - 训练时间: 0.13 秒
2025-08-29 04:35:39 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8500
2025-08-29 04:35:39 - model_training - INFO - 模型 NaiveBayes 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NaiveBayes_single_043539.joblib
2025-08-29 04:35:39 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NaiveBayes_results.joblib
2025-08-29 04:35:39 - model_training - INFO - [NeuralNet] 使用传入的scaler实例
2025-08-29 04:35:39 - model_training - INFO - [NeuralNet] 使用Pipeline进行标准化
2025-08-29 04:35:40 - model_training - INFO - 模型名称: Neural Network
2025-08-29 04:35:40 - model_training - INFO - 准确率: 0.8500
2025-08-29 04:35:40 - model_training - INFO - AUC: 0.9437
2025-08-29 04:35:40 - model_training - INFO - AUPRC: 0.9159
2025-08-29 04:35:40 - model_training - INFO - 混淆矩阵:
2025-08-29 04:35:40 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 04:35:40 - model_training - INFO - 
分类报告:
2025-08-29 04:35:40 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 04:35:40 - model_training - INFO - 训练时间: 0.27 秒
2025-08-29 04:35:40 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8500
2025-08-29 04:35:40 - model_training - INFO - 模型 NeuralNet 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_042425\models\NeuralNet_single_043540.joblib
2025-08-29 04:35:40 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NeuralNet_results.joblib
