2025-08-09 22:44:50 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-09 22:44:50 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 22:44:50 - model_training - INFO - 准确率: 0.8750
2025-08-09 22:44:50 - model_training - INFO - AUC: 0.9790
2025-08-09 22:44:50 - model_training - INFO - AUPRC: 0.9838
2025-08-09 22:44:50 - model_training - INFO - 混淆矩阵:
2025-08-09 22:44:50 - model_training - INFO - 
[[ 9  2]
 [ 1 12]]
2025-08-09 22:44:50 - model_training - INFO - 
分类报告:
2025-08-09 22:44:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.82      0.86        11
           1       0.86      0.92      0.89        13

    accuracy                           0.88        24
   macro avg       0.88      0.87      0.87        24
weighted avg       0.88      0.88      0.87        24

2025-08-09 22:44:50 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 22:44:50 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8750
2025-08-09 22:44:50 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 22:44:50 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-09 22:44:50 - model_training - INFO - 模型名称: SVM
2025-08-09 22:44:50 - model_training - INFO - 准确率: 0.7917
2025-08-09 22:44:50 - model_training - INFO - AUC: 0.9301
2025-08-09 22:44:50 - model_training - INFO - AUPRC: 0.9477
2025-08-09 22:44:50 - model_training - INFO - 混淆矩阵:
2025-08-09 22:44:50 - model_training - INFO - 
[[ 8  3]
 [ 2 11]]
2025-08-09 22:44:50 - model_training - INFO - 
分类报告:
2025-08-09 22:44:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.73      0.76        11
           1       0.79      0.85      0.81        13

    accuracy                           0.79        24
   macro avg       0.79      0.79      0.79        24
weighted avg       0.79      0.79      0.79        24

2025-08-09 22:44:50 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 22:44:50 - model_training - INFO - 模型 SVM 性能: 准确率=0.7917
2025-08-09 22:44:50 - model_training - INFO - 模型 SVM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 22:44:50 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-08-09 22:44:51 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-09 22:44:51 - binary_classification_pipeline - INFO - 初始化二分类分析流程
2025-08-09 22:44:51 - binary_classification_pipeline - INFO - 数据文件: test_scaler_coverage.csv
2025-08-09 22:44:51 - binary_classification_pipeline - INFO - 输出目录: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\binary_classification_analysis
2025-08-09 22:44:51 - binary_classification_pipeline - INFO - 超参数调优: 启用
2025-08-09 22:44:51 - binary_classification_pipeline - INFO - SHAP分析: 启用
2025-08-09 22:44:51 - model_ensemble - INFO - ============================================================
2025-08-09 22:44:51 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-09 22:44:51 - model_ensemble - INFO - ============================================================
2025-08-09 22:44:51 - model_ensemble - INFO - 基础模型: ['Logistic', 'RandomForest']
2025-08-09 22:44:51 - model_ensemble - INFO - 集成方法: ['voting']
2025-08-09 22:44:51 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-09 22:44:51 - model_ensemble - INFO - 训练基础模型: Logistic
2025-08-09 22:44:51 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 22:44:51 - model_training - INFO - 准确率: 0.8750
2025-08-09 22:44:51 - model_training - INFO - AUC: 0.9790
2025-08-09 22:44:51 - model_training - INFO - AUPRC: 0.9838
2025-08-09 22:44:51 - model_training - INFO - 混淆矩阵:
2025-08-09 22:44:51 - model_training - INFO - 
[[ 9  2]
 [ 1 12]]
2025-08-09 22:44:51 - model_training - INFO - 
分类报告:
2025-08-09 22:44:51 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.82      0.86        11
           1       0.86      0.92      0.89        13

    accuracy                           0.88        24
   macro avg       0.88      0.87      0.87        24
weighted avg       0.88      0.88      0.87        24

2025-08-09 22:44:51 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 22:44:51 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8750
2025-08-09 22:44:51 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 22:44:51 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-09 22:44:51 - model_ensemble - INFO -   Logistic 训练完成
2025-08-09 22:44:51 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-09 22:44:51 - model_training - INFO - 模型名称: Random Forest
2025-08-09 22:44:51 - model_training - INFO - 准确率: 0.7500
2025-08-09 22:44:51 - model_training - INFO - AUC: 0.9126
2025-08-09 22:44:51 - model_training - INFO - AUPRC: 0.9352
2025-08-09 22:44:51 - model_training - INFO - 混淆矩阵:
2025-08-09 22:44:51 - model_training - INFO - 
[[ 8  3]
 [ 3 10]]
2025-08-09 22:44:51 - model_training - INFO - 
分类报告:
2025-08-09 22:44:51 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.73      0.73      0.73        11
           1       0.77      0.77      0.77        13

    accuracy                           0.75        24
   macro avg       0.75      0.75      0.75        24
weighted avg       0.75      0.75      0.75        24

2025-08-09 22:44:51 - model_training - INFO - 训练时间: 0.09 秒
2025-08-09 22:44:51 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.7500
2025-08-09 22:44:51 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 22:44:51 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-09 22:44:51 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-09 22:44:51 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-08-09 22:44:51 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-08-09 22:44:51 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-08-09 22:44:51 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-09 22:44:51 - model_ensemble - INFO -     voting_soft - 准确率: 0.8333, F1: 0.8333
2025-08-09 22:44:51 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-08-09 22:44:51 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-09 22:44:51 - model_ensemble - INFO -     voting_hard - 准确率: 0.7917, F1: 0.7920
2025-08-09 22:44:51 - model_ensemble - INFO - ============================================================
2025-08-09 22:44:51 - model_ensemble - INFO - 集成学习结果总结
2025-08-09 22:44:51 - model_ensemble - INFO - ============================================================
2025-08-09 22:44:51 - model_ensemble - INFO - 最佳集成模型: voting_soft
2025-08-09 22:44:51 - model_ensemble - INFO - 最佳F1分数: 0.8333
2025-08-09 22:44:51 - model_ensemble - INFO - 最佳准确率: 0.8333
2025-08-09 22:44:51 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-08-09 22:44:51 - model_ensemble - INFO -   voting_soft     - 准确率: 0.8333, 精确率: 0.8333, 召回率: 0.8333, F1: 0.8333, AUC: 0.9510
2025-08-09 22:44:51 - model_ensemble - INFO -   voting_hard     - 准确率: 0.7917, 精确率: 0.7951, 召回率: 0.7917, F1: 0.7920, AUC: 0.0000
