#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试KNN训练
"""

import sys
import numpy as np
from pathlib import Path

# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))

def quick_test():
    """快速测试KNN训练"""
    print("开始快速测试...")
    
    try:
        from sklearn.neighbors import KNeighborsClassifier
        from sklearn.pipeline import Pipeline
        from sklearn.preprocessing import StandardScaler
        from sklearn.datasets import make_classification
        
        # 创建简单的测试数据
        print("创建数据...")
        X, y = make_classification(n_samples=100, n_features=5, n_classes=2, random_state=42)
        
        print("创建Pipeline...")
        model = Pipeline([
            ('scaler', StandardScaler()),
            ('classifier', KNeighborsClassifier(
                n_neighbors=5,
                weights='uniform',
                algorithm='auto'
            ))
        ])
        
        print("开始训练...")
        model.fit(X, y)
        print("训练完成!")
        
        print("测试预测...")
        y_pred = model.predict(X[:5])
        print(f"预测结果: {y_pred}")
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    quick_test()