#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复GUI中的KNN挂起问题
"""

def fix_gui_hanging():
    """修复GUI中的潜在挂起问题"""
    
    # 读取gui_main.py
    gui_file = Path(__file__).parent / 'gui_main.py'
    if not gui_file.exists():
        print("❌ gui_main.py文件不存在")
        return
    
    with open(gui_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加matplotlib后端修复，防止Tkinter冲突
    matplotlib_fix = '''# 在文件开头导入部分添加matplotlib后端设置
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端，防止Tkinter冲突

'''
    
    # 检查是否已经添加了修复
    if 'matplotlib.use' in content:
        print("✅ matplotlib后端修复已存在")
    else:
        # 在第一个import之前添加修复
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('import ') and not line.startswith('import matplotlib'):
                lines.insert(i, matplotlib_fix.strip())
                break
        
        content = '\n'.join(lines)
        
        # 写回文件
        with open(gui_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 已添加matplotlib后端修复")

def add_timeout_to_knn():
    """为KNN训练添加超时保护"""
    
    model_file = Path(__file__).parent / 'code' / 'model_training.py'
    if not model_file.exists():
        print("❌ model_training.py文件不存在")
        return
    
    with open(model_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加超时装饰器
    timeout_code = '''
import signal
import functools

def timeout(seconds):
    """超时装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            def _handle_timeout(signum, frame):
                raise TimeoutError(f"操作超时（{seconds}秒）")
            
            # 设置信号处理器
            old_handler = signal.signal(signal.SIGALRM, _handle_timeout)
            signal.alarm(seconds)
            
            try:
                result = func(*args, **kwargs)
            finally:
                signal.alarm(0)  # 取消超时
                signal.signal(signal.SIGALRM, old_handler)  # 恢复原处理器
            
            return result
        return wrapper
    return decorator

'''
    
    # 检查是否已存在
    if 'def timeout(seconds):' in content:
        print("✅ 超时装饰器已存在")
    else:
        # 在logger导入后添加
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('logger = get_logger(__name__)'):
                lines.insert(i+1, timeout_code.strip())
                break
        
        content = '\n'.join(lines)
        
        # 为KNN的fit方法添加超时
        old_fit = '''        # 训练模型
        try:
            model.fit(X_train, y_train)'''
        
        new_fit = '''        # 训练模型
        try:
            # 为KNN添加超时保护
            if self.model_name == 'KNN':
                @timeout(10)  # 10秒超时
                def fit_with_timeout():
                    model.fit(X_train, y_train)
                fit_with_timeout()
            else:
                model.fit(X_train, y_train)'''
        
        content = content.replace(old_fit, new_fit)
        
        # 写回文件
        with open(model_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 已添加KNN超时保护")

def disable_duplicate_logging():
    """禁用重复日志"""
    
    logger_file = Path(__file__).parent / 'code' / 'logger.py'
    if not logger_file.exists():
        print("❌ logger.py文件不存在")
        return
    
    with open(logger_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修改get_logger函数以避免重复处理器
    old_check = '''    # 避免重复设置处理器
    if logger.handlers:
        return logger'''
    
    new_check = '''    # 避免重复设置处理器 - 强制清除现有处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)'''
    
    content = content.replace(old_check, new_check)
    
    # 写回文件
    with open(logger_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已修复重复日志问题")

if __name__ == "__main__":
    from pathlib import Path
    
    print("开始修复GUI挂起问题...\n")
    
    # 1. 修复matplotlib后端
    fix_gui_hanging()
    
    # 2. 添加KNN超时保护
    add_timeout_to_knn()
    
    # 3. 修复重复日志
    disable_duplicate_logging()
    
    print("\n🎉 修复完成！请重新启动GUI测试。")