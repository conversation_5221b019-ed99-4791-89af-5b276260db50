2025-08-29 02:00:39 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-29 02:00:39 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 02:00:40 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7523
2025-08-29 02:00:40 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 02:00:40 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-29 02:00:40 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-29 02:00:41 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-29 02:00:41 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-29 02:00:42 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-29 02:00:42 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-29 02:01:02 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8817
2025-08-29 02:01:02 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 02:01:03 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-29 02:01:03 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-29 02:01:03 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-29 02:01:03 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 02:01:04 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-29 02:01:04 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 02:01:05 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7523
2025-08-29 02:01:05 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 02:01:06 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-29 02:01:06 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-29 02:01:06 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-29 02:01:06 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-29 02:01:07 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-29 02:01:07 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-29 02:01:28 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8817
2025-08-29 02:01:28 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 02:01:29 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-29 02:01:29 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-29 02:01:30 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-29 02:01:30 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 02:42:33 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-29 02:42:33 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 02:42:34 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7523
2025-08-29 02:42:34 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 02:42:35 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-29 02:42:35 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-29 02:42:36 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-29 02:42:36 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-29 02:42:36 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-29 02:42:36 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-29 02:42:56 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8817
2025-08-29 02:42:56 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 02:42:57 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-29 02:42:57 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-29 02:42:58 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-29 02:42:58 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 03:28:29 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-29 03:28:29 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 03:28:30 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7523
2025-08-29 03:28:30 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 03:28:31 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-29 03:28:31 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-29 03:28:32 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-29 03:28:32 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-29 03:28:32 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-29 03:28:32 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-29 03:28:54 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8817
2025-08-29 03:28:54 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 03:28:55 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-29 03:28:55 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-29 03:28:56 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-29 03:28:56 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 03:34:43 - enhanced_ensemble_selector - INFO - 开始评估 4 个基模型...
2025-08-29 03:34:43 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 03:34:44 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8369
2025-08-29 03:34:44 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 03:34:45 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8796
2025-08-29 03:34:45 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 03:34:46 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.7779
2025-08-29 03:34:46 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.7870
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.211
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.267
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.844, 多样性=0.156
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.717, 多样性=0.283
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.791, 多样性=0.209
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.749, 多样性=0.251
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.880, 多样性=0.120
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   KNN vs Logistic: 相关性=0.739, 多样性=0.261
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.035
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.750
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 2, 'instance': 1, 'linear': 1}
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.352
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.129
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   熵多样性: 0.276
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'KNN', 'Logistic']
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 综合得分: 0.6756
2025-08-29 04:25:05 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-29 04:25:05 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 04:25:06 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7523
2025-08-29 04:25:06 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 04:25:06 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-29 04:25:06 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-29 04:25:07 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-29 04:25:07 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-29 04:25:08 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-29 04:25:08 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-29 04:25:14 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-29 04:25:14 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 04:25:15 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-29 04:25:15 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 04:25:15 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7523
2025-08-29 04:25:15 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 04:25:15 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-29 04:25:15 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 04:25:18 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7523
2025-08-29 04:25:18 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 04:25:19 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7523
2025-08-29 04:25:19 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 04:25:19 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-29 04:25:19 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-29 04:25:21 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-29 04:25:21 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-29 04:25:23 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-29 04:25:23 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-29 04:25:23 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-29 04:25:23 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-29 04:25:25 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-29 04:25:25 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-29 04:25:27 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-29 04:25:27 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-29 04:25:27 - enhanced_ensemble_selector - ERROR - 评估模型 CatBoost 失败: catboost/cuda/cuda_lib/devices_provider.h:190: Error: device already requested 0
2025-08-29 04:25:27 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 04:25:27 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-29 04:25:27 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-29 04:25:29 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-29 04:25:29 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-29 04:25:31 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-29 04:25:31 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-29 04:25:31 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-29 04:25:31 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-29 04:25:31 - enhanced_ensemble_selector - ERROR - 评估模型 CatBoost 失败: catboost/cuda/cuda_lib/devices_provider.h:190: Error: device already requested 0
2025-08-29 04:25:31 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 04:25:32 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8817
2025-08-29 04:25:32 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 04:25:35 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-29 04:25:35 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 04:25:35 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-29 04:25:35 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-29 04:25:35 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-29 04:25:35 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-29 04:25:38 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-29 04:25:38 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 04:25:38 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-29 04:25:38 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 04:25:50 - enhanced_ensemble_selector - WARNING - 模型 KNN 训练超时，跳过该模型
2025-08-29 04:25:50 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-08-29 04:25:51 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8664
2025-08-29 04:25:51 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8743
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.239
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.314
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.843, 多样性=0.157
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.891, 多样性=0.109
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.809, 多样性=0.191
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.778, 多样性=0.222
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.750, 多样性=0.250
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.714, 多样性=0.286
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.778, 多样性=0.222
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.916, 多样性=0.084
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.928, 多样性=0.072
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.943, 多样性=0.057
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.929, 多样性=0.071
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.868, 多样性=0.132
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.900, 多样性=0.100
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.927, 多样性=0.073
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.834, 多样性=0.166
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.810, 多样性=0.190
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.761, 多样性=0.239
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.784, 多样性=0.216
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.855, 多样性=0.145
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.833, 多样性=0.167
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.748, 多样性=0.252
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.806, 多样性=0.194
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.953, 多样性=0.047
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.824, 多样性=0.176
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.928, 多样性=0.072
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.876, 多样性=0.124
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.924, 多样性=0.076
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.851, 多样性=0.149
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.261
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.800
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 4, 'linear': 2, 'probabilistic': 1, 'neural': 1}
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.294
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.211
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO -   熵多样性: 0.244
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO - 从 8 个合格模型中选择 3 个进行集成
2025-08-29 04:25:52 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-08-29 04:25:53 - enhanced_ensemble_selector - WARNING - 模型 KNN 训练超时，跳过该模型
2025-08-29 04:25:53 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-08-29 04:25:53 - enhanced_ensemble_selector - WARNING - 模型 KNN 训练超时，跳过该模型
2025-08-29 04:25:53 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-08-29 04:25:55 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8817
2025-08-29 04:25:55 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 04:25:56 - enhanced_ensemble_selector - INFO - 多样性指标详细表格导出成功
2025-08-29 04:25:56 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-08-29 04:25:56 - enhanced_ensemble_selector - INFO -   最优组合: ['LightGBM', 'NaiveBayes', 'NeuralNet']
2025-08-29 04:25:56 - enhanced_ensemble_selector - INFO -   性能得分: 0.8799
2025-08-29 04:25:56 - enhanced_ensemble_selector - INFO -   多样性得分: 0.3619
2025-08-29 04:25:56 - enhanced_ensemble_selector - INFO -   综合得分: 0.6209
2025-08-29 04:25:56 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-08-29 04:25:57 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8664
2025-08-29 04:25:57 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-08-29 04:25:57 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8664
2025-08-29 04:25:57 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-08-29 04:25:58 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-29 04:25:58 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8743
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.239
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.314
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.843, 多样性=0.157
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.891, 多样性=0.109
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.809, 多样性=0.191
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.778, 多样性=0.222
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.750, 多样性=0.250
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.714, 多样性=0.286
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.778, 多样性=0.222
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.916, 多样性=0.084
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.928, 多样性=0.072
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.943, 多样性=0.057
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.929, 多样性=0.071
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.868, 多样性=0.132
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.900, 多样性=0.100
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.927, 多样性=0.073
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.834, 多样性=0.166
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.810, 多样性=0.190
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.761, 多样性=0.239
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.784, 多样性=0.216
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.855, 多样性=0.145
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.833, 多样性=0.167
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.748, 多样性=0.252
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.806, 多样性=0.194
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.953, 多样性=0.047
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.824, 多样性=0.176
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.928, 多样性=0.072
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.876, 多样性=0.124
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.924, 多样性=0.076
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.851, 多样性=0.149
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8743
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.261
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.800
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 4, 'linear': 2, 'probabilistic': 1, 'neural': 1}
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.239
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.276
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.843, 多样性=0.157
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.891, 多样性=0.109
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.809, 多样性=0.191
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.859, 多样性=0.141
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.778, 多样性=0.222
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.750, 多样性=0.250
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.714, 多样性=0.286
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.778, 多样性=0.222
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.916, 多样性=0.084
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.928, 多样性=0.072
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.946, 多样性=0.054
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.943, 多样性=0.057
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.929, 多样性=0.071
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.868, 多样性=0.132
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.900, 多样性=0.100
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.927, 多样性=0.073
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.961, 多样性=0.039
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.834, 多样性=0.166
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.810, 多样性=0.190
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.761, 多样性=0.239
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.784, 多样性=0.216
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.982, 多样性=0.018
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.855, 多样性=0.145
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.833, 多样性=0.167
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.748, 多样性=0.252
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.806, 多样性=0.194
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.875, 多样性=0.125
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.857, 多样性=0.143
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.768, 多样性=0.232
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.842, 多样性=0.158
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.953, 多样性=0.047
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.824, 多样性=0.176
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.928, 多样性=0.072
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.876, 多样性=0.124
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.924, 多样性=0.076
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.851, 多样性=0.149
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.215
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.700
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'probabilistic': 1, 'neural': 1}
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.294
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.211
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   熵多样性: 0.244
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 从 8 个合格模型中选择 3 个进行集成
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.287
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.218
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO -   熵多样性: 0.236
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 从 9 个合格模型中选择 3 个进行集成
2025-08-29 04:26:02 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-08-29 04:26:04 - enhanced_ensemble_selector - INFO - 多样性指标详细表格导出成功
2025-08-29 04:26:04 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-08-29 04:26:04 - enhanced_ensemble_selector - INFO -   最优组合: ['LightGBM', 'NaiveBayes', 'NeuralNet']
2025-08-29 04:26:04 - enhanced_ensemble_selector - INFO -   性能得分: 0.8799
2025-08-29 04:26:04 - enhanced_ensemble_selector - INFO -   多样性得分: 0.3619
2025-08-29 04:26:04 - enhanced_ensemble_selector - INFO -   综合得分: 0.6209
2025-08-29 04:26:04 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-08-29 04:26:06 - enhanced_ensemble_selector - INFO - 多样性指标详细表格导出成功
2025-08-29 04:26:06 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-08-29 04:26:06 - enhanced_ensemble_selector - INFO -   最优组合: ['LightGBM', 'NaiveBayes', 'NeuralNet']
2025-08-29 04:26:06 - enhanced_ensemble_selector - INFO -   性能得分: 0.8799
2025-08-29 04:26:06 - enhanced_ensemble_selector - INFO -   多样性得分: 0.3619
2025-08-29 04:26:06 - enhanced_ensemble_selector - INFO -   综合得分: 0.6209
2025-08-29 04:26:06 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-08-29 04:26:17 - enhanced_ensemble_selector - WARNING - 模型 KNN 训练超时，跳过该模型
2025-08-29 04:26:17 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-08-29 04:26:17 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8664
2025-08-29 04:26:17 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8743
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.239
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.276
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.843, 多样性=0.157
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.891, 多样性=0.109
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.809, 多样性=0.191
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.859, 多样性=0.141
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.778, 多样性=0.222
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.750, 多样性=0.250
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.714, 多样性=0.286
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.778, 多样性=0.222
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.916, 多样性=0.084
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.928, 多样性=0.072
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.946, 多样性=0.054
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.943, 多样性=0.057
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.929, 多样性=0.071
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.868, 多样性=0.132
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.900, 多样性=0.100
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.927, 多样性=0.073
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.961, 多样性=0.039
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.834, 多样性=0.166
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.810, 多样性=0.190
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.761, 多样性=0.239
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.784, 多样性=0.216
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.982, 多样性=0.018
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.855, 多样性=0.145
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.833, 多样性=0.167
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.748, 多样性=0.252
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.806, 多样性=0.194
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.875, 多样性=0.125
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.857, 多样性=0.143
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.768, 多样性=0.232
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.842, 多样性=0.158
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.953, 多样性=0.047
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.824, 多样性=0.176
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.928, 多样性=0.072
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.876, 多样性=0.124
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.924, 多样性=0.076
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.851, 多样性=0.149
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.215
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.700
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'probabilistic': 1, 'neural': 1}
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.287
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.218
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO -   熵多样性: 0.236
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO - 从 9 个合格模型中选择 3 个进行集成
2025-08-29 04:26:18 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-08-29 04:26:19 - enhanced_ensemble_selector - INFO - 多样性指标详细表格导出成功
2025-08-29 04:26:19 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-08-29 04:26:19 - enhanced_ensemble_selector - INFO -   最优组合: ['LightGBM', 'NaiveBayes', 'NeuralNet']
2025-08-29 04:26:19 - enhanced_ensemble_selector - INFO -   性能得分: 0.8799
2025-08-29 04:26:19 - enhanced_ensemble_selector - INFO -   多样性得分: 0.3619
2025-08-29 04:26:19 - enhanced_ensemble_selector - INFO -   综合得分: 0.6209
2025-08-29 04:26:19 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-08-29 04:29:40 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-29 04:29:40 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 04:29:41 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7523
2025-08-29 04:29:41 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 04:29:42 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-29 04:29:42 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-29 04:29:43 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-29 04:29:43 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-29 04:29:43 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-29 04:29:43 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-29 04:30:04 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8817
2025-08-29 04:30:04 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 04:30:05 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-29 04:30:05 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-29 04:30:06 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-29 04:30:06 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 04:30:21 - enhanced_ensemble_selector - WARNING - 模型 KNN 训练超时，跳过该模型
2025-08-29 04:30:21 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-08-29 04:30:22 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8664
2025-08-29 04:30:22 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8743
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.239
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.276
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.843, 多样性=0.157
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.891, 多样性=0.109
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.809, 多样性=0.191
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.859, 多样性=0.141
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.778, 多样性=0.222
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.750, 多样性=0.250
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.714, 多样性=0.286
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.778, 多样性=0.222
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.916, 多样性=0.084
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.928, 多样性=0.072
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.946, 多样性=0.054
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.943, 多样性=0.057
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.929, 多样性=0.071
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.868, 多样性=0.132
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.900, 多样性=0.100
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.927, 多样性=0.073
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.961, 多样性=0.039
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.834, 多样性=0.166
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.810, 多样性=0.190
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.761, 多样性=0.239
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.784, 多样性=0.216
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.982, 多样性=0.018
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.855, 多样性=0.145
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.833, 多样性=0.167
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.748, 多样性=0.252
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.806, 多样性=0.194
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.875, 多样性=0.125
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.857, 多样性=0.143
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.768, 多样性=0.232
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.842, 多样性=0.158
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.953, 多样性=0.047
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.824, 多样性=0.176
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.928, 多样性=0.072
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.876, 多样性=0.124
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.924, 多样性=0.076
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.851, 多样性=0.149
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.215
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.700
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'probabilistic': 1, 'neural': 1}
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.287
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.218
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO -   熵多样性: 0.236
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO - 从 9 个合格模型中选择 2 个进行集成
2025-08-29 04:30:23 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-08-29 04:30:24 - enhanced_ensemble_selector - INFO - 多样性指标详细表格导出成功
2025-08-29 04:30:24 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-08-29 04:30:24 - enhanced_ensemble_selector - INFO -   最优组合: ['LightGBM', 'NeuralNet']
2025-08-29 04:30:24 - enhanced_ensemble_selector - INFO -   性能得分: 0.8866
2025-08-29 04:30:24 - enhanced_ensemble_selector - INFO -   多样性得分: 0.4096
2025-08-29 04:30:24 - enhanced_ensemble_selector - INFO -   综合得分: 0.6481
2025-08-29 04:30:24 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-08-29 04:33:09 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-29 04:33:09 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 04:33:10 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7523
2025-08-29 04:33:10 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 04:33:11 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-29 04:33:11 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-29 04:33:11 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-29 04:33:11 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-29 04:33:12 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-29 04:33:12 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-29 04:33:34 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8817
2025-08-29 04:33:34 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 04:33:35 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-29 04:33:35 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-29 04:33:35 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-29 04:33:35 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 04:33:50 - enhanced_ensemble_selector - WARNING - 模型 KNN 训练超时，跳过该模型
2025-08-29 04:33:50 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-08-29 04:33:51 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8664
2025-08-29 04:33:51 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8743
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.239
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.276
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.843, 多样性=0.157
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.891, 多样性=0.109
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.809, 多样性=0.191
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.859, 多样性=0.141
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.778, 多样性=0.222
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.750, 多样性=0.250
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.714, 多样性=0.286
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.778, 多样性=0.222
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.916, 多样性=0.084
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.928, 多样性=0.072
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.946, 多样性=0.054
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.943, 多样性=0.057
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.929, 多样性=0.071
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.868, 多样性=0.132
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.900, 多样性=0.100
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.927, 多样性=0.073
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.961, 多样性=0.039
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.834, 多样性=0.166
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.810, 多样性=0.190
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.761, 多样性=0.239
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.784, 多样性=0.216
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.982, 多样性=0.018
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.855, 多样性=0.145
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.833, 多样性=0.167
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.748, 多样性=0.252
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.806, 多样性=0.194
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.875, 多样性=0.125
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.857, 多样性=0.143
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.768, 多样性=0.232
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.842, 多样性=0.158
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.953, 多样性=0.047
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.824, 多样性=0.176
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.928, 多样性=0.072
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.876, 多样性=0.124
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.924, 多样性=0.076
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.851, 多样性=0.149
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.215
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.700
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'probabilistic': 1, 'neural': 1}
2025-08-29 04:33:52 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-08-29 04:33:53 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-08-29 04:33:53 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.287
2025-08-29 04:33:53 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.218
2025-08-29 04:33:53 - enhanced_ensemble_selector - INFO -   熵多样性: 0.236
2025-08-29 04:33:53 - enhanced_ensemble_selector - INFO - 从 9 个合格模型中选择 4 个进行集成
2025-08-29 04:33:53 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-08-29 04:33:55 - enhanced_ensemble_selector - INFO - 多样性指标详细表格导出成功
2025-08-29 04:33:55 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-08-29 04:33:55 - enhanced_ensemble_selector - INFO -   最优组合: ['XGBoost', 'LightGBM', 'NaiveBayes', 'NeuralNet']
2025-08-29 04:33:55 - enhanced_ensemble_selector - INFO -   性能得分: 0.8740
2025-08-29 04:33:55 - enhanced_ensemble_selector - INFO -   多样性得分: 0.3417
2025-08-29 04:33:55 - enhanced_ensemble_selector - INFO -   综合得分: 0.6078
2025-08-29 04:33:55 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
2025-08-29 04:34:58 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-29 04:34:58 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 04:34:58 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7523
2025-08-29 04:34:58 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 04:34:59 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-29 04:34:59 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-29 04:35:00 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-29 04:35:00 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-29 04:35:01 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-29 04:35:01 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-29 04:35:22 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8817
2025-08-29 04:35:22 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 04:35:23 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-29 04:35:23 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-29 04:35:23 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-29 04:35:23 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 04:35:39 - enhanced_ensemble_selector - WARNING - 模型 KNN 训练超时，跳过该模型
2025-08-29 04:35:39 - enhanced_ensemble_selector - INFO -   评估模型: NaiveBayes
2025-08-29 04:35:39 - enhanced_ensemble_selector - INFO -     NaiveBayes - 性能得分: 0.8664
2025-08-29 04:35:39 - enhanced_ensemble_selector - INFO -   评估模型: NeuralNet
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -     NeuralNet - 性能得分: 0.8743
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.239
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.276
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.843, 多样性=0.157
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   DecisionTree vs XGBoost: 相关性=0.891, 多样性=0.109
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   DecisionTree vs LightGBM: 相关性=0.809, 多样性=0.191
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   DecisionTree vs CatBoost: 相关性=0.859, 多样性=0.141
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.778, 多样性=0.222
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   DecisionTree vs SVM: 相关性=0.750, 多样性=0.250
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   DecisionTree vs NaiveBayes: 相关性=0.714, 多样性=0.286
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   DecisionTree vs NeuralNet: 相关性=0.778, 多样性=0.222
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   RandomForest vs XGBoost: 相关性=0.916, 多样性=0.084
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   RandomForest vs LightGBM: 相关性=0.928, 多样性=0.072
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   RandomForest vs CatBoost: 相关性=0.946, 多样性=0.054
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.943, 多样性=0.057
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   RandomForest vs SVM: 相关性=0.929, 多样性=0.071
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   RandomForest vs NaiveBayes: 相关性=0.868, 多样性=0.132
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   RandomForest vs NeuralNet: 相关性=0.900, 多样性=0.100
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   XGBoost vs LightGBM: 相关性=0.927, 多样性=0.073
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   XGBoost vs CatBoost: 相关性=0.961, 多样性=0.039
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   XGBoost vs Logistic: 相关性=0.834, 多样性=0.166
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   XGBoost vs SVM: 相关性=0.810, 多样性=0.190
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   XGBoost vs NaiveBayes: 相关性=0.761, 多样性=0.239
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   XGBoost vs NeuralNet: 相关性=0.784, 多样性=0.216
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   LightGBM vs CatBoost: 相关性=0.982, 多样性=0.018
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   LightGBM vs Logistic: 相关性=0.855, 多样性=0.145
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   LightGBM vs SVM: 相关性=0.833, 多样性=0.167
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   LightGBM vs NaiveBayes: 相关性=0.748, 多样性=0.252
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   LightGBM vs NeuralNet: 相关性=0.806, 多样性=0.194
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   CatBoost vs Logistic: 相关性=0.875, 多样性=0.125
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   CatBoost vs SVM: 相关性=0.857, 多样性=0.143
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   CatBoost vs NaiveBayes: 相关性=0.768, 多样性=0.232
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   CatBoost vs NeuralNet: 相关性=0.842, 多样性=0.158
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   Logistic vs SVM: 相关性=0.953, 多样性=0.047
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   Logistic vs NaiveBayes: 相关性=0.824, 多样性=0.176
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   Logistic vs NeuralNet: 相关性=0.928, 多样性=0.072
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   SVM vs NaiveBayes: 相关性=0.876, 多样性=0.124
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   SVM vs NeuralNet: 相关性=0.924, 多样性=0.076
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO -   NaiveBayes vs NeuralNet: 相关性=0.851, 多样性=0.149
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.215
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.700
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 5, 'linear': 2, 'probabilistic': 1, 'neural': 1}
2025-08-29 04:35:40 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-08-29 04:35:41 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-08-29 04:35:41 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.287
2025-08-29 04:35:41 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.218
2025-08-29 04:35:41 - enhanced_ensemble_selector - INFO -   熵多样性: 0.236
2025-08-29 04:35:41 - enhanced_ensemble_selector - INFO - 从 9 个合格模型中选择 5 个进行集成
2025-08-29 04:35:41 - enhanced_ensemble_selector - INFO - 使用量化多样性评估进行模型选择...
2025-08-29 04:35:44 - enhanced_ensemble_selector - INFO - 多样性指标详细表格导出成功
2025-08-29 04:35:44 - enhanced_ensemble_selector - INFO - 量化评估选择完成:
2025-08-29 04:35:44 - enhanced_ensemble_selector - INFO -   最优组合: ['XGBoost', 'LightGBM', 'SVM', 'NaiveBayes', 'NeuralNet']
2025-08-29 04:35:44 - enhanced_ensemble_selector - INFO -   性能得分: 0.8699
2025-08-29 04:35:44 - enhanced_ensemble_selector - INFO -   多样性得分: 0.3236
2025-08-29 04:35:44 - enhanced_ensemble_selector - INFO -   综合得分: 0.5968
2025-08-29 04:35:44 - enhanced_ensemble_selector - INFO -   多样性等级: 中等的多样性
