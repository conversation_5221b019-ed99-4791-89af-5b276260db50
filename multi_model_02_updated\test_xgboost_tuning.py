#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试XGBoost超参数调优
"""

import numpy as np
import sys
from pathlib import Path

# 添加代码目录
sys.path.insert(0, str(Path(__file__).parent / 'code'))

from hyperparameter_tuning import tune_model
from config import RANDOM_SEED

def test_xgboost_tuning():
    """测试XGBoost超参数调优"""
    print("测试XGBoost超参数调优...")
    
    # 生成测试数据
    np.random.seed(RANDOM_SEED)
    X = np.random.rand(100, 10)
    y = np.random.randint(0, 2, 100)
    
    print(f"数据形状: X={X.shape}, y={y.shape}")
    print(f"类别分布: {np.bincount(y)}")
    
    try:
        # 运行超参数调优
        best_params, best_score = tune_model(
            'XGBoost',
            n_trials=5,
            X_train=X,
            y_train=y,
            scoring='roc_auc',
            early_stopping_rounds=5,
            timeout=60
        )
        
        print(f"\nXGBoost调优结果:")
        print(f"最佳参数: {best_params}")
        print(f"最佳得分: {best_score:.4f}")
        
        if best_score > 0:
            print("✓ XGBoost超参数调优成功！")
        else:
            print("✗ XGBoost调优得分为0，可能存在问题")
            
        return best_score > 0
        
    except Exception as e:
        print(f"✗ XGBoost调优失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_xgboost_tuning()