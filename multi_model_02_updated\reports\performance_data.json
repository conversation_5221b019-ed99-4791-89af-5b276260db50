{"generation_time": "2025-08-29T01:58:03.316023", "best_model": "NeuralNet", "best_score": 0.9198999784967485, "model_count": 10, "detailed_metrics": {"KNN": {"accuracy": 0.825, "precision": 0.9166666666666666, "recall": 0.6470588235294118, "f1_score": 0.7586206896551724, "specificity": 0.9565217391304348, "sensitivity": 0.6470588235294118, "npv": 0.7857142857142857, "ppv": 0.9166666666666666, "auc_roc": 0.9437340153452685, "auc_roc_ci": "(0.860, 0.997)", "auc_roc_ci_lower": 0.8601072994987469, "auc_roc_ci_upper": 0.9973360613810741, "auc_pr": 0.9273964271023096, "mcc": 0.6511094304643837, "kappa": 0.6276595744680851, "balanced_accuracy": 0.8017902813299234}, "Logistic": {"accuracy": 0.925, "precision": 0.8888888888888888, "recall": 0.9411764705882353, "f1_score": 0.9142857142857143, "specificity": 0.9130434782608695, "sensitivity": 0.9411764705882353, "npv": 0.9545454545454546, "ppv": 0.8888888888888888, "auc_roc": 0.969309462915601, "auc_roc_ci": "(0.912, 1.000)", "auc_roc_ci_lower": 0.9116161616161617, "auc_roc_ci_upper": 1.0, "auc_pr": 0.9654652056901192, "mcc": 0.8488100150835068, "kappa": 0.8477157360406091, "balanced_accuracy": 0.9271099744245523}, "LightGBM": {"accuracy": 0.9, "precision": 0.8823529411764706, "recall": 0.8823529411764706, "f1_score": 0.8823529411764706, "specificity": 0.9130434782608695, "sensitivity": 0.8823529411764706, "npv": 0.9130434782608695, "ppv": 0.8823529411764706, "auc_roc": 0.959079283887468, "auc_roc_ci": "(0.887, 1.000)", "auc_roc_ci_lower": 0.8874617810510933, "auc_roc_ci_upper": 1.0, "auc_pr": 0.9353312873209068, "mcc": 0.7953964194373402, "kappa": 0.7953964194373402, "balanced_accuracy": 0.8976982097186701}, "NaiveBayes": {"accuracy": 0.8, "precision": 0.8461538461538461, "recall": 0.6470588235294118, "f1_score": 0.7333333333333333, "specificity": 0.9130434782608695, "sensitivity": 0.6470588235294118, "npv": 0.7777777777777778, "ppv": 0.8461538461538461, "auc_roc": 0.9309462915601023, "auc_roc_ci": "(0.839, 0.990)", "auc_roc_ci_lower": 0.8392631673881674, "auc_roc_ci_upper": 0.9899008885850991, "auc_pr": 0.9144649134632745, "mcc": 0.5911561035156879, "kappa": 0.5778364116094987, "balanced_accuracy": 0.7800511508951407}, "CatBoost": {"accuracy": 0.9, "precision": 0.8823529411764706, "recall": 0.8823529411764706, "f1_score": 0.8823529411764706, "specificity": 0.9130434782608695, "sensitivity": 0.8823529411764706, "npv": 0.9130434782608695, "ppv": 0.8823529411764706, "auc_roc": 0.9641943734015345, "auc_roc_ci": "(0.899, 1.000)", "auc_roc_ci_lower": 0.8986310776942356, "auc_roc_ci_upper": 1.0, "auc_pr": 0.9461880861808014, "mcc": 0.7953964194373402, "kappa": 0.7953964194373402, "balanced_accuracy": 0.8976982097186701}, "SVM": {"accuracy": 0.9, "precision": 0.8823529411764706, "recall": 0.8823529411764706, "f1_score": 0.8823529411764706, "specificity": 0.9130434782608695, "sensitivity": 0.8823529411764706, "npv": 0.9130434782608695, "ppv": 0.8823529411764706, "auc_roc": 0.9744245524296675, "auc_roc_ci": "(0.915, 1.000)", "auc_roc_ci_lower": 0.9147708285866181, "auc_roc_ci_upper": 1.0, "auc_pr": 0.974640522875817, "mcc": 0.7953964194373402, "kappa": 0.7953964194373402, "balanced_accuracy": 0.8976982097186701}, "NeuralNet": {"accuracy": 0.925, "precision": 0.8888888888888888, "recall": 0.9411764705882353, "f1_score": 0.9142857142857143, "specificity": 0.9130434782608695, "sensitivity": 0.9411764705882353, "npv": 0.9545454545454546, "ppv": 0.8888888888888888, "auc_roc": 0.9820971867007672, "auc_roc_ci": "(0.933, 1.000)", "auc_roc_ci_lower": 0.9334616949546617, "auc_roc_ci_upper": 1.0, "auc_pr": 0.9811945238453438, "mcc": 0.8488100150835068, "kappa": 0.8477157360406091, "balanced_accuracy": 0.9271099744245523}, "RandomForest": {"accuracy": 0.825, "precision": 0.8571428571428571, "recall": 0.7058823529411765, "f1_score": 0.7741935483870968, "specificity": 0.9130434782608695, "sensitivity": 0.7058823529411765, "npv": 0.8076923076923077, "ppv": 0.8571428571428571, "auc_roc": 0.9539641943734015, "auc_roc_ci": "(0.880, 1.000)", "auc_roc_ci_lower": 0.8797953964194373, "auc_roc_ci_upper": 1.0, "auc_pr": 0.9323532762387252, "mcc": 0.6414699190203339, "kappa": 0.6335078534031413, "balanced_accuracy": 0.809462915601023}, "XGBoost": {"accuracy": 0.9, "precision": 0.9333333333333333, "recall": 0.8235294117647058, "f1_score": 0.875, "specificity": 0.9565217391304348, "sensitivity": 0.8235294117647058, "npv": 0.88, "ppv": 0.9333333333333333, "auc_roc": 0.9616368286445013, "auc_roc_ci": "(0.893, 1.000)", "auc_roc_ci_lower": 0.8925352088661552, "auc_roc_ci_upper": 1.0, "auc_pr": 0.9396521384683831, "mcc": 0.7965184258559546, "kappa": 0.7922077922077921, "balanced_accuracy": 0.8900255754475703}, "DecisionTree": {"accuracy": 0.85, "precision": 0.9230769230769231, "recall": 0.7058823529411765, "f1_score": 0.8, "specificity": 0.9565217391304348, "sensitivity": 0.7058823529411765, "npv": 0.8148148148148148, "ppv": 0.9230769230769231, "auc_roc": 0.9322250639386189, "auc_roc_ci": "(0.838, 0.991)", "auc_roc_ci_lower": 0.8383273889852837, "auc_roc_ci_upper": 0.990893983004386, "auc_pr": 0.8633621280680104, "mcc": 0.6991298210527999, "kappa": 0.683377308707124, "balanced_accuracy": 0.8312020460358056}}, "ranking": [["NeuralNet", 0.9198999784967485], ["Logistic", 0.916703047550457], ["SVM", 0.8906265984654732], ["CatBoost", 0.8880690537084399], ["LightGBM", 0.8867902813299233], ["XGBoost", 0.8853991668421203], ["DecisionTree", 0.8232781577970246], ["RandomForest", 0.8016762630832399], ["KNN", 0.7980020768798363], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", 0.7669557272281556]]}