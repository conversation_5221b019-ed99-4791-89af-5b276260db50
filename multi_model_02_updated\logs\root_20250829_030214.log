2025-08-29 03:02:14 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-29 03:02:14 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 03:02:14 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 03:02:15 - data_exploration - INFO - 数据探索器初始化完成，输出目录: d:\Code\MM01U\multi_model_02_updated\output\data_exploration
2025-08-29 03:02:15 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-29 03:02:15 - GUI - INFO - GUI界面初始化完成
2025-08-29 03:02:43 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 16)
2025-08-29 03:02:43 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800}
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9655
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9655
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9655
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9655
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9655
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 7, 'min_samples_split': 18, 'min_samples_leaf': 7, 'criterion': 'entropy', 'class_weight': None, 'max_features': None}
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9655
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\optimization_history_20250829_030243.html
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\param_importances_20250829_030243.html
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.55 秒
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800}
2025-08-29 03:02:45 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9727
2025-08-29 03:02:48 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9742
2025-08-29 03:02:48 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9776
2025-08-29 03:02:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:02:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9776
2025-08-29 03:02:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:02:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9776
2025-08-29 03:02:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:02:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9776
2025-08-29 03:02:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:02:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9776
2025-08-29 03:02:57 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 153, 'max_depth': 26, 'min_samples_split': 16, 'min_samples_leaf': 1, 'max_features': 'sqrt'}
2025-08-29 03:02:57 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9776
2025-08-29 03:02:57 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/50
2025-08-29 03:02:57 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:02:57 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\optimization_history_20250829_030257.html
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\param_importances_20250829_030258.html
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 14.21 秒
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9788
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:02:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:02:59 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9820
2025-08-29 03:02:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:02:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:02:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:02:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:03:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:03:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:03:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:03:00 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9820
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 247, 'max_depth': 3, 'learning_rate': 0.15912798713994736, 'subsample': 0.7962072844310213, 'colsample_bytree': 0.5232252063599989}
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9820
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\optimization_history_20250829_030301.html
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\param_importances_20250829_030301.html
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.83 秒
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-29 03:03:01 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9819
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9828
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 55, 'max_depth': 10, 'learning_rate': 0.2514083658321223, 'feature_fraction': 0.6061695553391381, 'bagging_fraction': 0.5909124836035503}
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9828
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\optimization_history_20250829_030302.html
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\param_importances_20250829_030302.html
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.95 秒
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-29 03:03:02 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:03:07 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9663
2025-08-29 03:03:07 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:03:08 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9822
2025-08-29 03:03:08 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:03:10 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:03:12 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9871
2025-08-29 03:03:12 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:03:16 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:03:20 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:03:23 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:03:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:03:29 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:03:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:03:36 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:03:39 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:03:41 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9871
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 96, 'depth': 4, 'learning_rate': 0.16217936517334897, 'l2_leaf_reg': 4.887505167779041, 'bagging_temperature': 0.2912291401980419}
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9871
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\optimization_history_20250829_030343.html
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\param_importances_20250829_030343.html
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 41.08 秒
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:03:43 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9503
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9503
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9503
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9503
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - Trial 14: 发现更好的得分 0.9519
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - Trial 15: 发现更好的得分 0.9542
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 0.37205286378505253, 'clf__solver': 'lbfgs'}
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9542
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\optimization_history_20250829_030344.html
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\param_importances_20250829_030344.html
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.66 秒
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9479
2025-08-29 03:03:44 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9692
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.9717
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9717
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9717
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9717
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9717
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9717
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9717
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 2.6712172157398797, 'clf__kernel': 'rbf'}
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9717
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 实际执行试验次数: 25/50
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\optimization_history_20250829_030345.html
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\param_importances_20250829_030345.html
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.99 秒
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:03:45 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-29 03:04:15 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 7, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-29 03:04:15 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.5000
2025-08-29 03:04:15 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9527
2025-08-29 03:04:45 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'distance', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 03:05:15 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 13, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-29 03:05:45 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 3, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 03:06:15 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 03:06:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:06:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9527
2025-08-29 03:06:15 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 7, 'clf__weights': 'distance', 'clf__algorithm': 'ball_tree', 'clf__p': 2}
2025-08-29 03:06:15 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9527
2025-08-29 03:06:15 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-29 03:06:15 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:06:15 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\optimization_history_20250829_030615.html
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\param_importances_20250829_030616.html
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 150.43 秒
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9369
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:06:16 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-29 03:06:21 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9726
2025-08-29 03:06:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:06:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9726
2025-08-29 03:06:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:06:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9726
2025-08-29 03:06:30 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:06:30 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9726
2025-08-29 03:06:30 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:06:30 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9726
2025-08-29 03:06:30 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:06:30 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9726
2025-08-29 03:06:31 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:06:31 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9726
2025-08-29 03:06:31 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.00286486058930544}
2025-08-29 03:06:31 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9726
2025-08-29 03:06:31 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-29 03:06:31 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:06:31 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\optimization_history_20250829_030631.html
2025-08-29 03:06:31 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\param_importances_20250829_030631.html
2025-08-29 03:06:31 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 15.07 秒
2025-08-29 03:07:17 - training_session_manager - INFO - 创建会话目录结构: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_030717
2025-08-29 03:07:17 - training_session_manager - INFO - 创建训练会话: 训练_N-2_20250829_030717 (ID: 20250829_030717)
2025-08-29 03:07:17 - training_session_manager - INFO - 创建新会话: 训练_N-2_20250829_030717
2025-08-29 03:07:17 - session_utils - INFO - 创建新会话: 训练_N-2_20250829_030717 (ID: 20250829_030717)
2025-08-29 03:07:17 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 16)
2025-08-29 03:07:17 - data_preprocessing - INFO - 应用了 standard 特征缩放
