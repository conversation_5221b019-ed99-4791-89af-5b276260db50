#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证KNN模型训练和ensemble selector的修复
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path

# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))

def test_knn_training():
    """测试KNN模型训练"""
    print("=== 测试KNN模型训练 ===")
    
    try:
        from model_training import MODEL_TRAINERS
        from sklearn.datasets import make_classification
        from sklearn.model_selection import train_test_split
        
        # 创建测试数据
        X, y = make_classification(n_samples=200, n_features=10, n_classes=2, random_state=42)
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        print(f"训练数据形状: {X_train.shape}")
        print(f"测试数据形状: {X_test.shape}")
        
        # 测试KNN训练
        print("\n开始训练KNN模型...")
        trainer = MODEL_TRAINERS['KNN']
        model = trainer.train_and_evaluate(X_train, y_train, X_test, y_test)
        
        print("✅ KNN模型训练成功!")
        print(f"模型类型: {type(model)}")
        
        # 测试预测
        y_pred = model.predict(X_test)
        print(f"预测结果形状: {y_pred.shape}")
        print(f"预测准确率: {np.mean(y_pred == y_test):.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ KNN训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ensemble_selector():
    """测试Ensemble Selector"""
    print("\n=== 测试Ensemble Selector ===")
    
    try:
        from enhanced_ensemble_selector import EnhancedEnsembleSelector
        from sklearn.datasets import make_classification
        from sklearn.model_selection import train_test_split
        
        # 创建测试数据
        X, y = make_classification(n_samples=200, n_features=10, n_classes=2, random_state=42)
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 创建选择器
        selector = EnhancedEnsembleSelector()
        
        # 测试基模型评估
        print("\n开始评估基模型...")
        model_results = selector.evaluate_base_models(
            X_train, y_train, X_test, y_test, 
            model_names=['DecisionTree', 'RandomForest', 'KNN', 'Logistic']
        )
        
        print(f"✅ 成功评估 {len(model_results)} 个模型")
        for name, result in model_results.items():
            print(f"  - {name}: 性能得分 = {result['performance_score']:.4f}")
        
        # 测试模型选择
        print("\n开始选择最优组合...")
        selection_result = selector.select_optimal_ensemble(
            target_size=3, 
            strategy='balanced'
        )
        
        if selection_result:
            print("✅ 模型选择成功!")
            print(f"选中模型: {selection_result['selected_models']}")
            print(f"选择策略: {selection_result['selection_strategy']}")
        else:
            print("❌ 模型选择失败")
            
        return True
        
    except Exception as e:
        print(f"❌ Ensemble Selector测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_quantified_strategy():
    """测试量化策略"""
    print("\n=== 测试Quantified策略 ===")
    
    try:
        from enhanced_ensemble_selector import EnhancedEnsembleSelector
        from sklearn.datasets import make_classification
        from sklearn.model_selection import train_test_split
        
        # 创建测试数据
        X, y = make_classification(n_samples=200, n_features=10, n_classes=2, random_state=42)
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # 创建选择器
        selector = EnhancedEnsembleSelector()
        
        # 评估基模型
        model_results = selector.evaluate_base_models(
            X_train, y_train, X_test, y_test,
            model_names=['DecisionTree', 'RandomForest', 'KNN', 'Logistic', 'SVM']
        )
        
        # 使用量化策略选择
        print("\n使用quantified策略选择模型...")
        selection_result = selector.select_optimal_ensemble(
            target_size=3,
            strategy='quantified'
        )
        
        if selection_result:
            print("✅ Quantified策略成功!")
            print(f"选中模型: {selection_result['selected_models']}")
            
            # 检查是否有量化评估结果
            if hasattr(selector, 'quantified_diversity_results') and selector.quantified_diversity_results:
                print("✅ 量化多样性评估结果存在")
                overall_stats = selector.quantified_diversity_results.get('overall_statistics', {})
                print(f"平均多样性: {overall_stats.get('mean_diversity', 'N/A')}")
            else:
                print("⚠️ 量化多样性评估结果不存在")
        else:
            print("❌ Quantified策略失败")
            
        return True
        
    except Exception as e:
        print(f"❌ Quantified策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试修复后的代码...\n")
    
    # 运行测试
    test1 = test_knn_training()
    test2 = test_ensemble_selector()
    test3 = test_quantified_strategy()
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print(f"KNN模型训练: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"Ensemble Selector: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"Quantified策略: {'✅ 通过' if test3 else '❌ 失败'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 所有测试通过！修复成功！")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")