2025-08-29 03:34:43 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-29 03:34:43 - enhanced_ensemble_selector - INFO - 开始评估 4 个基模型...
2025-08-29 03:34:43 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 03:34:44 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 03:34:44 - model_training - INFO - 准确率: 0.8250
2025-08-29 03:34:44 - model_training - INFO - AUC: 0.8646
2025-08-29 03:34:44 - model_training - INFO - AUPRC: 0.9087
2025-08-29 03:34:44 - model_training - INFO - 混淆矩阵:
2025-08-29 03:34:44 - model_training - INFO - 
[[14  2]
 [ 5 19]]
2025-08-29 03:34:44 - model_training - INFO - 
分类报告:
2025-08-29 03:34:44 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.74      0.88      0.80        16
           1       0.90      0.79      0.84        24

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 03:34:44 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 03:34:44 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8250
2025-08-29 03:34:44 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 03:34:44 - model_training - INFO - 特征名称已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_feature_names.joblib
2025-08-29 03:34:44 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.8369
2025-08-29 03:34:44 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 03:34:45 - model_training - INFO - 模型名称: Random Forest
2025-08-29 03:34:45 - model_training - INFO - 准确率: 0.8500
2025-08-29 03:34:45 - model_training - INFO - AUC: 0.9570
2025-08-29 03:34:45 - model_training - INFO - AUPRC: 0.9728
2025-08-29 03:34:45 - model_training - INFO - 混淆矩阵:
2025-08-29 03:34:45 - model_training - INFO - 
[[15  1]
 [ 5 19]]
2025-08-29 03:34:45 - model_training - INFO - 
分类报告:
2025-08-29 03:34:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.75      0.94      0.83        16
           1       0.95      0.79      0.86        24

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 03:34:45 - model_training - INFO - 训练时间: 0.19 秒
2025-08-29 03:34:45 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 03:34:45 - model_training - INFO - 模型 RandomForest 的结果已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 03:34:45 - model_training - INFO - 特征名称已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\RandomForest_feature_names.joblib
2025-08-29 03:34:45 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8796
2025-08-29 03:34:45 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 03:34:45 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 03:34:45 - model_training - INFO - 模型名称: KNN
2025-08-29 03:34:45 - model_training - INFO - 准确率: 0.7500
2025-08-29 03:34:45 - model_training - INFO - AUC: 0.8490
2025-08-29 03:34:45 - model_training - INFO - AUPRC: 0.8751
2025-08-29 03:34:45 - model_training - INFO - 混淆矩阵:
2025-08-29 03:34:45 - model_training - INFO - 
[[13  3]
 [ 7 17]]
2025-08-29 03:34:45 - model_training - INFO - 
分类报告:
2025-08-29 03:34:45 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.65      0.81      0.72        16
           1       0.85      0.71      0.77        24

    accuracy                           0.75        40
   macro avg       0.75      0.76      0.75        40
weighted avg       0.77      0.75      0.75        40

2025-08-29 03:34:45 - model_training - INFO - 训练时间: 0.10 秒
2025-08-29 03:34:45 - model_training - INFO - 模型 KNN 性能: 准确率=0.7500
2025-08-29 03:34:45 - model_training - INFO - 模型 KNN 的结果已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\KNN_results.joblib
2025-08-29 03:34:45 - model_training - INFO - 特征名称已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\KNN_feature_names.joblib
2025-08-29 03:34:46 - enhanced_ensemble_selector - INFO -     KNN - 性能得分: 0.7779
2025-08-29 03:34:46 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 03:34:46 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 03:34:46 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 03:34:46 - model_training - INFO - 准确率: 0.7500
2025-08-29 03:34:46 - model_training - INFO - AUC: 0.8854
2025-08-29 03:34:46 - model_training - INFO - AUPRC: 0.9181
2025-08-29 03:34:46 - model_training - INFO - 混淆矩阵:
2025-08-29 03:34:46 - model_training - INFO - 
[[13  3]
 [ 7 17]]
2025-08-29 03:34:46 - model_training - INFO - 
分类报告:
2025-08-29 03:34:46 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.65      0.81      0.72        16
           1       0.85      0.71      0.77        24

    accuracy                           0.75        40
   macro avg       0.75      0.76      0.75        40
weighted avg       0.77      0.75      0.75        40

2025-08-29 03:34:46 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 03:34:46 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7500
2025-08-29 03:34:46 - model_training - INFO - 模型 Logistic 的结果已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 03:34:46 - model_training - INFO - 特征名称已缓存到: D:\Code\MM01U\multi_model_02_updated\cache\Logistic_feature_names.joblib
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.7870
2025-08-29 03:34:47 - improved_diversity_analyzer - INFO - 开始综合多样性分析...
2025-08-29 03:34:47 - improved_diversity_analyzer - INFO - 数据复杂度得分: 0.211
2025-08-29 03:34:47 - improved_diversity_analyzer - INFO - 调整后相关性阈值: 0.600
2025-08-29 03:34:47 - improved_diversity_analyzer - INFO - 综合多样性得分: 0.267
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 使用改进的多样性分析完成
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 数据复杂度: 0.211
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 调整后相关性阈值: 0.600
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 综合多样性得分: 0.267
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   DecisionTree vs RandomForest: 相关性=0.844, 多样性=0.156
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   DecisionTree vs KNN: 相关性=0.717, 多样性=0.283
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   DecisionTree vs Logistic: 相关性=0.791, 多样性=0.209
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   RandomForest vs KNN: 相关性=0.749, 多样性=0.251
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   RandomForest vs Logistic: 相关性=0.880, 多样性=0.120
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   KNN vs Logistic: 相关性=0.739, 多样性=0.261
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 特征重要性多样性: 0.035
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 算法类型多样性: 0.750
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 算法类型分布: {'tree': 2, 'instance': 1, 'linear': 1}
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 开始量化多样性评估...
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO - 开始计算 4 个模型间的量化多样性指标...
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_RandomForest:
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     Q统计量: 0.765 (多样性: 0.235)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     双错度量: 0.075 (多样性: 0.925)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     综合多样性: 0.378
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_KNN:
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     Q统计量: 0.714 (多样性: 0.286)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     不一致性: 0.225
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     相关系数: 0.551 (多样性: 0.449)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     综合多样性: 0.423
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -   DecisionTree_vs_Logistic:
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     Q统计量: 0.867 (多样性: 0.133)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     不一致性: 0.175
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     相关系数: 0.651 (多样性: 0.349)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     综合多样性: 0.337
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -   RandomForest_vs_KNN:
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     Q统计量: 0.806 (多样性: 0.194)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     不一致性: 0.200
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     双错度量: 0.100 (多样性: 0.900)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     相关系数: 0.600 (多样性: 0.400)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     综合多样性: 0.378
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -   RandomForest_vs_Logistic:
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     Q统计量: 0.933 (多样性: 0.067)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     双错度量: 0.125 (多样性: 0.875)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     综合多样性: 0.300
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -   KNN_vs_Logistic:
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     Q统计量: 0.909 (多样性: 0.091)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     不一致性: 0.150
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     双错度量: 0.175 (多样性: 0.825)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     相关系数: 0.700 (多样性: 0.300)
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -     综合多样性: 0.297
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO - 整体多样性统计:
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -   平均多样性: 0.352
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -   多样性标准差: 0.045
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -   最小多样性: 0.297
2025-08-29 03:34:47 - quantified_diversity_evaluator - INFO -   最大多样性: 0.423
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 量化多样性评估完成:
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   Q统计量平均多样性: 0.352
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   多样性变异系数: 0.129
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO -   熵多样性: 0.276
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 从 4 个合格模型中选择 3 个进行集成
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 最优组合: ['DecisionTree', 'KNN', 'Logistic']
2025-08-29 03:34:47 - enhanced_ensemble_selector - INFO - 综合得分: 0.6756
