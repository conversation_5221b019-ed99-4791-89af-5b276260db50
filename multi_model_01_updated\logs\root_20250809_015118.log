2025-08-09 01:51:20 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-09 01:51:20 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-09 01:51:20 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-09 01:51:21 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-09 01:51:21 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-09 01:51:21 - GUI - INFO - GUI界面初始化完成
2025-08-09 01:51:45 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-09 01:51:45 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 01:51:45 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-09 01:51:45 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:51:45 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:51:45 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:51:45 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:51:45 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:51:47 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9786
2025-08-09 01:51:49 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9818
2025-08-09 01:51:51 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.9835
2025-08-09 01:52:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 01:52:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 01:52:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 01:52:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 01:52:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 01:52:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 01:52:02 - hyperparameter_tuning - INFO - Trial 22: 发现更好的得分 0.9852
2025-08-09 01:52:02 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 147, 'max_depth': 11, 'min_samples_split': 7, 'min_samples_leaf': 1, 'max_features': 'sqrt'}
2025-08-09 01:52:02 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9852
2025-08-09 01:52:02 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/50
2025-08-09 01:52:02 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:52:02 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:52:02 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_015202.html
2025-08-09 01:52:03 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:52:03 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_015203.html
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 17.42 秒
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 4.619272175049414, 'clf__kernel': 'rbf'}
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:52:03 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:52:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_015203.html
2025-08-09 01:52:04 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:52:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_015204.html
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.25 秒
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 3}
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:52:05 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:52:05 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_015205.html
2025-08-09 01:52:05 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:52:05 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_015205.html
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.22 秒
2025-08-09 01:52:10 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015210
2025-08-09 01:52:10 - training_session_manager - INFO - 创建训练会话: 训练_nodule2_20250809_015210 (ID: 20250809_015210)
2025-08-09 01:52:10 - training_session_manager - INFO - 创建新会话: 训练_nodule2_20250809_015210
2025-08-09 01:52:10 - session_utils - INFO - 创建新会话: 训练_nodule2_20250809_015210 (ID: 20250809_015210)
2025-08-09 01:52:10 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-09 01:52:10 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 01:52:10 - model_training - INFO - 模型名称: Random Forest
2025-08-09 01:52:10 - model_training - INFO - 准确率: 0.8750
2025-08-09 01:52:10 - model_training - INFO - AUC: 0.9412
2025-08-09 01:52:10 - model_training - INFO - AUPRC: 0.9359
2025-08-09 01:52:10 - model_training - INFO - 混淆矩阵:
2025-08-09 01:52:10 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 01:52:10 - model_training - INFO - 
分类报告:
2025-08-09 01:52:10 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 01:52:10 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 01:52:10 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-09 01:52:10 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015210\models\RandomForest_single_015210.joblib
2025-08-09 01:52:10 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015210\models\RandomForest_single_015210.joblib
2025-08-09 01:52:10 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 01:52:10 - model_training - INFO - 模型名称: SVM
2025-08-09 01:52:10 - model_training - INFO - 准确率: 0.7750
2025-08-09 01:52:10 - model_training - INFO - AUC: 0.9182
2025-08-09 01:52:10 - model_training - INFO - AUPRC: 0.9034
2025-08-09 01:52:10 - model_training - INFO - 混淆矩阵:
2025-08-09 01:52:10 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-09 01:52:10 - model_training - INFO - 
分类报告:
2025-08-09 01:52:10 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-09 01:52:10 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 01:52:10 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-09 01:52:10 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015210\models\SVM_single_015210.joblib
2025-08-09 01:52:10 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015210\models\SVM_single_015210.joblib
2025-08-09 01:52:10 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 01:52:10 - model_training - INFO - 模型名称: KNN
2025-08-09 01:52:10 - model_training - INFO - 准确率: 0.8750
2025-08-09 01:52:10 - model_training - INFO - AUC: 0.9322
2025-08-09 01:52:10 - model_training - INFO - AUPRC: 0.9189
2025-08-09 01:52:10 - model_training - INFO - 混淆矩阵:
2025-08-09 01:52:10 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 01:52:10 - model_training - INFO - 
分类报告:
2025-08-09 01:52:10 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 01:52:10 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 01:52:10 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-09 01:52:10 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015210\models\KNN_single_015210.joblib
2025-08-09 01:52:10 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015210\models\KNN_single_015210.joblib
2025-08-09 01:52:10 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 01:52:10 - delong_test - INFO - 开始DeLong检验，比较3个模型
2025-08-09 01:52:10 - delong_test - INFO - 比较RandomForest vs SVM: AUC1=0.9412, AUC2=0.9182, p=0.670889
2025-08-09 01:52:10 - delong_test - INFO - 比较RandomForest vs KNN: AUC1=0.9412, AUC2=0.9322, p=0.870121
2025-08-09 01:52:10 - delong_test - INFO - 比较SVM vs KNN: AUC1=0.9182, AUC2=0.9322, p=0.814172
2025-08-09 01:52:10 - delong_test - INFO - DeLong检验完成，共3个比较
2025-08-09 01:52:52 - training_session_manager - INFO - 成功加载会话: 训练_nodule2_20250809_015210
2025-08-09 01:52:52 - session_loader - INFO - 初始化会话加载器
2025-08-09 01:52:55 - training_session_manager - INFO - 成功删除会话: 20250809_015210
2025-08-09 01:53:04 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-09 01:53:04 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9274
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9430
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9430
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9430
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - Trial 12: 发现更好的得分 0.9479
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9430
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9430
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 7, 'min_samples_split': 30, 'min_samples_leaf': 9, 'criterion': 'entropy', 'class_weight': 'balanced', 'max_features': None}
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9479
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/50
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:53:04 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:53:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250809_015304.html
2025-08-09 01:53:04 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:53:05 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250809_015304.html
2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.94 秒
2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:53:09 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9786
2025-08-09 01:53:09 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9836
2025-08-09 01:53:16 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:16 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 222, 'max_depth': 23, 'min_samples_split': 17, 'min_samples_leaf': 1, 'max_features': 'sqrt'}
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9844
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/50
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:53:19 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:53:19 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_015319.html
2025-08-09 01:53:19 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:53:19 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_015319.html
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 14.73 秒
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9860
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9876
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9893
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 215, 'max_depth': 10, 'learning_rate': 0.217017658308203, 'subsample': 0.73022589840624, 'colsample_bytree': 0.5914857955622806}
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/50
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:53:23 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:53:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_015323.html
2025-08-09 01:53:24 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:53:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_015324.html
2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.49 秒
2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:53:31 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9823
2025-08-09 01:53:32 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9842
2025-08-09 01:53:32 - hyperparameter_tuning - INFO - Trial 12: 发现更好的得分 0.9866
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9866
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9866
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9874
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9874
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9874
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9874
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9874
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9874
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 88, 'max_depth': 4, 'learning_rate': 0.09954745639192011, 'feature_fraction': 0.933015774119652, 'bagging_fraction': 0.6692850364629274}
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9874
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 实际执行试验次数: 27/50
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:53:33 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:53:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_015333.html
2025-08-09 01:53:34 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:53:34 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_015334.html
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 10.02 秒
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 01:53:39 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9721
2025-08-09 01:53:40 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9820
2025-08-09 01:53:55 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9861
2025-08-09 01:54:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9869
2025-08-09 01:54:43 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 204, 'depth': 2, 'learning_rate': 0.11895186568849454, 'l2_leaf_reg': 8.448363213508443, 'bagging_temperature': 0.8095789183531145}
2025-08-09 01:54:43 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9869
2025-08-09 01:54:43 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-09 01:54:43 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:54:43 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:54:43 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:43 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250809_015443.html
2025-08-09 01:54:43 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:54:44 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250809_015443.html
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 69.72 秒
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9706
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9731
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9747
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 9.081540944009888, 'clf__solver': 'lbfgs'}
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9747
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 实际执行试验次数: 22/50
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:54:45 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:54:45 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_015445.html
2025-08-09 01:54:45 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:54:45 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_015445.html
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.61 秒
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9688
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9868
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 5.756578470029271, 'clf__kernel': 'rbf'}
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/50
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:54:46 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:54:46 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_015446.html
2025-08-09 01:54:46 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:54:47 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_015446.html
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.27 秒
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9782
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 4}
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 实际执行试验次数: 26/50
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:54:47 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:54:48 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_015447.html
2025-08-09 01:54:48 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:54:48 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_015448.html
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.41 秒
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9573
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:54:55 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9857
2025-08-09 01:55:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9857
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9857
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.008391381121781416}
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9864
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:55:06 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:55:07 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:55:07 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_015506.html
2025-08-09 01:55:07 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 01:55:07 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:55:07 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_015507.html
2025-08-09 01:55:07 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 18.92 秒
2025-08-09 01:55:12 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512
2025-08-09 01:55:12 - training_session_manager - INFO - 创建训练会话: 训练_nodule2_20250809_015512 (ID: 20250809_015512)
2025-08-09 01:55:12 - training_session_manager - INFO - 创建新会话: 训练_nodule2_20250809_015512
2025-08-09 01:55:12 - session_utils - INFO - 创建新会话: 训练_nodule2_20250809_015512 (ID: 20250809_015512)
2025-08-09 01:55:12 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-09 01:55:12 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 01:55:12 - model_training - INFO - 模型名称: Decision Tree
2025-08-09 01:55:12 - model_training - INFO - 准确率: 0.8000
2025-08-09 01:55:12 - model_training - INFO - AUC: 0.8951
2025-08-09 01:55:12 - model_training - INFO - AUPRC: 0.7948
2025-08-09 01:55:12 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:12 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-09 01:55:12 - model_training - INFO - 
分类报告:
2025-08-09 01:55:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-09 01:55:12 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 01:55:12 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8000
2025-08-09 01:55:12 - training_session_manager - INFO - 保存模型 DecisionTree 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\DecisionTree_single_015512.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\DecisionTree_single_015512.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型名称: Random Forest
2025-08-09 01:55:12 - model_training - INFO - 准确率: 0.8750
2025-08-09 01:55:12 - model_training - INFO - AUC: 0.9412
2025-08-09 01:55:12 - model_training - INFO - AUPRC: 0.9359
2025-08-09 01:55:12 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:12 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 01:55:12 - model_training - INFO - 
分类报告:
2025-08-09 01:55:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 01:55:12 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 01:55:12 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-09 01:55:12 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\RandomForest_single_015512.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\RandomForest_single_015512.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 01:55:12 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 01:55:12 - model_training - INFO - 模型名称: XGBoost
2025-08-09 01:55:12 - model_training - INFO - 准确率: 0.9000
2025-08-09 01:55:12 - model_training - INFO - AUC: 0.9719
2025-08-09 01:55:12 - model_training - INFO - AUPRC: 0.9627
2025-08-09 01:55:12 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:12 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-09 01:55:12 - model_training - INFO - 
分类报告:
2025-08-09 01:55:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-09 01:55:12 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 01:55:12 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-09 01:55:12 - training_session_manager - INFO - 保存模型 XGBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\XGBoost_single_015512.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\XGBoost_single_015512.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 01:55:12 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 01:55:12 - model_training - INFO - 模型名称: LightGBM
2025-08-09 01:55:12 - model_training - INFO - 准确率: 0.8500
2025-08-09 01:55:12 - model_training - INFO - AUC: 0.9488
2025-08-09 01:55:12 - model_training - INFO - AUPRC: 0.9492
2025-08-09 01:55:12 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:12 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-09 01:55:12 - model_training - INFO - 
分类报告:
2025-08-09 01:55:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-09 01:55:12 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 01:55:12 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8500
2025-08-09 01:55:12 - training_session_manager - INFO - 保存模型 LightGBM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\LightGBM_single_015512.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\LightGBM_single_015512.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 01:55:12 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-09 01:55:13 - model_training - INFO - 模型名称: CatBoost
2025-08-09 01:55:13 - model_training - INFO - 准确率: 0.8750
2025-08-09 01:55:13 - model_training - INFO - AUC: 0.9591
2025-08-09 01:55:13 - model_training - INFO - AUPRC: 0.9570
2025-08-09 01:55:13 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:13 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 01:55:13 - model_training - INFO - 
分类报告:
2025-08-09 01:55:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 01:55:13 - model_training - INFO - 训练时间: 1.03 秒
2025-08-09 01:55:13 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8750
2025-08-09 01:55:13 - training_session_manager - INFO - 保存模型 CatBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\CatBoost_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\CatBoost_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 01:55:13 - model_training - INFO - 准确率: 0.8250
2025-08-09 01:55:13 - model_training - INFO - AUC: 0.9284
2025-08-09 01:55:13 - model_training - INFO - AUPRC: 0.9288
2025-08-09 01:55:13 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:13 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-08-09 01:55:13 - model_training - INFO - 
分类报告:
2025-08-09 01:55:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-08-09 01:55:13 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 01:55:13 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8250
2025-08-09 01:55:13 - training_session_manager - INFO - 保存模型 Logistic 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\Logistic_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\Logistic_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型名称: SVM
2025-08-09 01:55:13 - model_training - INFO - 准确率: 0.7750
2025-08-09 01:55:13 - model_training - INFO - AUC: 0.9182
2025-08-09 01:55:13 - model_training - INFO - AUPRC: 0.9034
2025-08-09 01:55:13 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:13 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-09 01:55:13 - model_training - INFO - 
分类报告:
2025-08-09 01:55:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-09 01:55:13 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 01:55:13 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-09 01:55:13 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\SVM_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\SVM_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型名称: KNN
2025-08-09 01:55:13 - model_training - INFO - 准确率: 0.8750
2025-08-09 01:55:13 - model_training - INFO - AUC: 0.9322
2025-08-09 01:55:13 - model_training - INFO - AUPRC: 0.9189
2025-08-09 01:55:13 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:13 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 01:55:13 - model_training - INFO - 
分类报告:
2025-08-09 01:55:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 01:55:13 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 01:55:13 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-09 01:55:13 - training_session_manager - INFO - 保存模型 KNN 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\KNN_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\KNN_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型名称: Naive Bayes
2025-08-09 01:55:13 - model_training - INFO - 准确率: 0.8750
2025-08-09 01:55:13 - model_training - INFO - AUC: 0.8977
2025-08-09 01:55:13 - model_training - INFO - AUPRC: 0.9096
2025-08-09 01:55:13 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:13 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-09 01:55:13 - model_training - INFO - 
分类报告:
2025-08-09 01:55:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 01:55:13 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 01:55:13 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8750
2025-08-09 01:55:13 - training_session_manager - INFO - 保存模型 NaiveBayes 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\NaiveBayes_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\NaiveBayes_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-09 01:55:14 - model_training - INFO - 模型名称: Neural Network
2025-08-09 01:55:14 - model_training - INFO - 准确率: 0.8750
2025-08-09 01:55:14 - model_training - INFO - AUC: 0.9591
2025-08-09 01:55:14 - model_training - INFO - AUPRC: 0.9450
2025-08-09 01:55:14 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:14 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 01:55:14 - model_training - INFO - 
分类报告:
2025-08-09 01:55:14 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 01:55:14 - model_training - INFO - 训练时间: 0.22 秒
2025-08-09 01:55:14 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-09 01:55:14 - training_session_manager - INFO - 保存模型 NeuralNet 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\NeuralNet_single_015514.joblib
2025-08-09 01:55:14 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\NeuralNet_single_015514.joblib
2025-08-09 01:55:14 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-09 01:55:14 - delong_test - INFO - 开始DeLong检验，比较10个模型
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs SVM: AUC1=0.9412, AUC2=0.9182, p=0.670889
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs KNN: AUC1=0.9412, AUC2=0.9322, p=0.870121
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs DecisionTree: AUC1=0.9412, AUC2=0.8951, p=0.461102
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs XGBoost: AUC1=0.9412, AUC2=0.9719, p=0.442730
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs LightGBM: AUC1=0.9412, AUC2=0.9488, p=0.871470
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs CatBoost: AUC1=0.9412, AUC2=0.9591, p=0.684015
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs Logistic: AUC1=0.9412, AUC2=0.9284, p=0.808351
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs NaiveBayes: AUC1=0.9412, AUC2=0.8977, p=0.503043
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs NeuralNet: AUC1=0.9412, AUC2=0.9591, p=0.681476
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs KNN: AUC1=0.9182, AUC2=0.9322, p=0.814172
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs DecisionTree: AUC1=0.9182, AUC2=0.8951, p=0.731086
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs XGBoost: AUC1=0.9182, AUC2=0.9719, p=0.250326
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs LightGBM: AUC1=0.9182, AUC2=0.9488, p=0.564215
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs CatBoost: AUC1=0.9182, AUC2=0.9591, p=0.414906
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs Logistic: AUC1=0.9182, AUC2=0.9284, p=0.859988
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs NaiveBayes: AUC1=0.9182, AUC2=0.8977, p=0.767725
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs NeuralNet: AUC1=0.9182, AUC2=0.9591, p=0.411875
2025-08-09 01:55:14 - delong_test - INFO - 比较KNN vs DecisionTree: AUC1=0.9322, AUC2=0.8951, p=0.582415
2025-08-09 01:55:14 - delong_test - INFO - 比较KNN vs XGBoost: AUC1=0.9322, AUC2=0.9719, p=0.402874
2025-08-09 01:55:14 - delong_test - INFO - 比较KNN vs LightGBM: AUC1=0.9322, AUC2=0.9488, p=0.757393
2025-08-09 01:55:14 - delong_test - INFO - 比较KNN vs CatBoost: AUC1=0.9322, AUC2=0.9591, p=0.597174
2025-08-09 01:55:14 - delong_test - INFO - 比较KNN vs Logistic: AUC1=0.9322, AUC2=0.9284, p=0.947748
2025-08-09 01:55:14 - delong_test - INFO - 比较KNN vs NaiveBayes: AUC1=0.9322, AUC2=0.8977, p=0.620483
2025-08-09 01:55:14 - delong_test - INFO - 比较KNN vs NeuralNet: AUC1=0.9322, AUC2=0.9591, p=0.594848
2025-08-09 01:55:14 - delong_test - INFO - 比较DecisionTree vs XGBoost: AUC1=0.8951, AUC2=0.9719, p=0.171602
2025-08-09 01:55:14 - delong_test - INFO - 比较DecisionTree vs LightGBM: AUC1=0.8951, AUC2=0.9488, p=0.383626
2025-08-09 01:55:14 - delong_test - INFO - 比较DecisionTree vs CatBoost: AUC1=0.8951, AUC2=0.9591, p=0.278861
2025-08-09 01:55:14 - delong_test - INFO - 比较DecisionTree vs Logistic: AUC1=0.8951, AUC2=0.9284, p=0.613404
2025-08-09 01:55:14 - delong_test - INFO - 比较DecisionTree vs NaiveBayes: AUC1=0.8951, AUC2=0.8977, p=0.973132
2025-08-09 01:55:14 - delong_test - INFO - 比较DecisionTree vs NeuralNet: AUC1=0.8951, AUC2=0.9591, p=0.276612
2025-08-09 01:55:14 - delong_test - INFO - 比较XGBoost vs LightGBM: AUC1=0.9719, AUC2=0.9488, p=0.551970
2025-08-09 01:55:14 - delong_test - INFO - 比较XGBoost vs CatBoost: AUC1=0.9719, AUC2=0.9591, p=0.710112
2025-08-09 01:55:14 - delong_test - INFO - 比较XGBoost vs Logistic: AUC1=0.9719, AUC2=0.9284, p=0.334321
2025-08-09 01:55:14 - delong_test - INFO - 比较XGBoost vs NaiveBayes: AUC1=0.9719, AUC2=0.8977, p=0.207561
2025-08-09 01:55:14 - delong_test - INFO - 比较XGBoost vs NeuralNet: AUC1=0.9719, AUC2=0.9591, p=0.706238
2025-08-09 01:55:14 - delong_test - INFO - 比较LightGBM vs CatBoost: AUC1=0.9488, AUC2=0.9591, p=0.811190
2025-08-09 01:55:14 - delong_test - INFO - 比较LightGBM vs Logistic: AUC1=0.9488, AUC2=0.9284, p=0.692589
2025-08-09 01:55:14 - delong_test - INFO - 比较LightGBM vs NaiveBayes: AUC1=0.9488, AUC2=0.8977, p=0.425151
2025-08-09 01:55:14 - delong_test - INFO - 比较LightGBM vs NeuralNet: AUC1=0.9488, AUC2=0.9591, p=0.809528
2025-08-09 01:55:14 - delong_test - INFO - 比较CatBoost vs Logistic: AUC1=0.9591, AUC2=0.9284, p=0.527935
2025-08-09 01:55:14 - delong_test - INFO - 比较CatBoost vs NaiveBayes: AUC1=0.9591, AUC2=0.8977, p=0.319359
2025-08-09 01:55:14 - delong_test - INFO - 比较CatBoost vs NeuralNet: AUC1=0.9591, AUC2=0.9591, p=1.000000
2025-08-09 01:55:14 - delong_test - INFO - 比较Logistic vs NaiveBayes: AUC1=0.9284, AUC2=0.8977, p=0.652459
2025-08-09 01:55:14 - delong_test - INFO - 比较Logistic vs NeuralNet: AUC1=0.9284, AUC2=0.9591, p=0.525075
2025-08-09 01:55:14 - delong_test - INFO - 比较NaiveBayes vs NeuralNet: AUC1=0.8977, AUC2=0.9591, p=0.317282
2025-08-09 01:55:14 - delong_test - INFO - DeLong检验完成，共45个比较
