factor,value,fit_time,pred_time,total_time
algorithm,auto,0.0,25.217804670333862,25.217804670333862
algorithm,ball_tree,0.005429506301879883,0.0030031204223632812,0.008432626724243164
algorithm,kd_tree,0.002000093460083008,0.0038144588470458984,0.005814552307128906
algorithm,brute,0.0009999275207519531,0.003000974655151367,0.00400090217590332
weights,uniform,0.0009982585906982422,0.002000093460083008,0.00299835205078125
weights,distance,0.0,0.004455089569091797,0.004455089569091797
n_neighbors,3,0.001438140869140625,0.0020096302032470703,0.0034477710723876953
n_neighbors,5,0.0009965896606445312,0.0030851364135742188,0.00408172607421875
n_neighbors,10,0.0010025501251220703,0.0019969940185546875,0.002999544143676758
n_neighbors,15,0.0009996891021728516,0.003094911575317383,0.004094600677490234
scaler,None,0.001001596450805664,0.0030243396759033203,0.004025936126708984
scaler,StandardScaler,0.002003908157348633,0.003001689910888672,0.005005598068237305
scaler,MinMaxScaler,0.0009989738464355469,0.0030002593994140625,0.003999233245849609
dataset_size,100,0.0,0.0020003318786621094,0.0020003318786621094
dataset_size,500,0.0010004043579101562,0.0033071041107177734,0.00430750846862793
dataset_size,1000,0.0,0.004005908966064453,0.004005908966064453
dataset_size,2000,0.0,0.003509998321533203,0.003509998321533203
cross_validation,5-fold,0.029204845428466797,0.0,0.029204845428466797
