#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试：验证Pipeline参数处理修复
"""

import numpy as np
import sys
from pathlib import Path

# 添加代码目录
sys.path.insert(0, str(Path(__file__).parent / 'code'))

from model_training import ModelTrainer

def test_logistic_pipeline_params():
    """测试Logistic回归的Pipeline参数处理"""
    print("测试Logistic回归的Pipeline参数处理...")
    
    # 创建训练器
    trainer = ModelTrainer('Logistic')
    
    # 模拟带'clf__'前缀的参数（来自超参数调优）
    pipeline_params = {
        'clf__C': 1.0,
        'clf__penalty': 'l2',
        'clf__solver': 'liblinear'
    }
    
    # 创建测试数据
    X = np.random.rand(50, 3)
    y = np.random.randint(0, 2, 50)
    
    try:
        # 使用Pipeline参数训练
        result = trainer.train_and_evaluate(X, y, X[:10], y[:10], params=pipeline_params)
        print("✓ 成功处理Pipeline参数！")
        print(f"  模型准确率: {result['metrics']['accuracy']:.3f}")
        return True
    except Exception as e:
        print(f"✗ 处理Pipeline参数失败: {e}")
        return False

def test_regular_params():
    """测试常规参数处理"""
    print("\n测试常规参数处理...")
    
    # 创建训练器
    trainer = ModelTrainer('RandomForest')
    
    # 常规参数（不带前缀）
    regular_params = {
        'n_estimators': 50,
        'max_depth': 5
    }
    
    # 创建测试数据
    X = np.random.rand(50, 3)
    y = np.random.randint(0, 2, 50)
    
    try:
        # 使用常规参数训练
        result = trainer.train_and_evaluate(X, y, X[:10], y[:10], params=regular_params)
        print("✓ 成功处理常规参数！")
        print(f"  模型准确率: {result['metrics']['accuracy']:.3f}")
        return True
    except Exception as e:
        print(f"✗ 处理常规参数失败: {e}")
        return False

if __name__ == "__main__":
    print("验证Pipeline参数处理修复...")
    print("=" * 50)
    
    success = True
    
    # 测试Pipeline参数
    if not test_logistic_pipeline_params():
        success = False
    
    # 测试常规参数
    if not test_regular_params():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！Pipeline参数处理修复成功！")
    else:
        print("❌ 测试失败！需要进一步检查。")
    print("=" * 50)