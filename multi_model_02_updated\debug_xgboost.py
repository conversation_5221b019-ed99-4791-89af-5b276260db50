#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试XGBoost超参数调优问题
"""

import numpy as np
import sys
from pathlib import Path
import traceback

# 添加代码目录
sys.path.insert(0, str(Path(__file__).parent / 'code'))

def test_xgboost_direct():
    """直接测试XGBoost模型"""
    print("=== 直接测试XGBoost模型 ===")
    
    try:
        from xgboost import XGBClassifier
        from sklearn.model_selection import cross_val_score
        from sklearn.model_selection import StratifiedKFold
        
        # 生成测试数据
        np.random.seed(42)
        X = np.random.rand(100, 10)
        y = np.random.randint(0, 2, 100)
        
        print(f"数据形状: X={X.shape}, y={y.shape}")
        print(f"类别分布: {np.bincount(y)}")
        
        # 创建模型
        model = XGBClassifier(
            n_estimators=100,
            max_depth=3,
            learning_rate=0.1,
            random_state=42,
            eval_metric='logloss',
            device='cpu'
        )
        
        # 交叉验证
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        scores = cross_val_score(model, X, y, cv=cv, scoring='roc_auc')
        
        print(f"交叉验证得分: {scores}")
        print(f"平均得分: {scores.mean():.4f}")
        
        return True
        
    except Exception as e:
        print(f"XGBoost直接测试失败: {e}")
        traceback.print_exc()
        return False

def test_xgboost_tuning():
    """测试XGBoost超参数调优"""
    print("\n=== 测试XGBoost超参数调优 ===")
    
    try:
        from hyperparameter_tuning import tune_model
        
        # 生成测试数据
        np.random.seed(42)
        X = np.random.rand(100, 10)
        y = np.random.randint(0, 2, 100)
        
        print(f"数据形状: X={X.shape}, y={y.shape}")
        
        # 运行超参数调优
        best_params, best_score = tune_model(
            'XGBoost',
            n_trials=3,
            X_train=X,
            y_train=y,
            scoring='roc_auc',
            early_stopping_rounds=5,
            timeout=60
        )
        
        print(f"\n调优结果:")
        print(f"最佳参数: {best_params}")
        print(f"最佳得分: {best_score:.4f}")
        
        if best_score > 0:
            print("✓ XGBoost超参数调优成功！")
            return True
        else:
            print("✗ XGBoost调优得分为0")
            return False
            
    except Exception as e:
        print(f"XGBoost调优测试失败: {e}")
        traceback.print_exc()
        return False

def test_optuna_objective():
    """测试Optuna目标函数"""
    print("\n=== 测试Optuna目标函数 ===")
    
    try:
        import optuna
        from hyperparameter_tuning import objective_xgboost
        
        # 生成测试数据
        np.random.seed(42)
        X = np.random.rand(100, 10)
        y = np.random.randint(0, 2, 100)
        
        # 创建study
        study = optuna.create_study(direction="maximize")
        
        # 手动运行一个trial
        def trial_callback(trial):
            return objective_xgboost(trial, X, y, 'roc_auc')
        
        # 运行优化
        study.optimize(trial_callback, n_trials=1)
        
        print(f"试验得分: {study.best_value:.4f}")
        print(f"最佳参数: {study.best_params}")
        
        return True
        
    except Exception as e:
        print(f"Optuna目标函数测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始调试XGBoost问题...\n")
    
    success_count = 0
    
    # 1. 直接测试XGBoost
    if test_xgboost_direct():
        success_count += 1
    
    # 2. 测试Optuna目标函数
    if test_optuna_objective():
        success_count += 1
    
    # 3. 测试超参数调优
    if test_xgboost_tuning():
        success_count += 1
    
    print(f"\n{'='*50}")
    print(f"测试结果: {success_count}/3 通过")
    
    if success_count == 3:
        print("✓ 所有测试通过，XGBoost工作正常")
    elif success_count > 0:
        print("⚠ 部分测试通过，可能存在特定场景的问题")
    else:
        print("✗ 所有测试失败，XGBoost存在严重问题")
    print('='*50)

if __name__ == "__main__":
    main()