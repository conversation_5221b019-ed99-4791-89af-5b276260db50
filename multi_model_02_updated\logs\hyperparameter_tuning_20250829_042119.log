2025-08-29 04:21:19 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-29 04:21:19 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 04:21:19 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 04:21:19 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 04:21:19 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 04:21:19 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800}
2025-08-29 04:21:19 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9290
2025-08-29 04:21:19 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9601
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - Trial 9: 发现更好的得分 0.9629
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9629
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9629
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9629
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9629
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 3, 'min_samples_split': 36, 'min_samples_leaf': 5, 'criterion': 'gini', 'class_weight': None, 'max_features': None}
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9629
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 实际执行试验次数: 23/50
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\optimization_history_20250829_042120.html
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\param_importances_20250829_042120.html
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.83 秒
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 04:21:20 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800}
2025-08-29 04:21:23 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9875
2025-08-29 04:21:30 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:21:30 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9875
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9875
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - Trial 12: 发现更好的得分 0.9891
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 163, 'max_depth': 26, 'min_samples_split': 16, 'min_samples_leaf': 9, 'max_features': 'sqrt'}
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9891
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\optimization_history_20250829_042131.html
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\param_importances_20250829_042131.html
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 11.03 秒
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9906
2025-08-29 04:21:31 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:32 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9923
2025-08-29 04:21:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:32 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:33 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:34 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9924
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 294, 'max_depth': 7, 'learning_rate': 0.10264378756176612, 'subsample': 0.5089809378074099, 'colsample_bytree': 0.5072567334400258}
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9924
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\optimization_history_20250829_042135.html
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\param_importances_20250829_042135.html
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 3.53 秒
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9838
2025-08-29 04:21:35 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9856
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9856
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 202, 'max_depth': 3, 'learning_rate': 0.02886496196573106, 'feature_fraction': 0.9744427686266666, 'bagging_fraction': 0.9828160165372797}
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9856
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\optimization_history_20250829_042136.html
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\param_importances_20250829_042136.html
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.52 秒
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-29 04:21:36 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:21:41 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9808
2025-08-29 04:21:41 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:21:42 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9841
2025-08-29 04:21:42 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:21:44 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:21:46 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:21:50 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9882
2025-08-29 04:21:50 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:21:54 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:21:57 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:22:02 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:22:04 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:22:08 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:22:15 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:22:18 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:22:20 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:22:21 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 04:22:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:22:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9882
2025-08-29 04:22:24 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 203, 'depth': 3, 'learning_rate': 0.09472194807521325, 'l2_leaf_reg': 4.297256589643226, 'bagging_temperature': 0.45606998421703593}
2025-08-29 04:22:24 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9882
2025-08-29 04:22:24 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-29 04:22:24 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 04:22:24 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\optimization_history_20250829_042224.html
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\param_importances_20250829_042225.html
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 48.27 秒
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9669
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - Trial 9: 发现更好的得分 0.9686
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 0.938830174550426, 'clf__solver': 'liblinear'}
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9686
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 实际执行试验次数: 22/50
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\optimization_history_20250829_042225.html
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\param_importances_20250829_042225.html
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.86 秒
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 04:22:25 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9779
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9779
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9779
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9779
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9779
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9779
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9779
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 1.9317549845020012, 'clf__kernel': 'linear'}
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9779
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\optimization_history_20250829_042226.html
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\param_importances_20250829_042226.html
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.58 秒
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 04:22:26 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-29 04:22:41 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 7, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-29 04:22:41 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.5000
2025-08-29 04:22:41 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9767
2025-08-29 04:22:56 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'distance', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 04:23:11 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 13, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-29 04:23:26 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 3, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 04:23:41 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 04:23:41 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9797
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9797
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 15, 'clf__weights': 'distance', 'clf__algorithm': 'kd_tree', 'clf__p': 1}
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9797
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/50
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\optimization_history_20250829_042342.html
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\param_importances_20250829_042342.html
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 75.79 秒
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9525
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 04:23:42 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-29 04:23:47 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9667
2025-08-29 04:23:53 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9702
2025-08-29 04:24:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:24:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.005731636137525193}
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9702
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 实际执行试验次数: 23/50
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\optimization_history_20250829_042404.html
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\param_importances_20250829_042404.html
2025-08-29 04:24:04 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 22.50 秒
