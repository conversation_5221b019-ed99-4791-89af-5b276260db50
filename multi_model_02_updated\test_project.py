#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目功能测试脚本
用于测试多模型二分类项目的关键功能
"""

import os
import sys
from pathlib import Path
import pandas as pd
import numpy as np

# 添加代码路径
project_root = Path(__file__).parent
code_path = project_root / 'code'
sys.path.insert(0, str(code_path))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    try:
        from config import MODEL_NAMES, MODEL_DISPLAY_NAMES, OUTPUT_PATH, CACHE_PATH
        print(f"✅ 成功导入配置: {len(MODEL_NAMES)} 个模型")
        
        from data_preprocessing import load_and_preprocess_data, DataPreprocessor
        print("✅ 成功导入数据预处理模块")
        
        from model_training import MODEL_TRAINERS
        print(f"✅ 成功导入模型训练器: {len(MODEL_TRAINERS)} 个")
        
        from hyperparameter_tuning import tune_model
        print("✅ 成功导入超参数调优模块")
        
        from model_ensemble import run_ensemble_pipeline
        print("✅ 成功导入集成学习模块")
        
        from multi_data_ensemble import run_multi_data_ensemble_pipeline
        print("✅ 成功导入多数据源集成模块")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_gpu_detection():
    """测试GPU检测"""
    print("\n测试GPU检测...")
    try:
        from config import detect_gpu_availability, OPTIMIZED_GPU_CONFIG
        gpu_info = detect_gpu_availability()
        print(f"GPU检测结果:")
        print(f"  - XGBoost GPU: {gpu_info['xgboost_gpu']}")
        print(f"  - LightGBM GPU: {gpu_info['lightgbm_gpu']}")
        print(f"  - CatBoost GPU: {gpu_info['catboost_gpu']}")
        print(f"  - 优化配置启用GPU: {OPTIMIZED_GPU_CONFIG.get('use_gpu', False)}")
        return True
    except Exception as e:
        print(f"❌ GPU检测失败: {e}")
        return False

def test_data_preprocessing():
    """测试数据预处理"""
    print("\n测试数据预处理...")
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            'feature1': np.random.randn(100),
            'feature2': np.random.randn(100),
            'feature3': np.random.randn(100),
            'target': np.random.randint(0, 2, 100)
        })
        
        # 保存测试数据
        test_file = project_root / 'test_data.csv'
        test_data.to_csv(test_file, index=False)
        
        # 测试数据加载
        from data_preprocessing import load_and_preprocess_data
        X_train, X_test, y_train, y_test = load_and_preprocess_data(str(test_file))
        
        print(f"✅ 数据加载成功:")
        print(f"  - 训练集: {X_train.shape}")
        print(f"  - 测试集: {X_test.shape}")
        print(f"  - 标签分布: {np.bincount(y_train)}")
        
        # 清理测试文件
        test_file.unlink()
        
        return True
    except Exception as e:
        print(f"❌ 数据预处理失败: {e}")
        return False

def test_model_training():
    """测试模型训练（快速测试）"""
    print("\n测试模型训练...")
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            'feature1': np.random.randn(50),
            'feature2': np.random.randn(50),
            'target': np.random.randint(0, 2, 50)
        })
        
        test_file = project_root / 'test_data.csv'
        test_data.to_csv(test_file, index=False)
        
        # 加载和预处理数据
        from data_preprocessing import load_and_preprocess_data
        X_train, X_test, y_train, y_test = load_and_preprocess_data(str(test_file))
        
        # 测试单个模型训练
        from model_training import train_decision_tree
        result = train_decision_tree(X_train, y_train, X_test, y_test)
        
        if result and 'model' in result:
            print("✅ 决策树模型训练成功")
            print(f"  - 模型类型: {type(result['model']).__name__}")
            print(f"  - 测试集大小: {result['X_test'].shape}")
        
        # 清理测试文件
        test_file.unlink()
        
        return True
    except Exception as e:
        print(f"❌ 模型训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_imports():
    """测试GUI相关导入"""
    print("\n测试GUI相关导入...")
    try:
        import tkinter as tk
        print("✅ tkinter 可用")
        
        # 测试GUI模块导入
        gui_path = project_root
        sys.path.insert(0, str(gui_path))
        
        from thread_safe_gui import ThreadSafeGUI
        print("✅ 线程安全GUI模块可用")
        
        return True
    except Exception as e:
        print(f"❌ GUI导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("多模型二分类项目功能测试")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("模块导入", test_imports),
        ("GPU检测", test_gpu_detection),
        ("数据预处理", test_data_preprocessing),
        ("模型训练", test_model_training),
        ("GUI相关", test_gui_imports)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目可以正常运行。")
    else:
        print("⚠️  部分测试失败，请检查相关问题。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)