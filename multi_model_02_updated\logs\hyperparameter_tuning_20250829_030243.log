2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800}
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9655
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9655
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9655
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9655
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9655
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 7, 'min_samples_split': 18, 'min_samples_leaf': 7, 'criterion': 'entropy', 'class_weight': None, 'max_features': None}
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9655
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\optimization_history_20250829_030243.html
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\param_importances_20250829_030243.html
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.55 秒
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:02:43 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800}
2025-08-29 03:02:45 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9727
