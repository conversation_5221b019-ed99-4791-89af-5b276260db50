#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试超参数调优和模型训练的集成
验证Pipeline参数处理是否正确
"""

import numpy as np
from pathlib import Path
import sys
import os

# 添加代码目录到路径
sys.path.insert(0, str(Path(__file__).parent / 'code'))

from hyperparameter_tuning import tune_model
from model_training import MODEL_TRAINERS
from data_preprocessing import load_and_preprocess_data
from config import RANDOM_SEED

def test_pipeline_parameter_handling():
    """测试Pipeline参数处理"""
    print("=" * 60)
    print("测试Pipeline参数处理")
    print("=" * 60)
    
    # 生成测试数据
    np.random.seed(RANDOM_SEED)
    X = np.random.rand(100, 5)
    y = np.random.randint(0, 2, 100)
    
    # 测试使用Pipeline的模型（Logistic、SVM、KNN、NeuralNet）
    pipeline_models = ['Logistic', 'SVM', 'KNN']
    
    for model_name in pipeline_models:
        print(f"\n测试模型: {model_name}")
        print("-" * 40)
        
        try:
            # 1. 超参数调优（会返回带'clf__'前缀的参数）
            print("1. 运行超参数调优...")
            best_params, best_score = tune_model(
                model_name, 
                n_trials=3,  # 减少试验次数以加快测试
                X_train=X, 
                y_train=y,
                scoring='accuracy'
            )
            
            print(f"   调优返回的参数: {best_params}")
            print(f"   最佳得分: {best_score:.4f}")
            
            # 2. 检查参数前缀
            has_prefix = any(k.startswith('clf__') for k in best_params.keys())
            print(f"   参数包含'clf__'前缀: {has_prefix}")
            
            # 3. 使用参数训练模型
            print("2. 使用调优参数训练模型...")
            trainer = MODEL_TRAINERS[model_name]
            
            # 确保模型训练器能正确处理Pipeline参数
            result = trainer.train_and_evaluate(
                X, y, X[:20], y[:20], 
                params=best_params
            )
            
            print(f"   训练成功！准确率: {result['metrics']['accuracy']:.4f}")
            print(f"   ✓ {model_name} 的Pipeline参数处理正常")
            
        except Exception as e:
            print(f"   ✗ {model_name} 测试失败: {e}")
            return False
    
    print(f"\n{'='*60}")
    print("所有Pipeline模型的参数处理测试通过！")
    print("="*60)
    return True

def test_non_pipeline_models():
    """测试非Pipeline模型"""
    print("\n" + "=" * 60)
    print("测试非Pipeline模型")
    print("=" * 60)
    
    # 生成测试数据
    np.random.seed(RANDOM_SEED)
    X = np.random.rand(100, 5)
    y = np.random.randint(0, 2, 100)
    
    # 测试不使用Pipeline的模型
    non_pipeline_models = ['RandomForest', 'XGBoost', 'DecisionTree']
    
    for model_name in non_pipeline_models:
        print(f"\n测试模型: {model_name}")
        print("-" * 40)
        
        try:
            # 1. 超参数调优
            print("1. 运行超参数调优...")
            best_params, best_score = tune_model(
                model_name, 
                n_trials=3,
                X_train=X, 
                y_train=y,
                scoring='accuracy'
            )
            
            print(f"   调优返回的参数: {best_params}")
            
            # 2. 检查参数前缀（应该没有）
            has_prefix = any(k.startswith('clf__') for k in best_params.keys())
            print(f"   参数包含'clf__'前缀: {has_prefix}")
            
            # 3. 训练模型
            print("2. 使用调优参数训练模型...")
            trainer = MODEL_TRAINERS[model_name]
            result = trainer.train_and_evaluate(
                X, y, X[:20], y[:20], 
                params=best_params
            )
            
            print(f"   训练成功！准确率: {result['metrics']['accuracy']:.4f}")
            print(f"   ✓ {model_name} 参数处理正常")
            
        except Exception as e:
            print(f"   ✗ {model_name} 测试失败: {e}")
            return False
    
    print(f"\n{'='*60}")
    print("所有非Pipeline模型的参数处理测试通过！")
    print("="*60)
    return True

def main():
    """主测试函数"""
    print("开始测试超参数调优和模型训练的集成...")
    
    success = True
    
    # 测试Pipeline模型
    if not test_pipeline_parameter_handling():
        success = False
    
    # 测试非Pipeline模型
    if not test_non_pipeline_models():
        success = False
    
    if success:
        print(f"\n{'='*60}")
        print("🎉 所有测试通过！")
        print("Pipeline参数处理修复成功！")
        print("="*60)
    else:
        print(f"\n{'='*60}")
        print("❌ 部分测试失败！")
        print("="*60)
    
    return success

if __name__ == "__main__":
    main()