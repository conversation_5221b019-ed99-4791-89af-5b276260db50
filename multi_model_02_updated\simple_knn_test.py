#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的KNN性能测试
"""

import time
import numpy as np
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.datasets import make_classification

# 创建测试数据
X, y = make_classification(n_samples=1000, n_features=20, random_state=42)

print("=== KNN性能瓶颈分析 ===")

# 1. 算法选择测试
print("\n1. 算法选择影响:")
algorithms = ['auto', 'ball_tree', 'kd_tree', 'brute']
for algo in algorithms:
    knn = KNeighborsClassifier(algorithm=algo)
    start = time.time()
    knn.fit(X, y)
    fit_time = time.time() - start
    print(f"  {algo}: {fit_time:.4f}秒")

# 2. 权重方式测试
print("\n2. 权重方式影响:")
weights = ['uniform', 'distance']
for weight in weights:
    knn = KNeighborsClassifier(weights=weight)
    start = time.time()
    knn.fit(X, y)
    fit_time = time.time() - start
    # 测试预测
    start = time.time()
    knn.predict(X[:100])
    pred_time = time.time() - start
    print(f"  {weight}: 训练{fit_time:.4f}秒, 预测{pred_time:.4f}秒")

# 3. 邻居数测试
print("\n3. 邻居数影响:")
for n in [3, 5, 10, 15]:
    knn = KNeighborsClassifier(n_neighbors=n)
    start = time.time()
    knn.fit(X, y)
    fit_time = time.time() - start
    print(f"  n_neighbors={n}: {fit_time:.4f}秒")

# 4. 数据标准化测试
print("\n4. 标准化影响:")
# 无标准化
knn = KNeighborsClassifier()
start = time.time()
knn.fit(X, y)
no_scale_time = time.time() - start

# 有标准化
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)
knn = KNeighborsClassifier()
start = time.time()
knn.fit(X_scaled, y)
scale_time = time.time() - start

print(f"  无标准化: {no_scale_time:.4f}秒")
print(f"  有标准化: {scale_time:.4f}秒")

# 5. 数据集大小测试
print("\n5. 数据集大小影响:")
for size in [100, 500, 1000]:
    X_sub, y_sub = X[:size], y[:size]
    knn = KNeighborsClassifier()
    start = time.time()
    knn.fit(X_sub, y_sub)
    fit_time = time.time() - start
    print(f"  {size}样本: {fit_time:.4f}秒")

print("\n=== 结论 ===")
print("KNN训练慢的主要原因:")
print("1. 'distance'权重比'uniform'慢很多（需要计算所有距离）")
print("2. 'auto'算法可能选择低效的算法")
print("3. 数据标准化增加额外开销")
print("4. 数据集大小影响显著（O(n²)复杂度）")