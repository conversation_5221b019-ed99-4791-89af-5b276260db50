﻿模型组合,组合大小,平均性能得分,平均Q统计量,平均Q统计量多样性,平均不一致性,平均双错度量,平均双错多样性,平均相关系数,平均相关性多样性,综合多样性得分,综合得分,多样性等级,推荐集成方法
LightGBM + NaiveBayes + NeuralNet,3,0.8799,0.7636,0.2364,0.15,0.0667,0.9333,0.7036,0.2964,0.3619,0.6209,中等,堆叠法
LightGBM + SVM + NaiveBayes,3,0.873,0.8083,0.1917,0.15,0.075,0.925,0.7129,0.2871,0.3449,0.609,中等,堆叠法
XGBoost + LightGBM + NeuralNet,3,0.8765,0.8135,0.1865,0.1333,0.0833,0.9167,0.7339,0.2661,0.3325,0.6045,中等,堆叠法
XGBoost + NaiveBayes + NeuralNet,3,0.8657,0.8229,0.1771,0.15,0.0833,0.9167,0.7036,0.2964,0.3407,0.6032,中等,堆叠法
XGBoost + LightGBM + NaiveBayes,3,0.8739,0.8135,0.1865,0.1333,0.0833,0.9167,0.7383,0.2617,0.3316,0.6028,中等,堆叠法
DecisionTree + LightGBM + NaiveBayes,3,0.8392,0.8154,0.1846,0.1833,0.0917,0.9083,0.6451,0.3549,0.363,0.6011,中等,堆叠法
LightGBM + SVM + NeuralNet,3,0.8757,0.8309,0.1691,0.1333,0.0833,0.9167,0.7382,0.2618,0.3264,0.601,中等,堆叠法
LightGBM + CatBoost + NeuralNet,3,0.885,0.8331,0.1669,0.1167,0.0833,0.9167,0.7688,0.2312,0.3147,0.5998,中等,堆叠法
LightGBM + CatBoost + NaiveBayes,3,0.8823,0.8331,0.1669,0.1167,0.0833,0.9167,0.775,0.225,0.3134,0.5979,中等,堆叠法
CatBoost + NaiveBayes + NeuralNet,3,0.8741,0.8622,0.1378,0.1333,0.0833,0.9167,0.7411,0.2589,0.3165,0.5953,中等,堆叠法
XGBoost + SVM + NaiveBayes,3,0.8588,0.8443,0.1557,0.15,0.0917,0.9083,0.7129,0.2871,0.3308,0.5948,中等,堆叠法
RandomForest + LightGBM + NeuralNet,3,0.8831,0.8617,0.1383,0.1167,0.0833,0.9167,0.7671,0.2329,0.3064,0.5947,中等,堆叠法
LightGBM + Logistic + NaiveBayes,3,0.858,0.8479,0.1521,0.15,0.0917,0.9083,0.7053,0.2947,0.3312,0.5946,中等,堆叠法
RandomForest + LightGBM + NaiveBayes,3,0.8804,0.8617,0.1383,0.1167,0.0833,0.9167,0.7704,0.2296,0.3057,0.5931,中等,堆叠法
DecisionTree + LightGBM + NeuralNet,3,0.8418,0.8645,0.1355,0.1667,0.1,0.9,0.6712,0.3288,0.3364,0.5891,中等,堆叠法
DecisionTree + SVM + NaiveBayes,3,0.8241,0.8383,0.1617,0.1833,0.1083,0.8917,0.6493,0.3507,0.352,0.5881,中等,堆叠法
CatBoost + SVM + NaiveBayes,3,0.8673,0.8796,0.1204,0.1333,0.0917,0.9083,0.7479,0.2521,0.3082,0.5877,中等,堆叠法
LightGBM + Logistic + NeuralNet,3,0.8606,0.8652,0.1348,0.1333,0.1,0.9,0.734,0.266,0.3136,0.5871,中等,堆叠法
XGBoost + SVM + NeuralNet,3,0.8615,0.8668,0.1332,0.1333,0.1,0.9,0.7382,0.2618,0.3123,0.5869,中等,堆叠法
XGBoost + CatBoost + NeuralNet,3,0.8708,0.8627,0.1373,0.1167,0.1,0.9,0.7688,0.2312,0.3024,0.5866,中等,堆叠法
XGBoost + CatBoost + NaiveBayes,3,0.8681,0.8627,0.1373,0.1167,0.1,0.9,0.775,0.225,0.3012,0.5847,中等,堆叠法
DecisionTree + CatBoost + NaiveBayes,3,0.8335,0.8647,0.1353,0.1667,0.1083,0.8917,0.6802,0.3198,0.3329,0.5832,中等,堆叠法
XGBoost + Logistic + NaiveBayes,3,0.8438,0.8694,0.1306,0.15,0.1083,0.8917,0.7053,0.2947,0.3214,0.5826,中等,堆叠法
RandomForest + XGBoost + NeuralNet,3,0.8689,0.8931,0.1069,0.1167,0.1,0.9,0.7671,0.2329,0.2936,0.5813,较差,建议重新选择模型
DecisionTree + XGBoost + NaiveBayes,3,0.825,0.8451,0.1549,0.1667,0.1167,0.8833,0.6786,0.3214,0.3374,0.5812,中等,堆叠法
DecisionTree + LightGBM + SVM,3,0.835,0.8928,0.1072,0.1667,0.1083,0.8917,0.6677,0.3323,0.327,0.581,中等,堆叠法
DecisionTree + RandomForest + NaiveBayes,3,0.8315,0.8759,0.1241,0.1667,0.1083,0.8917,0.6787,0.3213,0.3298,0.5807,中等,堆叠法
DecisionTree + NaiveBayes + NeuralNet,3,0.831,0.8759,0.1241,0.1667,0.1083,0.8917,0.6787,0.3213,0.3298,0.5804,中等,堆叠法
XGBoost + LightGBM + SVM,3,0.8697,0.9007,0.0993,0.1167,0.1,0.9,0.769,0.231,0.291,0.5803,较差,建议重新选择模型
CatBoost + SVM + NeuralNet,3,0.8699,0.9022,0.0978,0.1167,0.1,0.9,0.7714,0.2286,0.2901,0.58,较差,建议重新选择模型
RandomForest + XGBoost + NaiveBayes,3,0.8662,0.8931,0.1069,0.1167,0.1,0.9,0.7704,0.2296,0.293,0.5796,较差,建议重新选择模型
LightGBM + CatBoost + SVM,3,0.8781,0.9164,0.0836,0.1,0.1,0.9,0.8015,0.1985,0.2748,0.5764,较差,建议重新选择模型
RandomForest + CatBoost + NeuralNet,3,0.8773,0.917,0.083,0.1,0.1,0.9,0.8028,0.1972,0.2743,0.5758,较差,建议重新选择模型
XGBoost + Logistic + NeuralNet,3,0.8465,0.8867,0.1133,0.1333,0.1167,0.8833,0.734,0.266,0.3039,0.5752,中等,堆叠法
CatBoost + Logistic + NaiveBayes,3,0.8523,0.9065,0.0935,0.1333,0.1083,0.8917,0.742,0.258,0.298,0.5751,较差,建议重新选择模型
RandomForest + CatBoost + NaiveBayes,3,0.8747,0.917,0.083,0.1,0.1,0.9,0.8079,0.1921,0.2733,0.574,较差,建议重新选择模型
RandomForest + LightGBM + SVM,3,0.8762,0.929,0.071,0.1,0.1,0.9,0.805,0.195,0.2703,0.5732,较差,建议重新选择模型
DecisionTree + CatBoost + NeuralNet,3,0.8361,0.9138,0.0862,0.15,0.1167,0.8833,0.7044,0.2956,0.3067,0.5714,中等,堆叠法
RandomForest + NaiveBayes + NeuralNet,3,0.8722,0.9394,0.0606,0.1,0.1,0.9,0.8015,0.1985,0.2679,0.5701,较差,建议重新选择模型
DecisionTree + XGBoost + NeuralNet,3,0.8277,0.8942,0.1058,0.15,0.125,0.875,0.7047,0.2953,0.3108,0.5692,中等,堆叠法
DecisionTree + RandomForest + NeuralNet,3,0.8342,0.925,0.075,0.15,0.1167,0.8833,0.7059,0.2941,0.303,0.5686,中等,堆叠法
CatBoost + Logistic + NeuralNet,3,0.8549,0.9239,0.0761,0.1167,0.1167,0.8833,0.7688,0.2312,0.2808,0.5678,较差,建议重新选择模型
LightGBM + Logistic + SVM,3,0.8538,0.9205,0.0795,0.1167,0.1167,0.8833,0.7694,0.2306,0.2816,0.5677,较差,建议重新选择模型
DecisionTree + CatBoost + SVM,3,0.8292,0.9148,0.0852,0.15,0.125,0.875,0.6984,0.3016,0.3059,0.5676,中等,堆叠法
DecisionTree + RandomForest + SVM,3,0.8273,0.91,0.09,0.15,0.125,0.875,0.7052,0.2948,0.306,0.5667,中等,堆叠法
XGBoost + CatBoost + SVM,3,0.8639,0.9227,0.0773,0.1,0.1167,0.8833,0.8015,0.1985,0.2696,0.5667,较差,建议重新选择模型
DecisionTree + Logistic + NaiveBayes,3,0.8091,0.8791,0.1209,0.1667,0.1333,0.8667,0.6788,0.3212,0.3238,0.5665,中等,堆叠法
DecisionTree + SVM + NeuralNet,3,0.8268,0.91,0.09,0.15,0.125,0.875,0.7052,0.2948,0.306,0.5664,中等,堆叠法
XGBoost + LightGBM + Logistic,3,0.8546,0.9385,0.0615,0.1167,0.1167,0.8833,0.7667,0.2333,0.2768,0.5657,较差,建议重新选择模型
DecisionTree + XGBoost + SVM,3,0.8208,0.8991,0.1009,0.15,0.1333,0.8667,0.7012,0.2988,0.3084,0.5646,中等,堆叠法
RandomForest + SVM + NaiveBayes,3,0.8654,0.9408,0.0592,0.1,0.1083,0.8917,0.8135,0.1865,0.2634,0.5644,较差,建议重新选择模型
SVM + NaiveBayes + NeuralNet,3,0.8648,0.9408,0.0592,0.1,0.1083,0.8917,0.8135,0.1865,0.2634,0.5641,较差,建议重新选择模型
RandomForest + XGBoost + SVM,3,0.862,0.937,0.063,0.1,0.1167,0.8833,0.805,0.195,0.2646,0.5633,较差,建议重新选择模型
LightGBM + CatBoost + Logistic,3,0.8631,0.956,0.044,0.1,0.1167,0.8833,0.8007,0.1993,0.2597,0.5614,较差,建议重新选择模型
DecisionTree + RandomForest + LightGBM,3,0.8424,0.9627,0.0373,0.1333,0.1167,0.8833,0.7379,0.2621,0.2803,0.5613,较差,建议重新选择模型
RandomForest + LightGBM + Logistic,3,0.8612,0.9634,0.0366,0.1,0.1167,0.8833,0.8007,0.1993,0.2575,0.5593,较差,建议重新选择模型
XGBoost + Logistic + SVM,3,0.8396,0.9186,0.0814,0.1167,0.1333,0.8667,0.7694,0.2306,0.2789,0.5592,较差,建议重新选择模型
RandomForest + CatBoost + SVM,3,0.8705,0.957,0.043,0.0833,0.1167,0.8833,0.8383,0.1617,0.2469,0.5587,较差,建议重新选择模型
DecisionTree + LightGBM + Logistic,3,0.8199,0.9463,0.0537,0.15,0.1333,0.8667,0.7024,0.2976,0.294,0.557,较差,建议重新选择模型
RandomForest + SVM + NeuralNet,3,0.868,0.9633,0.0367,0.0833,0.1167,0.8833,0.8401,0.1599,0.2446,0.5563,较差,建议重新选择模型
XGBoost + CatBoost + Logistic,3,0.8489,0.9478,0.0522,0.1,0.1333,0.8667,0.8007,0.1993,0.2588,0.5539,较差,建议重新选择模型
RandomForest + Logistic + NaiveBayes,3,0.8503,0.9625,0.0375,0.1,0.125,0.875,0.8041,0.1959,0.2554,0.5529,较差,建议重新选择模型
RandomForest + XGBoost + LightGBM,3,0.8771,0.9818,0.0182,0.0667,0.1167,0.8833,0.8674,0.1326,0.2286,0.5528,较差,建议重新选择模型
CatBoost + Logistic + SVM,3,0.848,0.9519,0.0481,0.1,0.1333,0.8667,0.8017,0.1983,0.2574,0.5527,较差,建议重新选择模型
Logistic + NaiveBayes + NeuralNet,3,0.8498,0.9625,0.0375,0.1,0.125,0.875,0.8041,0.1959,0.2554,0.5526,较差,建议重新选择模型
RandomForest + XGBoost + Logistic,3,0.847,0.9569,0.0431,0.1,0.1333,0.8667,0.8007,0.1993,0.2561,0.5516,较差,建议重新选择模型
RandomForest + LightGBM + CatBoost,3,0.8855,0.986,0.014,0.05,0.1167,0.8833,0.9023,0.0977,0.2154,0.5504,较差,建议重新选择模型
DecisionTree + RandomForest + CatBoost,3,0.8366,0.9686,0.0314,0.1167,0.1333,0.8667,0.7712,0.2288,0.2635,0.5501,较差,建议重新选择模型
Logistic + SVM + NaiveBayes,3,0.8429,0.9519,0.0481,0.1,0.1333,0.8667,0.8136,0.1864,0.255,0.549,较差,建议重新选择模型
RandomForest + CatBoost + Logistic,3,0.8554,0.9787,0.0213,0.0833,0.1333,0.8667,0.8356,0.1644,0.2376,0.5465,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost,3,0.8282,0.9644,0.0356,0.1167,0.1417,0.8583,0.7714,0.2286,0.2631,0.5456,较差,建议重新选择模型
DecisionTree + RandomForest + Logistic,3,0.8123,0.9455,0.0545,0.1333,0.15,0.85,0.7379,0.2621,0.2788,0.5455,较差,建议重新选择模型
DecisionTree + Logistic + SVM,3,0.8049,0.9185,0.0815,0.1333,0.1583,0.8417,0.7347,0.2653,0.2858,0.5454,较差,建议重新选择模型
DecisionTree + CatBoost + Logistic,3,0.8142,0.9556,0.0444,0.1333,0.15,0.85,0.7347,0.2653,0.2764,0.5453,较差,建议重新选择模型
DecisionTree + Logistic + NeuralNet,3,0.8118,0.9455,0.0545,0.1333,0.15,0.85,0.7379,0.2621,0.2788,0.5453,较差,建议重新选择模型
RandomForest + Logistic + NeuralNet,3,0.853,0.9798,0.0202,0.0833,0.1333,0.8667,0.8339,0.1661,0.2376,0.5453,较差,建议重新选择模型
DecisionTree + LightGBM + CatBoost,3,0.8443,1.0,0.0,0.1,0.1333,0.8667,0.8015,0.1985,0.243,0.5437,较差,建议重新选择模型
DecisionTree + XGBoost + Logistic,3,0.8058,0.9381,0.0619,0.1333,0.1583,0.8417,0.7359,0.2641,0.2797,0.5428,较差,建议重新选择模型
RandomForest + XGBoost + CatBoost,3,0.8713,0.9878,0.0122,0.05,0.1333,0.8667,0.9023,0.0977,0.2115,0.5414,较差,建议重新选择模型
DecisionTree + XGBoost + LightGBM,3,0.8358,1.0,0.0,0.1,0.1417,0.8583,0.8025,0.1975,0.2412,0.5385,较差,建议重新选择模型
XGBoost + LightGBM + CatBoost,3,0.879,1.0,0.0,0.0333,0.1333,0.8667,0.9341,0.0659,0.1965,0.5377,较差,建议重新选择模型
RandomForest + Logistic + SVM,3,0.8461,0.9918,0.0082,0.0667,0.15,0.85,0.872,0.128,0.2181,0.5321,较差,建议重新选择模型
Logistic + SVM + NeuralNet,3,0.8456,0.9918,0.0082,0.0667,0.15,0.85,0.872,0.128,0.2181,0.5318,较差,建议重新选择模型
DecisionTree + XGBoost + CatBoost,3,0.8301,1.0,0.0,0.0833,0.1583,0.8417,0.835,0.165,0.2263,0.5282,较差,建议重新选择模型
