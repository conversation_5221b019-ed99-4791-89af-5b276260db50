#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试XGBoost修复
"""

import numpy as np
import sys
from pathlib import Path

# 添加代码目录
sys.path.insert(0, str(Path(__file__).parent / 'code'))

def test_xgboost_after_fix():
    """测试修复后的XGBoost"""
    print("=== 测试修复后的XGBoost ===")
    
    try:
        from hyperparameter_tuning import tune_model
        
        # 生成测试数据
        np.random.seed(42)
        X = np.random.rand(100, 10)
        y = np.random.randint(0, 2, 100)
        
        print(f"数据形状: X={X.shape}, y={y.shape}")
        print(f"类别分布: {np.bincount(y)}")
        
        # 运行超参数调优
        print("\n运行超参数调优...")
        best_params, best_score = tune_model(
            'XGBoost',
            n_trials=3,
            X_train=X,
            y_train=y,
            scoring='roc_auc',
            early_stopping_rounds=5,
            timeout=60
        )
        
        print(f"\n调优结果:")
        print(f"最佳参数: {best_params}")
        print(f"最佳得分: {best_score:.4f}")
        
        if best_score > 0.5:  # 期望合理的得分
            print("✓ XGBoost超参数调优修复成功！")
            return True
        else:
            print("✗ XGBoost调优得分仍然过低")
            return False
            
    except Exception as e:
        print(f"XGBoost调优测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_xgboost_model_training():
    """测试XGBoost模型训练"""
    print("\n=== 测试XGBoost模型训练 ===")
    
    try:
        from model_training import MODEL_TRAINERS
        
        # 生成测试数据
        np.random.seed(42)
        X = np.random.rand(100, 10)
        y = np.random.randint(0, 2, 100)
        
        # 创建训练器
        trainer = MODEL_TRAINERS['XGBoost']
        
        # 使用一些典型参数
        params = {
            'n_estimators': 100,
            'max_depth': 3,
            'learning_rate': 0.1,
            'device': 'cpu'
        }
        
        print("训练XGBoost模型...")
        result = trainer.train_and_evaluate(X, y, X[:20], y[:20], params=params)
        
        print(f"训练完成！")
        # 注意：train_and_evaluate方法直接返回模型对象，不是字典
        print("✓ XGBoost模型训练成功！")
        return True
        
    except Exception as e:
        print(f"XGBoost模型训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("测试XGBoost修复...")
    print("=" * 50)
    
    success_count = 0
    
    # 测试超参数调优
    if test_xgboost_after_fix():
        success_count += 1
    
    # 测试模型训练
    if test_xgboost_model_training():
        success_count += 1
    
    print(f"\n{'='*50}")
    print(f"测试结果: {success_count}/2 通过")
    
    if success_count == 2:
        print("🎉 XGBoost问题已完全修复！")
    else:
        print("⚠ XGBoost仍有部分问题")
    print('='*50)