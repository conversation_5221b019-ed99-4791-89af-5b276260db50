2025-08-29 03:18:44 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-29 03:18:44 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-29 03:18:44 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-29 03:18:45 - data_exploration - INFO - 数据探索器初始化完成，输出目录: d:\Code\MM01U\multi_model_02_updated\output\data_exploration
2025-08-29 03:18:45 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-29 03:18:45 - GUI - INFO - GUI界面初始化完成
2025-08-29 03:19:05 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 16)
2025-08-29 03:19:05 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800}
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9202
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9438
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9438
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9483
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 6, 'min_samples_split': 50, 'min_samples_leaf': 15, 'criterion': 'gini', 'class_weight': None, 'max_features': None}
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9483
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\optimization_history_20250829_031906.html
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\param_importances_20250829_031906.html
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.62 秒
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800}
2025-08-29 03:19:07 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9724
2025-08-29 03:19:11 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9741
2025-08-29 03:19:13 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9783
2025-08-29 03:19:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:19:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9783
2025-08-29 03:19:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:19:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9791
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9791
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9791
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 99, 'max_depth': 32, 'min_samples_split': 13, 'min_samples_leaf': 3, 'max_features': 'sqrt'}
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9791
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 实际执行试验次数: 20/50
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\optimization_history_20250829_031924.html
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\param_importances_20250829_031924.html
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 18.38 秒
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:25 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9784
2025-08-29 03:19:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9793
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 139, 'max_depth': 7, 'learning_rate': 0.29116576212848116, 'subsample': 0.5089809378074099, 'colsample_bytree': 0.9168158328299749}
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9793
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\optimization_history_20250829_031927.html
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\param_importances_20250829_031927.html
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 2.56 秒
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9775
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9799
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9799
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 96, 'max_depth': 4, 'learning_rate': 0.16217936517334897, 'feature_fraction': 0.7159725093210578, 'bagging_fraction': 0.645614570099021}
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9799
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\optimization_history_20250829_031928.html
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\param_importances_20250829_031928.html
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.22 秒
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:33 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9699
2025-08-29 03:19:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:34 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9768
2025-08-29 03:19:34 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:36 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:38 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:41 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:46 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:49 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:53 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:55 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.9792
2025-08-29 03:19:55 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:59 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:02 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9810
2025-08-29 03:20:02 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:04 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:07 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:10 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9827
2025-08-29 03:20:10 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:13 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:26 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:31 - hyperparameter_tuning - INFO - Trial 17: 发现更好的得分 0.9842
2025-08-29 03:20:31 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:34 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:37 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:42 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:45 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:49 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:51 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:53 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:21:03 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:21:12 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9842
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 183, 'depth': 6, 'learning_rate': 0.14765398301499216, 'l2_leaf_reg': 2.24931553797329, 'bagging_temperature': 0.8703340675320387}
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9842
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 实际执行试验次数: 28/50
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\optimization_history_20250829_032120.html
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\param_importances_20250829_032120.html
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 111.66 秒
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9576
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9618
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 9.632750328932904, 'clf__solver': 'lbfgs'}
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/50
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\optimization_history_20250829_032121.html
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\param_importances_20250829_032121.html
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.10 秒
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9560
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9619
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9645
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - Trial 16: 发现更好的得分 0.9670
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - Trial 24: 发现更好的得分 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 8.181191598729157, 'clf__kernel': 'linear'}
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 实际执行试验次数: 40/50
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\optimization_history_20250829_032122.html
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\param_importances_20250829_032122.html
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.52 秒
2025-08-29 03:21:23 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-29 03:21:23 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 03:21:23 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:21:23 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:21:23 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:21:23 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-29 03:21:53 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 7, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-29 03:21:53 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.5000
2025-08-29 03:21:53 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9453
2025-08-29 03:22:23 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'distance', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 03:22:53 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 13, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-29 03:23:23 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 3, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 03:23:53 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9453
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 14, 'clf__weights': 'distance', 'clf__algorithm': 'ball_tree', 'clf__p': 1}
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9453
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\optimization_history_20250829_032353.html
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\param_importances_20250829_032353.html
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 150.37 秒
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9263
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-29 03:23:58 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9541
2025-08-29 03:23:59 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9584
2025-08-29 03:24:05 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9601
2025-08-29 03:24:16 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:24:16 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9610
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9610
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9610
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9610
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9610
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9610
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.006974332966310803}
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9610
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/50
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\optimization_history_20250829_032417.html
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\param_importances_20250829_032417.html
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 24.33 秒
2025-08-29 03:25:32 - training_session_manager - INFO - 创建会话目录结构: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532
2025-08-29 03:25:32 - training_session_manager - INFO - 创建训练会话: 训练_N-2_20250829_032532 (ID: 20250829_032532)
2025-08-29 03:25:32 - training_session_manager - INFO - 创建新会话: 训练_N-2_20250829_032532
2025-08-29 03:25:32 - session_utils - INFO - 创建新会话: 训练_N-2_20250829_032532 (ID: 20250829_032532)
2025-08-29 03:25:32 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 16)
2025-08-29 03:25:32 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-29 03:25:32 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 03:25:32 - model_training - INFO - 准确率: 0.8750
2025-08-29 03:25:32 - model_training - INFO - AUC: 0.9540
2025-08-29 03:25:32 - model_training - INFO - AUPRC: 0.9201
2025-08-29 03:25:32 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:32 - model_training - INFO - 
[[20  3]
 [ 2 15]]
2025-08-29 03:25:32 - model_training - INFO - 
分类报告:
2025-08-29 03:25:32 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.87      0.89        23
           1       0.83      0.88      0.86        17

    accuracy                           0.88        40
   macro avg       0.87      0.88      0.87        40
weighted avg       0.88      0.88      0.88        40

2025-08-29 03:25:32 - model_training - INFO - 训练时间: 0.24 秒
2025-08-29 03:25:32 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8750
2025-08-29 03:25:32 - training_session_manager - INFO - 保存模型 DecisionTree 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\DecisionTree_single_032532.joblib
2025-08-29 03:25:32 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\DecisionTree_single_032532.joblib
2025-08-29 03:25:32 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 03:25:33 - model_training - INFO - 模型名称: Random Forest
2025-08-29 03:25:33 - model_training - INFO - 准确率: 0.9250
2025-08-29 03:25:33 - model_training - INFO - AUC: 0.9923
2025-08-29 03:25:33 - model_training - INFO - AUPRC: 0.9903
2025-08-29 03:25:33 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:33 - model_training - INFO - 
[[21  2]
 [ 1 16]]
2025-08-29 03:25:33 - model_training - INFO - 
分类报告:
2025-08-29 03:25:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.91      0.93        23
           1       0.89      0.94      0.91        17

    accuracy                           0.93        40
   macro avg       0.92      0.93      0.92        40
weighted avg       0.93      0.93      0.93        40

2025-08-29 03:25:33 - model_training - INFO - 训练时间: 0.21 秒
2025-08-29 03:25:33 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.9250
2025-08-29 03:25:33 - training_session_manager - INFO - 保存模型 RandomForest 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\RandomForest_single_032533.joblib
2025-08-29 03:25:33 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\RandomForest_single_032533.joblib
2025-08-29 03:25:33 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 03:25:33 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 03:25:33 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 03:25:33 - model_training - INFO - 模型名称: XGBoost
2025-08-29 03:25:33 - model_training - INFO - 准确率: 0.9500
2025-08-29 03:25:33 - model_training - INFO - AUC: 0.9949
2025-08-29 03:25:33 - model_training - INFO - AUPRC: 0.9938
2025-08-29 03:25:33 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:33 - model_training - INFO - 
[[22  1]
 [ 1 16]]
2025-08-29 03:25:33 - model_training - INFO - 
分类报告:
2025-08-29 03:25:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.96      0.96      0.96        23
           1       0.94      0.94      0.94        17

    accuracy                           0.95        40
   macro avg       0.95      0.95      0.95        40
weighted avg       0.95      0.95      0.95        40

2025-08-29 03:25:33 - model_training - INFO - 训练时间: 0.16 秒
2025-08-29 03:25:33 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9500
2025-08-29 03:25:33 - training_session_manager - INFO - 保存模型 XGBoost 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\XGBoost_single_032533.joblib
2025-08-29 03:25:33 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\XGBoost_single_032533.joblib
2025-08-29 03:25:33 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 03:25:33 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 03:25:33 - model_training - INFO - 模型名称: LightGBM
2025-08-29 03:25:33 - model_training - INFO - 准确率: 0.9500
2025-08-29 03:25:33 - model_training - INFO - AUC: 0.9974
2025-08-29 03:25:33 - model_training - INFO - AUPRC: 0.9967
2025-08-29 03:25:33 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:33 - model_training - INFO - 
[[22  1]
 [ 1 16]]
2025-08-29 03:25:33 - model_training - INFO - 
分类报告:
2025-08-29 03:25:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.96      0.96      0.96        23
           1       0.94      0.94      0.94        17

    accuracy                           0.95        40
   macro avg       0.95      0.95      0.95        40
weighted avg       0.95      0.95      0.95        40

2025-08-29 03:25:33 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 03:25:33 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.9500
2025-08-29 03:25:33 - training_session_manager - INFO - 保存模型 LightGBM 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\LightGBM_single_032533.joblib
2025-08-29 03:25:33 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\LightGBM_single_032533.joblib
2025-08-29 03:25:33 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 03:25:33 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 03:25:33 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 03:25:34 - model_training - INFO - 模型名称: CatBoost
2025-08-29 03:25:34 - model_training - INFO - 准确率: 0.9250
2025-08-29 03:25:34 - model_training - INFO - AUC: 0.9949
2025-08-29 03:25:34 - model_training - INFO - AUPRC: 0.9938
2025-08-29 03:25:34 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:34 - model_training - INFO - 
[[21  2]
 [ 1 16]]
2025-08-29 03:25:34 - model_training - INFO - 
分类报告:
2025-08-29 03:25:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.91      0.93        23
           1       0.89      0.94      0.91        17

    accuracy                           0.93        40
   macro avg       0.92      0.93      0.92        40
weighted avg       0.93      0.93      0.93        40

2025-08-29 03:25:34 - model_training - INFO - 训练时间: 0.65 秒
2025-08-29 03:25:34 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.9250
2025-08-29 03:25:34 - training_session_manager - INFO - 保存模型 CatBoost 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\CatBoost_single_032534.joblib
2025-08-29 03:25:34 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\CatBoost_single_032534.joblib
2025-08-29 03:25:34 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 03:25:34 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 03:25:34 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 03:25:35 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 03:25:35 - model_training - INFO - 准确率: 0.9500
2025-08-29 03:25:35 - model_training - INFO - AUC: 0.9872
2025-08-29 03:25:35 - model_training - INFO - AUPRC: 0.9840
2025-08-29 03:25:35 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:35 - model_training - INFO - 
[[22  1]
 [ 1 16]]
2025-08-29 03:25:35 - model_training - INFO - 
分类报告:
2025-08-29 03:25:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.96      0.96      0.96        23
           1       0.94      0.94      0.94        17

    accuracy                           0.95        40
   macro avg       0.95      0.95      0.95        40
weighted avg       0.95      0.95      0.95        40

2025-08-29 03:25:35 - model_training - INFO - 训练时间: 1.40 秒
2025-08-29 03:25:35 - model_training - INFO - 模型 Logistic 性能: 准确率=0.9500
2025-08-29 03:25:35 - training_session_manager - INFO - 保存模型 Logistic 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\Logistic_single_032535.joblib
2025-08-29 03:25:35 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\Logistic_single_032535.joblib
2025-08-29 03:25:35 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 03:25:35 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 03:25:35 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 03:25:35 - model_training - INFO - 模型名称: SVM
2025-08-29 03:25:35 - model_training - INFO - 准确率: 0.9000
2025-08-29 03:25:35 - model_training - INFO - AUC: 0.9821
2025-08-29 03:25:35 - model_training - INFO - AUPRC: 0.9770
2025-08-29 03:25:35 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:35 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-29 03:25:35 - model_training - INFO - 
分类报告:
2025-08-29 03:25:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-29 03:25:35 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 03:25:35 - model_training - INFO - 模型 SVM 性能: 准确率=0.9000
2025-08-29 03:25:35 - training_session_manager - INFO - 保存模型 SVM 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\SVM_single_032535.joblib
2025-08-29 03:25:35 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\SVM_single_032535.joblib
2025-08-29 03:25:35 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 03:25:35 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 03:25:35 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 03:25:35 - model_training - INFO - 模型名称: KNN
2025-08-29 03:25:35 - model_training - INFO - 准确率: 0.9000
2025-08-29 03:25:35 - model_training - INFO - AUC: 0.9847
2025-08-29 03:25:35 - model_training - INFO - AUPRC: 0.9804
2025-08-29 03:25:35 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:35 - model_training - INFO - 
[[22  1]
 [ 3 14]]
2025-08-29 03:25:35 - model_training - INFO - 
分类报告:
2025-08-29 03:25:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.96      0.92        23
           1       0.93      0.82      0.88        17

    accuracy                           0.90        40
   macro avg       0.91      0.89      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-29 03:25:35 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 03:25:35 - model_training - INFO - 模型 KNN 性能: 准确率=0.9000
2025-08-29 03:25:35 - training_session_manager - INFO - 保存模型 KNN 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\KNN_single_032535.joblib
2025-08-29 03:25:35 - model_training - INFO - 模型 KNN 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\KNN_single_032535.joblib
2025-08-29 03:25:35 - model_training - INFO - 模型 KNN 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\KNN_results.joblib
2025-08-29 03:25:35 - model_training - INFO - 模型名称: Naive Bayes
2025-08-29 03:25:35 - model_training - INFO - 准确率: 0.8500
2025-08-29 03:25:35 - model_training - INFO - AUC: 0.9540
2025-08-29 03:25:35 - model_training - INFO - AUPRC: 0.9376
2025-08-29 03:25:35 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:35 - model_training - INFO - 
[[21  2]
 [ 4 13]]
2025-08-29 03:25:35 - model_training - INFO - 
分类报告:
2025-08-29 03:25:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.84      0.91      0.88        23
           1       0.87      0.76      0.81        17

    accuracy                           0.85        40
   macro avg       0.85      0.84      0.84        40
weighted avg       0.85      0.85      0.85        40

2025-08-29 03:25:35 - model_training - INFO - 训练时间: 0.10 秒
2025-08-29 03:25:35 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8500
2025-08-29 03:25:35 - training_session_manager - INFO - 保存模型 NaiveBayes 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\NaiveBayes_single_032535.joblib
2025-08-29 03:25:35 - model_training - INFO - 模型 NaiveBayes 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\NaiveBayes_single_032535.joblib
2025-08-29 03:25:35 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NaiveBayes_results.joblib
2025-08-29 03:25:36 - model_training - INFO - [NeuralNet] 使用传入的scaler实例
2025-08-29 03:25:36 - model_training - INFO - [NeuralNet] 使用Pipeline进行标准化
2025-08-29 03:25:36 - model_training - INFO - 模型名称: Neural Network
2025-08-29 03:25:36 - model_training - INFO - 准确率: 0.8750
2025-08-29 03:25:36 - model_training - INFO - AUC: 0.9847
2025-08-29 03:25:36 - model_training - INFO - AUPRC: 0.9769
2025-08-29 03:25:36 - model_training - INFO - 混淆矩阵:
2025-08-29 03:25:36 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-29 03:25:36 - model_training - INFO - 
分类报告:
2025-08-29 03:25:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-29 03:25:36 - model_training - INFO - 训练时间: 0.23 秒
2025-08-29 03:25:36 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-29 03:25:36 - training_session_manager - INFO - 保存模型 NeuralNet 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\NeuralNet_single_032536.joblib
2025-08-29 03:25:36 - model_training - INFO - 模型 NeuralNet 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\NeuralNet_single_032536.joblib
2025-08-29 03:25:36 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\NeuralNet_results.joblib
2025-08-29 03:25:36 - delong_test - INFO - 开始DeLong检验，比较10个模型
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs RandomForest: AUC1=0.9540, AUC2=0.9923, p=0.165713
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs XGBoost: AUC1=0.9540, AUC2=0.9949, p=0.133052
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs LightGBM: AUC1=0.9540, AUC2=0.9974, p=0.104314
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs CatBoost: AUC1=0.9540, AUC2=0.9949, p=0.133052
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs Logistic: AUC1=0.9540, AUC2=0.9872, p=0.250589
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs SVM: AUC1=0.9540, AUC2=0.9821, p=0.352786
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs KNN: AUC1=0.9540, AUC2=0.9847, p=0.302096
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs NaiveBayes: AUC1=0.9540, AUC2=0.9540, p=1.000000
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs NeuralNet: AUC1=0.9540, AUC2=0.9847, p=0.323391
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs XGBoost: AUC1=0.9923, AUC2=0.9949, p=0.799351
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs LightGBM: AUC1=0.9923, AUC2=0.9974, p=0.556514
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs CatBoost: AUC1=0.9923, AUC2=0.9949, p=0.799351
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs Logistic: AUC1=0.9923, AUC2=0.9872, p=0.715258
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs SVM: AUC1=0.9923, AUC2=0.9821, p=0.537918
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs KNN: AUC1=0.9923, AUC2=0.9847, p=0.623045
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs NaiveBayes: AUC1=0.9923, AUC2=0.9540, p=0.199202
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs NeuralNet: AUC1=0.9923, AUC2=0.9847, p=0.670420
2025-08-29 03:25:36 - delong_test - INFO - 比较XGBoost vs LightGBM: AUC1=0.9949, AUC2=0.9974, p=0.722155
2025-08-29 03:25:36 - delong_test - INFO - 比较XGBoost vs CatBoost: AUC1=0.9949, AUC2=0.9949, p=1.000000
2025-08-29 03:25:36 - delong_test - INFO - 比较XGBoost vs Logistic: AUC1=0.9949, AUC2=0.9872, p=0.559288
2025-08-29 03:25:36 - delong_test - INFO - 比较XGBoost vs SVM: AUC1=0.9949, AUC2=0.9821, p=0.420414
2025-08-29 03:25:36 - delong_test - INFO - 比较XGBoost vs KNN: AUC1=0.9949, AUC2=0.9847, p=0.490094
2025-08-29 03:25:36 - delong_test - INFO - 比较XGBoost vs NaiveBayes: AUC1=0.9949, AUC2=0.9540, p=0.165092
2025-08-29 03:25:36 - delong_test - INFO - 比较XGBoost vs NeuralNet: AUC1=0.9949, AUC2=0.9847, p=0.555493
2025-08-29 03:25:36 - delong_test - INFO - 比较LightGBM vs CatBoost: AUC1=0.9974, AUC2=0.9949, p=0.722155
2025-08-29 03:25:36 - delong_test - INFO - 比较LightGBM vs Logistic: AUC1=0.9974, AUC2=0.9872, p=0.398956
2025-08-29 03:25:36 - delong_test - INFO - 比较LightGBM vs SVM: AUC1=0.9974, AUC2=0.9821, p=0.307730
2025-08-29 03:25:36 - delong_test - INFO - 比较LightGBM vs KNN: AUC1=0.9974, AUC2=0.9847, p=0.358749
2025-08-29 03:25:36 - delong_test - INFO - 比较LightGBM vs NaiveBayes: AUC1=0.9974, AUC2=0.9540, p=0.134368
2025-08-29 03:25:36 - delong_test - INFO - 比较LightGBM vs NeuralNet: AUC1=0.9974, AUC2=0.9847, p=0.441080
2025-08-29 03:25:36 - delong_test - INFO - 比较CatBoost vs Logistic: AUC1=0.9949, AUC2=0.9872, p=0.559288
2025-08-29 03:25:36 - delong_test - INFO - 比较CatBoost vs SVM: AUC1=0.9949, AUC2=0.9821, p=0.420414
2025-08-29 03:25:36 - delong_test - INFO - 比较CatBoost vs KNN: AUC1=0.9949, AUC2=0.9847, p=0.490094
2025-08-29 03:25:36 - delong_test - INFO - 比较CatBoost vs NaiveBayes: AUC1=0.9949, AUC2=0.9540, p=0.165092
2025-08-29 03:25:36 - delong_test - INFO - 比较CatBoost vs NeuralNet: AUC1=0.9949, AUC2=0.9847, p=0.555493
2025-08-29 03:25:36 - delong_test - INFO - 比较Logistic vs SVM: AUC1=0.9872, AUC2=0.9821, p=0.783711
2025-08-29 03:25:36 - delong_test - INFO - 比较Logistic vs KNN: AUC1=0.9872, AUC2=0.9847, p=0.885436
2025-08-29 03:25:36 - delong_test - INFO - 比较Logistic vs NaiveBayes: AUC1=0.9872, AUC2=0.9540, p=0.284327
2025-08-29 03:25:36 - delong_test - INFO - 比较Logistic vs NeuralNet: AUC1=0.9872, AUC2=0.9847, p=0.897797
2025-08-29 03:25:36 - delong_test - INFO - 比较SVM vs KNN: AUC1=0.9821, AUC2=0.9847, p=0.897520
2025-08-29 03:25:36 - delong_test - INFO - 比较SVM vs NaiveBayes: AUC1=0.9821, AUC2=0.9540, p=0.383828
2025-08-29 03:25:36 - delong_test - INFO - 比较SVM vs NeuralNet: AUC1=0.9821, AUC2=0.9847, p=0.906654
2025-08-29 03:25:36 - delong_test - INFO - 比较KNN vs NaiveBayes: AUC1=0.9847, AUC2=0.9540, p=0.334529
2025-08-29 03:25:36 - delong_test - INFO - 比较KNN vs NeuralNet: AUC1=0.9847, AUC2=0.9847, p=1.000000
2025-08-29 03:25:36 - delong_test - INFO - 比较NaiveBayes vs NeuralNet: AUC1=0.9540, AUC2=0.9847, p=0.353201
2025-08-29 03:25:36 - delong_test - INFO - DeLong检验完成，共45个比较
2025-08-29 03:28:29 - config_manager - INFO - 默认配置已初始化
2025-08-29 03:28:29 - config_manager - INFO - 注册预处理器: current_scaler (StandardScaler)
2025-08-29 03:28:29 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-29 03:28:29 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 03:28:30 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 03:28:30 - model_training - INFO - 准确率: 0.7250
2025-08-29 03:28:30 - model_training - INFO - AUC: 0.8210
2025-08-29 03:28:30 - model_training - INFO - AUPRC: 0.6807
2025-08-29 03:28:30 - model_training - INFO - 混淆矩阵:
2025-08-29 03:28:30 - model_training - INFO - 
[[15  8]
 [ 3 14]]
2025-08-29 03:28:30 - model_training - INFO - 
分类报告:
2025-08-29 03:28:30 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-29 03:28:30 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 03:28:30 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-29 03:28:30 - training_session_manager - INFO - 保存模型 DecisionTree 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\DecisionTree_single_032830.joblib
2025-08-29 03:28:30 - model_training - INFO - 模型 DecisionTree 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\DecisionTree_single_032830.joblib
2025-08-29 03:28:30 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 03:28:30 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7523
2025-08-29 03:28:30 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 03:28:30 - model_training - INFO - 模型名称: Random Forest
2025-08-29 03:28:30 - model_training - INFO - 准确率: 0.8500
2025-08-29 03:28:30 - model_training - INFO - AUC: 0.9501
2025-08-29 03:28:30 - model_training - INFO - AUPRC: 0.9410
2025-08-29 03:28:30 - model_training - INFO - 混淆矩阵:
2025-08-29 03:28:30 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-29 03:28:30 - model_training - INFO - 
分类报告:
2025-08-29 03:28:30 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 03:28:30 - model_training - INFO - 训练时间: 0.18 秒
2025-08-29 03:28:30 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 03:28:30 - training_session_manager - INFO - 保存模型 RandomForest 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\RandomForest_single_032830.joblib
2025-08-29 03:28:30 - model_training - INFO - 模型 RandomForest 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\RandomForest_single_032830.joblib
2025-08-29 03:28:30 - model_training - INFO - 模型 RandomForest 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 03:28:31 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-29 03:28:31 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-29 03:28:31 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 03:28:31 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 03:28:31 - model_training - INFO - 模型名称: XGBoost
2025-08-29 03:28:31 - model_training - INFO - 准确率: 0.8250
2025-08-29 03:28:31 - model_training - INFO - AUC: 0.9437
2025-08-29 03:28:31 - model_training - INFO - AUPRC: 0.9387
2025-08-29 03:28:31 - model_training - INFO - 混淆矩阵:
2025-08-29 03:28:31 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-29 03:28:31 - model_training - INFO - 
分类报告:
2025-08-29 03:28:31 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 03:28:31 - model_training - INFO - 训练时间: 0.13 秒
2025-08-29 03:28:31 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-29 03:28:31 - training_session_manager - INFO - 保存模型 XGBoost 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\XGBoost_single_032831.joblib
2025-08-29 03:28:31 - model_training - INFO - 模型 XGBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\XGBoost_single_032831.joblib
2025-08-29 03:28:31 - model_training - INFO - 模型 XGBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 03:28:32 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-29 03:28:32 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-29 03:28:32 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 03:28:32 - model_training - INFO - 模型名称: LightGBM
2025-08-29 03:28:32 - model_training - INFO - 准确率: 0.8750
2025-08-29 03:28:32 - model_training - INFO - AUC: 0.9642
2025-08-29 03:28:32 - model_training - INFO - AUPRC: 0.9541
2025-08-29 03:28:32 - model_training - INFO - 混淆矩阵:
2025-08-29 03:28:32 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-29 03:28:32 - model_training - INFO - 
分类报告:
2025-08-29 03:28:32 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 03:28:32 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 03:28:32 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 03:28:32 - training_session_manager - INFO - 保存模型 LightGBM 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\LightGBM_single_032832.joblib
2025-08-29 03:28:32 - model_training - INFO - 模型 LightGBM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\LightGBM_single_032832.joblib
2025-08-29 03:28:32 - model_training - INFO - 模型 LightGBM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 03:28:32 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-29 03:28:32 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-29 03:28:33 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 03:28:33 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 03:28:53 - model_training - INFO - 模型名称: CatBoost
2025-08-29 03:28:53 - model_training - INFO - 准确率: 0.8500
2025-08-29 03:28:53 - model_training - INFO - AUC: 0.9668
2025-08-29 03:28:53 - model_training - INFO - AUPRC: 0.9616
2025-08-29 03:28:53 - model_training - INFO - 混淆矩阵:
2025-08-29 03:28:53 - model_training - INFO - 
[[18  5]
 [ 1 16]]
2025-08-29 03:28:53 - model_training - INFO - 
分类报告:
2025-08-29 03:28:53 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 03:28:53 - model_training - INFO - 训练时间: 20.78 秒
2025-08-29 03:28:53 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8500
2025-08-29 03:28:53 - training_session_manager - INFO - 保存模型 CatBoost 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\CatBoost_single_032853.joblib
2025-08-29 03:28:53 - model_training - INFO - 模型 CatBoost 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\CatBoost_single_032853.joblib
2025-08-29 03:28:53 - model_training - INFO - 模型 CatBoost 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 03:28:54 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8817
2025-08-29 03:28:54 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 03:28:54 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 03:28:54 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 03:28:54 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 03:28:54 - model_training - INFO - 准确率: 0.7750
2025-08-29 03:28:54 - model_training - INFO - AUC: 0.9028
2025-08-29 03:28:54 - model_training - INFO - AUPRC: 0.8685
2025-08-29 03:28:54 - model_training - INFO - 混淆矩阵:
2025-08-29 03:28:54 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-29 03:28:54 - model_training - INFO - 
分类报告:
2025-08-29 03:28:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 03:28:54 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 03:28:54 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 03:28:54 - training_session_manager - INFO - 保存模型 Logistic 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\Logistic_single_032854.joblib
2025-08-29 03:28:54 - model_training - INFO - 模型 Logistic 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\Logistic_single_032854.joblib
2025-08-29 03:28:54 - model_training - INFO - 模型 Logistic 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 03:28:55 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-29 03:28:55 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-29 03:28:55 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 03:28:55 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 03:28:55 - model_training - INFO - 模型名称: SVM
2025-08-29 03:28:55 - model_training - INFO - 准确率: 0.8250
2025-08-29 03:28:55 - model_training - INFO - AUC: 0.9258
2025-08-29 03:28:55 - model_training - INFO - AUPRC: 0.9259
2025-08-29 03:28:55 - model_training - INFO - 混淆矩阵:
2025-08-29 03:28:55 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-29 03:28:55 - model_training - INFO - 
分类报告:
2025-08-29 03:28:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-29 03:28:55 - model_training - INFO - 训练时间: 0.13 秒
2025-08-29 03:28:55 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-29 03:28:55 - training_session_manager - INFO - 保存模型 SVM 到: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\SVM_single_032855.joblib
2025-08-29 03:28:55 - model_training - INFO - 模型 SVM 已保存到会话: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_032532\models\SVM_single_032855.joblib
2025-08-29 03:28:55 - model_training - INFO - 模型 SVM 同时保存到缓存: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 03:28:56 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-29 03:28:56 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 03:28:56 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 03:28:56 - model_training - INFO - [KNN] 使用Pipeline进行标准化
