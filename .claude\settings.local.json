{"permissions": {"allow": ["<PERSON><PERSON>(claude mcp --help)", "Bash(export CLAUDE_CODE_GIT_BASH_PATH=\"/usr/bin/bash.exe\")", "Bash(export CLAUDE_CODE_GIT_BASH_PATH=\"/usr/bin/bash\")", "Bash(unset CLAUDE_CODE_GIT_BASH_PATH)", "<PERSON><PERSON>(python:*)", "Bash(../../anaconda/envs/multi_model/python.exe:*)", "Bash(D:anacondaenvsmulti_modelpython.exe:*)", "Bash(find:*)", "Bash(D:/anaconda/envs/multi_model/python.exe analyze_knn_performance.py)", "Bash(D:/anaconda/envs/multi_model/python.exe -c \"import sys; print(sys.executable)\")"], "deny": [], "ask": [], "additionalDirectories": ["D:\\d\\Code\\MM01U"]}}