﻿模型组合,组合大小,平均性能得分,平均Q统计量,平均Q统计量多样性,平均不一致性,平均双错度量,平均双错多样性,平均相关系数,平均相关性多样性,综合多样性得分,综合得分,多样性等级,推荐集成方法
XGBoost + LightGBM + NaiveBayes + NeuralNet,4,0.874,0.8034,0.1966,0.1417,0.0792,0.9208,0.7198,0.2802,0.3417,0.6078,中等,堆叠法
LightGBM + CatBoost + NaiveBayes + NeuralNet,4,0.8803,0.823,0.177,0.1292,0.0792,0.9208,0.7471,0.2529,0.3266,0.6035,中等,堆叠法
LightGBM + SVM + NaiveBayes + NeuralNet,4,0.8733,0.8359,0.1641,0.1333,0.0833,0.9167,0.7421,0.2579,0.3242,0.5987,中等,堆叠法
DecisionTree + LightGBM + NaiveBayes + NeuralNet,4,0.848,0.8298,0.1702,0.1667,0.0917,0.9083,0.6746,0.3254,0.3478,0.5979,中等,堆叠法
XGBoost + LightGBM + SVM + NaiveBayes,4,0.8688,0.8417,0.1583,0.1375,0.0875,0.9125,0.7333,0.2667,0.3246,0.5967,中等,堆叠法
DecisionTree + LightGBM + SVM + NaiveBayes,4,0.8428,0.8387,0.1613,0.1708,0.0958,0.9042,0.6687,0.3313,0.3467,0.5948,中等,堆叠法
RandomForest + LightGBM + NaiveBayes + NeuralNet,4,0.8789,0.8566,0.1434,0.1208,0.0833,0.9167,0.7607,0.2393,0.3105,0.5947,中等,堆叠法
XGBoost + LightGBM + SVM + NeuralNet,4,0.8708,0.853,0.147,0.1292,0.0917,0.9083,0.7448,0.2552,0.3156,0.5932,中等,堆叠法
LightGBM + CatBoost + SVM + NaiveBayes,4,0.8752,0.8594,0.1406,0.125,0.0875,0.9125,0.7593,0.2407,0.3103,0.5928,中等,堆叠法
XGBoost + CatBoost + NaiveBayes + NeuralNet,4,0.8697,0.8526,0.1474,0.1292,0.0917,0.9083,0.7471,0.2529,0.3152,0.5924,中等,堆叠法
LightGBM + CatBoost + SVM + NeuralNet,4,0.8772,0.8706,0.1294,0.1167,0.0917,0.9083,0.77,0.23,0.3015,0.5893,中等,堆叠法
LightGBM + Logistic + NaiveBayes + NeuralNet,4,0.8621,0.8598,0.1402,0.1333,0.0958,0.9042,0.7367,0.2633,0.3155,0.5888,中等,堆叠法
XGBoost + SVM + NaiveBayes + NeuralNet,4,0.8627,0.8687,0.1313,0.1333,0.0958,0.9042,0.7421,0.2579,0.3118,0.5873,中等,堆叠法
XGBoost + LightGBM + Logistic + NaiveBayes,4,0.8576,0.8673,0.1327,0.1375,0.1,0.9,0.7289,0.2711,0.3153,0.5864,中等,堆叠法
RandomForest + LightGBM + SVM + NaiveBayes,4,0.8737,0.885,0.115,0.1167,0.0917,0.9083,0.7754,0.2246,0.2961,0.5849,较差,建议重新选择模型
DecisionTree + LightGBM + SVM + NeuralNet,4,0.8448,0.8745,0.1255,0.1542,0.1042,0.8958,0.6956,0.3044,0.3239,0.5844,中等,堆叠法
DecisionTree + RandomForest + LightGBM + NaiveBayes,4,0.8484,0.8789,0.1211,0.15,0.1,0.9,0.708,0.292,0.3197,0.584,中等,堆叠法
DecisionTree + XGBoost + NaiveBayes + NeuralNet,4,0.8373,0.8595,0.1405,0.1583,0.1083,0.8917,0.6914,0.3086,0.3297,0.5835,中等,堆叠法
RandomForest + XGBoost + NaiveBayes + NeuralNet,4,0.8683,0.8872,0.1128,0.1208,0.0958,0.9042,0.7607,0.2393,0.2988,0.5835,较差,建议重新选择模型
XGBoost + CatBoost + SVM + NaiveBayes,4,0.8645,0.8773,0.1227,0.125,0.1,0.9,0.7593,0.2407,0.3024,0.5835,中等,堆叠法
RandomForest + XGBoost + LightGBM + NeuralNet,4,0.8764,0.8875,0.1125,0.1083,0.0958,0.9042,0.7839,0.2161,0.2903,0.5833,较差,建议重新选择模型
XGBoost + LightGBM + Logistic + NeuralNet,4,0.8596,0.876,0.124,0.1292,0.1042,0.8958,0.7421,0.2579,0.3067,0.5831,中等,堆叠法
DecisionTree + CatBoost + NaiveBayes + NeuralNet,4,0.8437,0.8791,0.1209,0.1542,0.1042,0.8958,0.7011,0.2989,0.3215,0.5826,中等,堆叠法
DecisionTree + XGBoost + SVM + NaiveBayes,4,0.8322,0.8567,0.1433,0.1625,0.1125,0.8875,0.6855,0.3145,0.3321,0.5822,中等,堆叠法
XGBoost + LightGBM + CatBoost + NeuralNet,4,0.8778,0.8773,0.1227,0.1,0.1,0.9,0.8014,0.1986,0.2865,0.5822,较差,建议重新选择模型
LightGBM + CatBoost + Logistic + NaiveBayes,4,0.8639,0.8859,0.1141,0.125,0.1,0.9,0.7558,0.2442,0.3006,0.5822,中等,堆叠法
RandomForest + XGBoost + LightGBM + NaiveBayes,4,0.8744,0.8875,0.1125,0.1083,0.0958,0.9042,0.7866,0.2134,0.2898,0.5821,较差,建议重新选择模型
CatBoost + SVM + NaiveBayes + NeuralNet,4,0.869,0.8962,0.1038,0.1208,0.0958,0.9042,0.7685,0.2315,0.2945,0.5818,较差,建议重新选择模型
DecisionTree + CatBoost + SVM + NaiveBayes,4,0.8385,0.8744,0.1256,0.1583,0.1083,0.8917,0.694,0.306,0.3247,0.5816,中等,堆叠法
DecisionTree + LightGBM + CatBoost + NaiveBayes,4,0.8498,0.8783,0.1217,0.1417,0.1042,0.8958,0.7254,0.2746,0.3131,0.5815,中等,堆叠法
RandomForest + LightGBM + SVM + NeuralNet,4,0.8757,0.8962,0.1038,0.1083,0.0958,0.9042,0.7876,0.2124,0.2869,0.5813,较差,建议重新选择模型
DecisionTree + XGBoost + LightGBM + NaiveBayes,4,0.8435,0.8685,0.1315,0.1458,0.1083,0.8917,0.7161,0.2839,0.3183,0.5809,中等,堆叠法
XGBoost + LightGBM + CatBoost + NaiveBayes,4,0.8758,0.8773,0.1227,0.1,0.1,0.9,0.8056,0.1944,0.2857,0.5808,较差,建议重新选择模型
RandomForest + LightGBM + CatBoost + NeuralNet,4,0.8827,0.8995,0.1005,0.0958,0.0958,0.9042,0.8103,0.1897,0.2777,0.5802,较差,建议重新选择模型
XGBoost + CatBoost + SVM + NeuralNet,4,0.8665,0.8886,0.1114,0.1167,0.1042,0.8958,0.77,0.23,0.2936,0.5801,较差,建议重新选择模型
LightGBM + Logistic + SVM + NaiveBayes,4,0.8569,0.8822,0.1178,0.1292,0.1042,0.8958,0.7503,0.2497,0.3032,0.5801,中等,堆叠法
DecisionTree + LightGBM + Logistic + NaiveBayes,4,0.8316,0.8722,0.1278,0.1625,0.1125,0.8875,0.6829,0.3171,0.328,0.5798,中等,堆叠法
LightGBM + CatBoost + Logistic + NeuralNet,4,0.8659,0.8945,0.1055,0.1167,0.1042,0.8958,0.768,0.232,0.2922,0.579,较差,建议重新选择模型
RandomForest + LightGBM + CatBoost + NaiveBayes,4,0.8807,0.8995,0.1005,0.0958,0.0958,0.9042,0.8139,0.1861,0.277,0.5788,较差,建议重新选择模型
RandomForest + CatBoost + NaiveBayes + NeuralNet,4,0.8746,0.9089,0.0911,0.1083,0.0958,0.9042,0.7884,0.2116,0.283,0.5788,较差,建议重新选择模型
DecisionTree + RandomForest + LightGBM + NeuralNet,4,0.8504,0.9035,0.0965,0.1417,0.1042,0.8958,0.7205,0.2795,0.3065,0.5784,中等,堆叠法
XGBoost + Logistic + NaiveBayes + NeuralNet,4,0.8514,0.8854,0.1146,0.1333,0.1083,0.8917,0.7367,0.2633,0.3054,0.5784,中等,堆叠法
DecisionTree + LightGBM + CatBoost + NeuralNet,4,0.8518,0.9028,0.0972,0.1333,0.1083,0.8917,0.7364,0.2636,0.3002,0.576,中等,堆叠法
RandomForest + XGBoost + SVM + NaiveBayes,4,0.8631,0.9038,0.0962,0.1167,0.1042,0.8958,0.7754,0.2246,0.2879,0.5755,较差,建议重新选择模型
DecisionTree + XGBoost + LightGBM + NeuralNet,4,0.8455,0.893,0.107,0.1375,0.1125,0.8875,0.7281,0.2719,0.3052,0.5753,中等,堆叠法
RandomForest + LightGBM + Logistic + NaiveBayes,4,0.8625,0.9089,0.0911,0.1167,0.1042,0.8958,0.7701,0.2299,0.2875,0.575,较差,建议重新选择模型
DecisionTree + RandomForest + SVM + NaiveBayes,4,0.8371,0.8912,0.1088,0.15,0.1125,0.8875,0.7117,0.2883,0.3128,0.5749,中等,堆叠法
DecisionTree + RandomForest + NaiveBayes + NeuralNet,4,0.8422,0.904,0.096,0.1458,0.1083,0.8917,0.7162,0.2838,0.3076,0.5749,中等,堆叠法
DecisionTree + SVM + NaiveBayes + NeuralNet,4,0.8367,0.8912,0.1088,0.15,0.1125,0.8875,0.7117,0.2883,0.3128,0.5747,中等,堆叠法
XGBoost + CatBoost + Logistic + NaiveBayes,4,0.8533,0.8966,0.1034,0.125,0.1125,0.8875,0.7558,0.2442,0.2949,0.5741,较差,建议重新选择模型
CatBoost + Logistic + NaiveBayes + NeuralNet,4,0.8578,0.9138,0.0862,0.1208,0.1083,0.8917,0.764,0.236,0.2877,0.5727,较差,建议重新选择模型
DecisionTree + RandomForest + CatBoost + NaiveBayes,4,0.8441,0.9065,0.0935,0.1375,0.1125,0.8875,0.7345,0.2655,0.2999,0.572,较差,建议重新选择模型
RandomForest + XGBoost + SVM + NeuralNet,4,0.8651,0.9151,0.0849,0.1083,0.1083,0.8917,0.7876,0.2124,0.2788,0.5719,较差,建议重新选择模型
LightGBM + Logistic + SVM + NeuralNet,4,0.8589,0.9021,0.0979,0.1125,0.1125,0.8875,0.7784,0.2216,0.2849,0.5719,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + NaiveBayes,4,0.8377,0.8946,0.1054,0.1417,0.1167,0.8833,0.7248,0.2752,0.3058,0.5718,中等,堆叠法
DecisionTree + XGBoost + SVM + NeuralNet,4,0.8342,0.8925,0.1075,0.1458,0.1208,0.8792,0.7123,0.2877,0.3094,0.5718,中等,堆叠法
RandomForest + LightGBM + Logistic + NeuralNet,4,0.8645,0.9175,0.0825,0.1083,0.1083,0.8917,0.7839,0.2161,0.2788,0.5716,较差,建议重新选择模型
XGBoost + Logistic + SVM + NaiveBayes,4,0.8463,0.8961,0.1039,0.1292,0.1167,0.8833,0.7503,0.2497,0.2965,0.5714,较差,建议重新选择模型
DecisionTree + CatBoost + SVM + NeuralNet,4,0.8405,0.9102,0.0898,0.1417,0.1167,0.8833,0.7199,0.2801,0.3021,0.5713,中等,堆叠法
RandomForest + XGBoost + CatBoost + NeuralNet,4,0.8721,0.9151,0.0849,0.0958,0.1083,0.8917,0.8103,0.1897,0.2705,0.5713,较差,建议重新选择模型
RandomForest + CatBoost + SVM + NaiveBayes,4,0.8694,0.9236,0.0764,0.1042,0.1042,0.8958,0.8019,0.1981,0.273,0.5712,较差,建议重新选择模型
XGBoost + CatBoost + Logistic + NeuralNet,4,0.8553,0.9053,0.0947,0.1167,0.1167,0.8833,0.768,0.232,0.2865,0.5709,较差,建议重新选择模型
DecisionTree + RandomForest + LightGBM + SVM,4,0.8452,0.9236,0.0764,0.1375,0.1125,0.8875,0.7289,0.2711,0.2959,0.5705,较差,建议重新选择模型
RandomForest + XGBoost + CatBoost + NaiveBayes,4,0.8701,0.9151,0.0849,0.0958,0.1083,0.8917,0.8139,0.1861,0.2698,0.5699,较差,建议重新选择模型
DecisionTree + LightGBM + Logistic + NeuralNet,4,0.8335,0.9054,0.0946,0.1458,0.1208,0.8792,0.7113,0.2887,0.3057,0.5696,中等,堆叠法
DecisionTree + XGBoost + CatBoost + NaiveBayes,4,0.8392,0.8931,0.1069,0.1333,0.1208,0.8792,0.7422,0.2578,0.2995,0.5693,较差,建议重新选择模型
DecisionTree + XGBoost + Logistic + NaiveBayes,4,0.8209,0.8829,0.1171,0.1542,0.1292,0.8708,0.6997,0.3003,0.3156,0.5683,中等,堆叠法
XGBoost + LightGBM + Logistic + SVM,4,0.8544,0.9196,0.0804,0.1167,0.1167,0.8833,0.7686,0.2314,0.2821,0.5682,较差,建议重新选择模型
RandomForest + CatBoost + SVM + NeuralNet,4,0.8714,0.9349,0.0651,0.0958,0.1083,0.8917,0.8132,0.1868,0.264,0.5677,较差,建议重新选择模型
DecisionTree + CatBoost + Logistic + NaiveBayes,4,0.8273,0.9015,0.0985,0.15,0.125,0.875,0.7089,0.2911,0.3078,0.5675,中等,堆叠法
RandomForest + XGBoost + LightGBM + SVM,4,0.8712,0.9371,0.0629,0.0958,0.1083,0.8917,0.8116,0.1884,0.2636,0.5674,较差,建议重新选择模型
DecisionTree + LightGBM + CatBoost + SVM,4,0.8466,0.931,0.069,0.1292,0.1167,0.8833,0.7423,0.2577,0.2877,0.5672,较差,建议重新选择模型
RandomForest + XGBoost + Logistic + NaiveBayes,4,0.8518,0.9205,0.0795,0.1167,0.1167,0.8833,0.7701,0.2299,0.2815,0.5667,较差,建议重新选择模型
DecisionTree + RandomForest + CatBoost + NeuralNet,4,0.8461,0.9311,0.0689,0.1292,0.1167,0.8833,0.7461,0.2539,0.2869,0.5665,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + NeuralNet,4,0.8397,0.9192,0.0808,0.1333,0.1208,0.8792,0.7373,0.2627,0.2926,0.5662,较差,建议重新选择模型
DecisionTree + XGBoost + LightGBM + SVM,4,0.8403,0.9231,0.0769,0.1333,0.1208,0.8792,0.7351,0.2649,0.2919,0.5661,较差,建议重新选择模型
CatBoost + Logistic + SVM + NaiveBayes,4,0.8526,0.9225,0.0775,0.1167,0.1167,0.8833,0.7763,0.2237,0.2797,0.5661,较差,建议重新选择模型
XGBoost + LightGBM + CatBoost + SVM,4,0.8727,0.935,0.065,0.0875,0.1125,0.8875,0.8265,0.1735,0.258,0.5653,较差,建议重新选择模型
RandomForest + LightGBM + CatBoost + SVM,4,0.8776,0.9471,0.0529,0.0833,0.1083,0.8917,0.8368,0.1632,0.2518,0.5647,较差,建议重新选择模型
LightGBM + CatBoost + Logistic + SVM,4,0.8607,0.9362,0.0638,0.1042,0.1167,0.8833,0.7933,0.2067,0.2684,0.5646,较差,建议重新选择模型
DecisionTree + RandomForest + SVM + NeuralNet,4,0.8391,0.9271,0.0729,0.1333,0.1208,0.8792,0.7391,0.2609,0.2899,0.5645,较差,建议重新选择模型
DecisionTree + XGBoost + CatBoost + NeuralNet,4,0.8412,0.9177,0.0823,0.125,0.125,0.875,0.7532,0.2468,0.2866,0.5639,较差,建议重新选择模型
RandomForest + SVM + NaiveBayes + NeuralNet,4,0.8676,0.9461,0.0539,0.0958,0.1083,0.8917,0.8172,0.1828,0.2598,0.5637,较差,建议重新选择模型
RandomForest + XGBoost + Logistic + NeuralNet,4,0.8538,0.9291,0.0709,0.1083,0.1208,0.8792,0.7839,0.2161,0.2728,0.5633,较差,建议重新选择模型
XGBoost + Logistic + SVM + NeuralNet,4,0.8483,0.916,0.084,0.1125,0.125,0.875,0.7784,0.2216,0.2783,0.5633,较差,建议重新选择模型
DecisionTree + LightGBM + Logistic + SVM,4,0.8284,0.9195,0.0805,0.1417,0.1292,0.8708,0.7185,0.2815,0.2971,0.5628,较差,建议重新选择模型
DecisionTree + Logistic + SVM + NaiveBayes,4,0.8203,0.897,0.103,0.1458,0.1333,0.8667,0.7191,0.2809,0.3042,0.5622,中等,堆叠法
RandomForest + CatBoost + Logistic + NaiveBayes,4,0.8582,0.9412,0.0588,0.1042,0.1167,0.8833,0.7974,0.2026,0.2661,0.5621,较差,建议重新选择模型
DecisionTree + RandomForest + Logistic + NaiveBayes,4,0.8258,0.9158,0.0842,0.1417,0.1292,0.8708,0.7249,0.2751,0.297,0.5614,较差,建议重新选择模型
DecisionTree + Logistic + NaiveBayes + NeuralNet,4,0.8254,0.9158,0.0842,0.1417,0.1292,0.8708,0.7249,0.2751,0.297,0.5612,较差,建议重新选择模型
DecisionTree + RandomForest + CatBoost + SVM,4,0.8409,0.9376,0.0624,0.125,0.125,0.875,0.7533,0.2467,0.2806,0.5607,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + SVM,4,0.8346,0.9276,0.0724,0.1292,0.1292,0.8708,0.7457,0.2543,0.2855,0.56,较差,建议重新选择模型
RandomForest + CatBoost + Logistic + NeuralNet,4,0.8602,0.9498,0.0502,0.0958,0.1208,0.8792,0.8103,0.1897,0.2576,0.5589,较差,建议重新选择模型
DecisionTree + XGBoost + Logistic + NeuralNet,4,0.8229,0.9161,0.0839,0.1375,0.1375,0.8625,0.7281,0.2719,0.2933,0.5581,较差,建议重新选择模型
RandomForest + LightGBM + Logistic + SVM,4,0.8593,0.9512,0.0488,0.0958,0.1208,0.8792,0.8118,0.1882,0.2569,0.5581,较差,建议重新选择模型
XGBoost + CatBoost + Logistic + SVM,4,0.8501,0.9353,0.0647,0.1042,0.1292,0.8708,0.7933,0.2067,0.2662,0.5581,较差,建议重新选择模型
CatBoost + Logistic + SVM + NeuralNet,4,0.8546,0.9424,0.0576,0.1,0.125,0.875,0.8035,0.1965,0.2616,0.5581,较差,建议重新选择模型
RandomForest + XGBoost + CatBoost + SVM,4,0.8669,0.9511,0.0489,0.0833,0.1208,0.8792,0.8368,0.1632,0.2481,0.5575,较差,建议重新选择模型
DecisionTree + CatBoost + Logistic + NeuralNet,4,0.8293,0.9347,0.0653,0.1333,0.1333,0.8667,0.7364,0.2636,0.2856,0.5574,较差,建议重新选择模型
RandomForest + XGBoost + LightGBM + Logistic,4,0.86,0.9601,0.0399,0.0958,0.1208,0.8792,0.8089,0.1911,0.2548,0.5574,较差,建议重新选择模型
DecisionTree + XGBoost + CatBoost + SVM,4,0.836,0.9341,0.0659,0.1208,0.1333,0.8667,0.759,0.241,0.2775,0.5568,较差,建议重新选择模型
DecisionTree + RandomForest + LightGBM + Logistic,4,0.8339,0.9545,0.0455,0.1292,0.1292,0.8708,0.7447,0.2553,0.2776,0.5558,较差,建议重新选择模型
RandomForest + Logistic + NaiveBayes + NeuralNet,4,0.8563,0.961,0.039,0.0958,0.1208,0.8792,0.8109,0.1891,0.2541,0.5552,较差,建议重新选择模型
XGBoost + LightGBM + CatBoost + Logistic,4,0.8614,0.9606,0.0394,0.0875,0.125,0.875,0.8256,0.1744,0.248,0.5547,较差,建议重新选择模型
RandomForest + LightGBM + CatBoost + Logistic,4,0.8663,0.971,0.029,0.0833,0.1208,0.8792,0.8348,0.1652,0.2426,0.5544,较差,建议重新选择模型
DecisionTree + XGBoost + Logistic + SVM,4,0.8178,0.9186,0.0814,0.1333,0.1458,0.8542,0.7353,0.2647,0.2882,0.553,较差,建议重新选择模型
DecisionTree + CatBoost + Logistic + SVM,4,0.8241,0.9352,0.0648,0.1292,0.1417,0.8583,0.7424,0.2576,0.2814,0.5527,较差,建议重新选择模型
DecisionTree + LightGBM + CatBoost + Logistic,4,0.8354,0.9645,0.0355,0.1208,0.1333,0.8667,0.7598,0.2402,0.2683,0.5518,较差,建议重新选择模型
RandomForest + XGBoost + Logistic + SVM,4,0.8487,0.9511,0.0489,0.0958,0.1333,0.8667,0.8118,0.1882,0.2544,0.5515,较差,建议重新选择模型
DecisionTree + RandomForest + LightGBM + CatBoost,4,0.8522,0.9793,0.0207,0.1,0.125,0.875,0.8032,0.1968,0.2506,0.5514,较差,建议重新选择模型
DecisionTree + RandomForest + Logistic + NeuralNet,4,0.8278,0.949,0.051,0.125,0.1375,0.8625,0.7539,0.2461,0.2745,0.5512,较差,建议重新选择模型
DecisionTree + XGBoost + LightGBM + Logistic,4,0.829,0.9557,0.0443,0.125,0.1375,0.8625,0.7518,0.2482,0.2729,0.551,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + LightGBM,4,0.8459,0.9772,0.0228,0.1042,0.1292,0.8708,0.7948,0.2052,0.2533,0.5496,较差,建议重新选择模型
RandomForest + Logistic + SVM + NaiveBayes,4,0.8512,0.9617,0.0383,0.0917,0.1292,0.8708,0.8258,0.1742,0.248,0.5496,较差,建议重新选择模型
Logistic + SVM + NaiveBayes + NeuralNet,4,0.8508,0.9617,0.0383,0.0917,0.1292,0.8708,0.8258,0.1742,0.248,0.5494,较差,建议重新选择模型
RandomForest + XGBoost + CatBoost + Logistic,4,0.8557,0.9678,0.0322,0.0833,0.1333,0.8667,0.8348,0.1652,0.241,0.5483,较差,建议重新选择模型
RandomForest + CatBoost + Logistic + SVM,4,0.855,0.9698,0.0302,0.0833,0.1333,0.8667,0.8369,0.1631,0.24,0.5475,较差,建议重新选择模型
DecisionTree + RandomForest + Logistic + SVM,4,0.8227,0.9414,0.0586,0.1208,0.1458,0.8542,0.7624,0.2376,0.2722,0.5474,较差,建议重新选择模型
DecisionTree + Logistic + SVM + NeuralNet,4,0.8223,0.9414,0.0586,0.1208,0.1458,0.8542,0.7624,0.2376,0.2722,0.5472,较差,建议重新选择模型
DecisionTree + RandomForest + CatBoost + Logistic,4,0.8296,0.9621,0.0379,0.1167,0.1417,0.8583,0.7699,0.2301,0.2641,0.5469,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + Logistic,4,0.8233,0.9512,0.0488,0.1208,0.1458,0.8542,0.7615,0.2385,0.2694,0.5464,较差,建议重新选择模型
RandomForest + XGBoost + LightGBM + CatBoost,4,0.8782,0.9889,0.0111,0.05,0.125,0.875,0.9015,0.0985,0.213,0.5456,较差,建议重新选择模型
DecisionTree + XGBoost + CatBoost + Logistic,4,0.8247,0.9604,0.0396,0.1125,0.15,0.85,0.7766,0.2234,0.2603,0.5425,较差,建议重新选择模型
RandomForest + Logistic + SVM + NeuralNet,4,0.8532,0.9817,0.0183,0.075,0.1375,0.8625,0.8545,0.1455,0.2296,0.5414,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + CatBoost,4,0.8416,0.9802,0.0198,0.0917,0.1417,0.8583,0.82,0.18,0.2411,0.5413,较差,建议重新选择模型
DecisionTree + XGBoost + LightGBM + CatBoost,4,0.8473,1.0,0.0,0.0792,0.1417,0.8583,0.8433,0.1567,0.2268,0.537,较差,建议重新选择模型
