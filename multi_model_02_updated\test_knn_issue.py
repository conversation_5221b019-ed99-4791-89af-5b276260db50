#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试KNN模型训练是否会卡住
"""

import sys
import numpy as np
from pathlib import Path

# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))

def test_knn_pipeline():
    """测试KNN的Pipeline训练"""
    print("=== 测试KNN Pipeline训练 ===")
    
    try:
        from model_training import MODEL_TRAINERS
        from sklearn.datasets import make_classification
        from sklearn.model_selection import train_test_split
        
        # 创建测试数据
        X, y = make_classification(n_samples=200, n_features=10, n_classes=2, random_state=42)
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        print(f"训练数据形状: {X_train.shape}")
        print(f"测试数据形状: {X_test.shape}")
        
        # 获取KNN训练器
        trainer = MODEL_TRAINERS['KNN']
        print(f"KNN默认参数: {trainer.default_params}")
        
        # 测试直接创建模型
        print("\n测试直接创建KNN模型...")
        from sklearn.neighbors import KNeighborsClassifier
        from sklearn.pipeline import Pipeline
        from sklearn.preprocessing import StandardScaler
        
        # 使用默认参数
        model = Pipeline([
            ('scaler', StandardScaler()),
            ('classifier', KNeighborsClassifier(
                n_neighbors=5,
                weights='uniform',
                algorithm='auto'
            ))
        ])
        
        print("开始训练...")
        import time
        start_time = time.time()
        model.fit(X_train, y_train)
        end_time = time.time()
        
        print(f"✅ 训练成功! 耗时: {end_time - start_time:.2f} 秒")
        
        # 测试预测
        y_pred = model.predict(X_test)
        print(f"预测结果形状: {y_pred.shape}")
        print(f"预测准确率: {np.mean(y_pred == y_test):.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_models():
    """测试所有模型训练"""
    print("\n=== 测试所有模型训练 ===")
    
    try:
        from model_training import MODEL_TRAINERS
        from sklearn.datasets import make_classification
        from sklearn.model_selection import train_test_split
        
        # 创建测试数据
        X, y = make_classification(n_samples=200, n_features=10, n_classes=2, random_state=42)
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        success_count = 0
        total_count = len(MODEL_TRAINERS)
        
        for model_name, trainer in MODEL_TRAINERS.items():
            print(f"\n测试 {model_name}...")
            try:
                import time
                start_time = time.time()
                
                # 训练模型
                model = trainer.train_and_evaluate(X_train, y_train, X_test, y_test)
                
                end_time = time.time()
                print(f"✅ {model_name} 训练成功! 耗时: {end_time - start_time:.2f} 秒")
                success_count += 1
                
            except Exception as e:
                print(f"❌ {model_name} 训练失败: {e}")
                # 继续测试下一个模型
                continue
        
        print(f"\n=== 测试结果 ===")
        print(f"成功: {success_count}/{total_count}")
        
        if success_count == total_count:
            print("🎉 所有模型都能正常训练!")
            return True
        else:
            print("⚠️ 部分模型训练失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试KNN训练问题...\n")
    
    # 先测试KNN
    knn_ok = test_knn_pipeline()
    
    # 如果KNN正常，测试所有模型
    if knn_ok:
        test_all_models()