#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
KNN性能分析工具
分析KNN模型训练慢的根本原因
"""

import time
import numpy as np
import pandas as pd
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import cross_val_score
from sklearn.pipeline import Pipeline
from sklearn.datasets import make_classification
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_knn_performance():
    """分析KNN性能瓶颈"""
    
    # 生成测试数据集
    print("生成测试数据集...")
    X, y = make_classification(
        n_samples=1000, 
        n_features=20, 
        n_informative=15, 
        n_redundant=5,
        random_state=42
    )
    
    print(f"数据集大小: {X.shape}")
    print(f"类别分布: {np.bincount(y)}")
    
    # 测试不同因素对KNN性能的影响
    factors = {
        'algorithm': ['auto', 'ball_tree', 'kd_tree', 'brute'],
        'weights': ['uniform', 'distance'],
        'n_neighbors': [3, 5, 10, 15],
        'scaler': [None, StandardScaler(), MinMaxScaler()]
    }
    
    results = []
    
    # 基准测试
    print("\n=== 基准测试 ===")
    baseline_knn = KNeighborsClassifier()
    start_time = time.time()
    baseline_knn.fit(X, y)
    baseline_time = time.time() - start_time
    print(f"基准训练时间: {baseline_time:.4f}秒")
    
    # 1. 算法选择影响
    print("\n=== 1. 算法选择影响 ===")
    for algo in factors['algorithm']:
        knn = KNeighborsClassifier(algorithm=algo)
        start_time = time.time()
        knn.fit(X, y)
        fit_time = time.time() - start_time
        
        # 测试预测时间
        start_time = time.time()
        knn.predict(X[:100])
        pred_time = time.time() - start_time
        
        results.append({
            'factor': 'algorithm',
            'value': algo,
            'fit_time': fit_time,
            'pred_time': pred_time,
            'total_time': fit_time + pred_time
        })
        print(f"算法 {algo}: 训练 {fit_time:.4f}秒, 预测 {pred_time:.4f}秒")
    
    # 2. 权重方式影响
    print("\n=== 2. 权重方式影响 ===")
    for weight in factors['weights']:
        knn = KNeighborsClassifier(weights=weight)
        start_time = time.time()
        knn.fit(X, y)
        fit_time = time.time() - start_time
        
        start_time = time.time()
        knn.predict(X[:100])
        pred_time = time.time() - start_time
        
        results.append({
            'factor': 'weights',
            'value': weight,
            'fit_time': fit_time,
            'pred_time': pred_time,
            'total_time': fit_time + pred_time
        })
        print(f"权重 {weight}: 训练 {fit_time:.4f}秒, 预测 {pred_time:.4f}秒")
    
    # 3. 邻居数影响
    print("\n=== 3. 邻居数影响 ===")
    for n in factors['n_neighbors']:
        knn = KNeighborsClassifier(n_neighbors=n)
        start_time = time.time()
        knn.fit(X, y)
        fit_time = time.time() - start_time
        
        start_time = time.time()
        knn.predict(X[:100])
        pred_time = time.time() - start_time
        
        results.append({
            'factor': 'n_neighbors',
            'value': n,
            'fit_time': fit_time,
            'pred_time': pred_time,
            'total_time': fit_time + pred_time
        })
        print(f"邻居数 {n}: 训练 {fit_time:.4f}秒, 预测 {pred_time:.4f}秒")
    
    # 4. 数据标准化影响
    print("\n=== 4. 数据标准化影响 ===")
    scaler_names = ['None', 'StandardScaler', 'MinMaxScaler']
    for i, scaler in enumerate(factors['scaler']):
        if scaler is None:
            knn = KNeighborsClassifier()
            X_scaled = X
        else:
            pipe = Pipeline([
                ('scaler', scaler),
                ('knn', KNeighborsClassifier())
            ])
            X_scaled = X
            knn = pipe
        
        start_time = time.time()
        knn.fit(X_scaled, y)
        fit_time = time.time() - start_time
        
        start_time = time.time()
        knn.predict(X_scaled[:100])
        pred_time = time.time() - start_time
        
        results.append({
            'factor': 'scaler',
            'value': scaler_names[i],
            'fit_time': fit_time,
            'pred_time': pred_time,
            'total_time': fit_time + pred_time
        })
        print(f"标准化 {scaler_names[i]}: 训练 {fit_time:.4f}秒, 预测 {pred_time:.4f}秒")
    
    # 5. 数据集大小影响
    print("\n=== 5. 数据集大小影响 ===")
    sizes = [100, 500, 1000, 2000]
    for size in sizes:
        X_sub, y_sub = X[:size], y[:size]
        knn = KNeighborsClassifier()
        start_time = time.time()
        knn.fit(X_sub, y_sub)
        fit_time = time.time() - start_time
        
        start_time = time.time()
        knn.predict(X_sub[:min(100, size)])
        pred_time = time.time() - start_time
        
        results.append({
            'factor': 'dataset_size',
            'value': size,
            'fit_time': fit_time,
            'pred_time': pred_time,
            'total_time': fit_time + pred_time
        })
        print(f"数据集大小 {size}: 训练 {fit_time:.4f}秒, 预测 {pred_time:.4f}秒")
    
    # 6. 交叉验证影响
    print("\n=== 6. 交叉验证影响 ===")
    knn = KNeighborsClassifier()
    pipe = Pipeline([
        ('scaler', StandardScaler()),
        ('knn', knn)
    ])
    
    start_time = time.time()
    scores = cross_val_score(pipe, X, y, cv=5, scoring='accuracy')
    cv_time = time.time() - start_time
    
    results.append({
        'factor': 'cross_validation',
        'value': '5-fold',
        'fit_time': cv_time,
        'pred_time': 0,
        'total_time': cv_time
    })
    print(f"5折交叉验证: 总时间 {cv_time:.4f}秒, 平均分数 {scores.mean():.4f}")
    
    # 分析结果
    print("\n=== 性能分析结果 ===")
    df_results = pd.DataFrame(results)
    
    # 找出最耗时的因素
    print("\n各因素的最大耗时:")
    for factor in df_results['factor'].unique():
        factor_data = df_results[df_results['factor'] == factor]
        max_row = factor_data.loc[factor_data['total_time'].idxmax()]
        print(f"{factor}: {max_row['value']} (耗时 {max_row['total_time']:.4f}秒)")
    
    # 保存结果
    df_results.to_csv('knn_performance_analysis.csv', index=False)
    print(f"\n详细结果已保存到 knn_performance_analysis.csv")
    
    return df_results

def find_bottlenecks():
    """找出KNN的性能瓶颈"""
    print("\n=== KNN性能瓶颈分析 ===")
    
    # 生成较大的数据集来模拟真实场景
    X, y = make_classification(
        n_samples=5000, 
        n_features=50, 
        n_informative=30, 
        n_redundant=20,
        random_state=42
    )
    
    print(f"大数据集大小: {X.shape}")
    
    # 测试Pipeline各部分耗时
    print("\nPipeline各部分耗时分析:")
    
    # 1. 仅标准化
    scaler = StandardScaler()
    start_time = time.time()
    X_scaled = scaler.fit_transform(X)
    scale_time = time.time() - start_time
    print(f"标准化耗时: {scale_time:.4f}秒")
    
    # 2. 仅KNN训练（已标准化数据）
    knn = KNeighborsClassifier()
    start_time = time.time()
    knn.fit(X_scaled, y)
    knn_fit_time = time.time() - start_time
    print(f"KNN训练耗时（标准化后）: {knn_fit_time:.4f}秒")
    
    # 3. 完整Pipeline
    pipe = Pipeline([
        ('scaler', StandardScaler()),
        ('knn', KNeighborsClassifier())
    ])
    start_time = time.time()
    pipe.fit(X, y)
    pipe_time = time.time() - start_time
    print(f"完整Pipeline耗时: {pipe_time:.4f}秒")
    
    # 4. 预测耗时
    start_time = time.time()
    pipe.predict(X[:1000])
    pred_time = time.time() - start_time
    print(f"预测1000个样本耗时: {pred_time:.4f}秒")
    
    # 5. 距离计算耗时（估算）
    print("\n距离计算分析:")
    from sklearn.metrics import pairwise_distances
    start_time = time.time()
    distances = pairwise_distances(X[:100], X[:100], metric='minkowski', p=2)
    dist_time = time.time() - start_time
    print(f"100x100距离矩阵计算耗时: {dist_time:.4f}秒")
    
    # 估算全量距离计算时间
    estimated_full_dist = dist_time * (5000/100) * (5000/100)
    print(f"估算5000x5000距离矩阵耗时: {estimated_full_dist:.4f}秒")
    
    # 6. 内存使用估算
    print("\n内存使用估算:")
    print(f"数据集内存占用: {X.nbytes / 1024 / 1024:.2f} MB")
    print(f"距离矩阵内存占用: {5000*5000*8 / 1024 / 1024:.2f} MB")
    
    return {
        'scale_time': scale_time,
        'knn_fit_time': knn_fit_time,
        'pipe_time': pipe_time,
        'pred_time': pred_time,
        'dist_time': dist_time,
        'estimated_full_dist': estimated_full_dist
    }

if __name__ == "__main__":
    print("开始KNN性能分析...")
    
    # 运行性能分析
    performance_results = analyze_knn_performance()
    
    # 找出瓶颈
    bottleneck_results = find_bottlenecks()
    
    print("\n=== 总结 ===")
    print("KNN训练慢的主要原因:")
    print("1. 距离计算: O(n²)复杂度，数据量大时急剧增加")
    print("2. 'distance'权重: 需要计算所有距离，比'uniform'慢")
    print("3. 算法选择: 'brute'在低维数据上可能更快")
    print("4. 标准化: 额外的计算开销")
    print("5. 交叉验证: 5倍训练时间")
    
    print("\n优化建议:")
    print("1. 使用'brute'算法（特别是低维数据）")
    print("2. 使用'uniform'权重")
    print("3. 减少n_neighbors（如3-5）")
    print("4. 考虑使用MinMaxScaler代替StandardScaler")
    print("5. 大数据集时考虑采样或特征选择")