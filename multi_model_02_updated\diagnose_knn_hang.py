#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：诊断KNN训练挂起问题
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path
import time
import threading
import traceback

# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))

def test_with_timeout():
    """带超时的测试"""
    print("=== 带超时的KNN训练测试 ===")
    
    result = {'success': False, 'error': None, 'traceback': None}
    
    def run_test():
        try:
            from model_training import MODEL_TRAINERS
            from sklearn.datasets import make_classification
            from sklearn.model_selection import train_test_split
            
            print("创建测试数据...")
            X, y = make_classification(n_samples=200, n_features=10, n_classes=2, random_state=42)
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            print(f"数据形状: X_train={X_train.shape}, y_train={y_train.shape}")
            
            # 测试KNN
            print("\n开始KNN训练...")
            trainer = MODEL_TRAINERS['KNN']
            
            print(f"KNN参数: {trainer.default_params}")
            
            # 记录开始时间
            start_time = time.time()
            
            # 训练模型
            model = trainer.train_and_evaluate(X_train, y_train, X_test, y_test)
            
            end_time = time.time()
            
            print(f"✅ KNN训练成功! 耗时: {end_time - start_time:.2f} 秒")
            result['success'] = True
            
        except Exception as e:
            result['error'] = str(e)
            result['traceback'] = traceback.format_exc()
            print(f"❌ KNN训练失败: {e}")
    
    # 创建线程
    test_thread = threading.Thread(target=run_test)
    test_thread.daemon = True
    
    # 启动线程
    print("启动测试线程...")
    test_thread.start()
    
    # 等待线程完成或超时
    timeout = 30  # 30秒超时
    test_thread.join(timeout=timeout)
    
    if test_thread.is_alive():
        print(f"❌ 测试超时! (超过 {timeout} 秒)")
        return False
    elif result['success']:
        return True
    else:
        print(f"❌ 测试失败: {result['error']}")
        if result['traceback']:
            print("\n详细错误信息:")
            print(result['traceback'])
        return False

def test_knn_step_by_step():
    """逐步测试KNN的各个步骤"""
    print("\n=== 逐步测试KNN训练步骤 ===")
    
    try:
        from sklearn.neighbors import KNeighborsClassifier
        from sklearn.pipeline import Pipeline
        from sklearn.preprocessing import StandardScaler
        from sklearn.datasets import make_classification
        from sklearn.model_selection import train_test_split
        
        # 1. 创建数据
        print("1. 创建测试数据...")
        X, y = make_classification(n_samples=200, n_features=10, n_classes=2, random_state=42)
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        print(f"   数据创建成功: X_train={X_train.shape}")
        
        # 2. 创建scaler
        print("\n2. 创建StandardScaler...")
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        print("   StandardScaler创建成功")
        
        # 3. 测试原始KNN（不使用Pipeline）
        print("\n3. 测试原始KNN...")
        knn = KNeighborsClassifier(n_neighbors=5, weights='uniform', algorithm='auto')
        print("   开始训练原始KNN...")
        start_time = time.time()
        knn.fit(X_train_scaled, y_train)
        end_time = time.time()
        print(f"   ✅ 原始KNN训练成功! 耗时: {end_time - start_time:.3f} 秒")
        
        # 4. 测试Pipeline
        print("\n4. 测试Pipeline...")
        pipeline = Pipeline([
            ('scaler', StandardScaler()),
            ('knn', KNeighborsClassifier(n_neighbors=5, weights='uniform', algorithm='auto'))
        ])
        print("   开始训练Pipeline...")
        start_time = time.time()
        pipeline.fit(X_train, y_train)
        end_time = time.time()
        print(f"   ✅ Pipeline训练成功! 耗时: {end_time - start_time:.3f} 秒")
        
        # 5. 测试预测
        print("\n5. 测试预测...")
        y_pred = pipeline.predict(X_test)
        accuracy = np.mean(y_pred == y_test)
        print(f"   ✅ 预测成功! 准确率: {accuracy:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 逐步测试失败: {e}")
        traceback.print_exc()
        return False

def check_data_preprocessing():
    """检查数据预处理是否有问题"""
    print("\n=== 检查数据预处理 ===")
    
    try:
        from data_preprocessing import load_and_preprocess_data
        
        # 使用GUI中相同的数据文件
        data_file = str(Path(__file__).parent / "sample_data" / "sample_classification.csv")
        
        if not Path(data_file).exists():
            print(f"⚠️ 测试数据文件不存在: {data_file}")
            return True
        
        print(f"加载数据文件: {data_file}")
        X_train, X_test, y_train, y_test = load_and_preprocess_data(data_file)
        
        print(f"数据加载成功:")
        print(f"  X_train: {X_train.shape}")
        print(f"  X_test: {X_test.shape}")
        print(f"  y_train: {y_train.shape}")
        print(f"  y_test: {y_test.shape}")
        
        # 检查数据是否有异常值
        print(f"\n数据统计:")
        print(f"  X_train min: {np.min(X_train):.4f}, max: {np.max(X_train):.4f}")
        print(f"  X_test min: {np.min(X_test):.4f}, max: {np.max(X_test):.4f}")
        print(f"  y_train 唯一值: {np.unique(y_train)}")
        print(f"  y_test 唯一值: {np.unique(y_test)}")
        
        # 检查是否有NaN或Inf
        print(f"\n数据质量检查:")
        print(f"  X_train 包含NaN: {np.any(np.isnan(X_train))}")
        print(f"  X_test 包含NaN: {np.any(np.isnan(X_test))}")
        print(f"  X_train 包含Inf: {np.any(np.isinf(X_train))}")
        print(f"  X_test 包含Inf: {np.any(np.isinf(X_test))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据预处理检查失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始诊断KNN训练挂起问题...\n")
    
    # 逐步测试
    step_ok = test_knn_step_by_step()
    
    # 带超时的测试
    timeout_ok = test_with_timeout()
    
    # 检查数据预处理
    data_ok = check_data_preprocessing()
    
    print("\n" + "="*50)
    print("诊断结果:")
    print(f"  逐步测试: {'✅ 通过' if step_ok else '❌ 失败'}")
    print(f"  超时测试: {'✅ 通过' if timeout_ok else '❌ 失败'}")
    print(f"  数据检查: {'✅ 通过' if data_ok else '❌ 失败'}")
    
    if all([step_ok, timeout_ok, data_ok]):
        print("\n🎉 所有测试通过! KNN训练应该正常工作。")
        print("如果GUI中仍然挂起，可能是GUI线程相关的问题。")
    else:
        print("\n⚠️ 发现问题，需要进一步排查。")