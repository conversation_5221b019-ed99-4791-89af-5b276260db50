﻿模型组合,组合大小,平均性能得分,平均Q统计量,平均Q统计量多样性,平均不一致性,平均双错度量,平均双错多样性,平均相关系数,平均相关性多样性,综合多样性得分,综合得分,多样性等级,推荐集成方法
XGBoost + LightGBM + SVM + NaiveBayes + NeuralNet,5,0.8699,0.8405,0.1595,0.135,0.0875,0.9125,0.7364,0.2636,0.3236,0.5968,中等,堆叠法
XGBoost + LightGBM + CatBoost + NaiveBayes + NeuralNet,5,0.8755,0.8467,0.1533,0.12,0.09,0.91,0.7642,0.2358,0.3111,0.5933,中等,堆叠法
LightGBM + CatBoost + SVM + NaiveBayes + NeuralNet,5,0.875,0.857,0.143,0.125,0.0875,0.9125,0.7574,0.2426,0.3114,0.5932,中等,堆叠法
RandomForest + XGBoost + LightGBM + NaiveBayes + NeuralNet,5,0.8744,0.8644,0.1356,0.12,0.09,0.91,0.7623,0.2377,0.3062,0.5903,中等,堆叠法
DecisionTree + LightGBM + SVM + NaiveBayes + NeuralNet,5,0.8491,0.854,0.146,0.155,0.0975,0.9025,0.6985,0.3015,0.3311,0.5901,中等,堆叠法
DecisionTree + XGBoost + LightGBM + NaiveBayes + NeuralNet,5,0.8496,0.8508,0.1492,0.15,0.1,0.9,0.706,0.294,0.3285,0.5891,中等,堆叠法
XGBoost + LightGBM + Logistic + NaiveBayes + NeuralNet,5,0.8609,0.8584,0.1416,0.135,0.0975,0.9025,0.7329,0.2671,0.3169,0.5889,中等,堆叠法
DecisionTree + LightGBM + CatBoost + NaiveBayes + NeuralNet,5,0.8547,0.8626,0.1374,0.145,0.0975,0.9025,0.7169,0.2831,0.3218,0.5883,中等,堆叠法
RandomForest + LightGBM + CatBoost + NaiveBayes + NeuralNet,5,0.8795,0.8775,0.1225,0.11,0.09,0.91,0.7841,0.2159,0.2949,0.5872,较差,建议重新选择模型
DecisionTree + RandomForest + LightGBM + NaiveBayes + NeuralNet,5,0.8536,0.8746,0.1254,0.145,0.0975,0.9025,0.716,0.284,0.3184,0.586,中等,堆叠法
LightGBM + CatBoost + Logistic + NaiveBayes + NeuralNet,5,0.866,0.8754,0.1246,0.125,0.0975,0.9025,0.7543,0.2457,0.3045,0.5853,中等,堆叠法
XGBoost + CatBoost + SVM + NaiveBayes + NeuralNet,5,0.8665,0.8767,0.1233,0.125,0.0975,0.9025,0.7574,0.2426,0.3035,0.585,中等,堆叠法
RandomForest + LightGBM + SVM + NaiveBayes + NeuralNet,5,0.8739,0.884,0.116,0.115,0.0925,0.9075,0.7766,0.2234,0.2955,0.5847,较差,建议重新选择模型
DecisionTree + XGBoost + LightGBM + SVM + NaiveBayes,5,0.8455,0.8657,0.1343,0.15,0.105,0.895,0.7077,0.2923,0.3227,0.5841,中等,堆叠法
XGBoost + LightGBM + CatBoost + SVM + NaiveBayes,5,0.8714,0.8781,0.1219,0.115,0.0975,0.9025,0.7768,0.2232,0.2962,0.5838,较差,建议重新选择模型
DecisionTree + LightGBM + CatBoost + SVM + NaiveBayes,5,0.8506,0.8763,0.1237,0.145,0.1025,0.8975,0.718,0.282,0.3165,0.5836,中等,堆叠法
XGBoost + LightGBM + CatBoost + SVM + NeuralNet,5,0.873,0.8849,0.1151,0.11,0.1,0.9,0.7825,0.2175,0.291,0.582,较差,建议重新选择模型
DecisionTree + RandomForest + LightGBM + SVM + NaiveBayes,5,0.8494,0.8835,0.1165,0.145,0.1025,0.8975,0.7186,0.2814,0.3142,0.5818,中等,堆叠法
RandomForest + XGBoost + LightGBM + SVM + NaiveBayes,5,0.8703,0.891,0.109,0.115,0.0975,0.9025,0.7765,0.2235,0.2924,0.5813,较差,建议重新选择模型
XGBoost + LightGBM + Logistic + SVM + NaiveBayes,5,0.8568,0.8814,0.1186,0.13,0.105,0.895,0.7463,0.2537,0.3043,0.5806,中等,堆叠法
DecisionTree + XGBoost + SVM + NaiveBayes + NeuralNet,5,0.8406,0.8737,0.1263,0.15,0.11,0.89,0.7086,0.2914,0.3192,0.5799,中等,堆叠法
DecisionTree + LightGBM + Logistic + NaiveBayes + NeuralNet,5,0.8401,0.8766,0.1234,0.15,0.11,0.89,0.7061,0.2939,0.3188,0.5795,中等,堆叠法
RandomForest + XGBoost + LightGBM + SVM + NeuralNet,5,0.8718,0.8978,0.1022,0.11,0.1,0.9,0.7831,0.2169,0.287,0.5794,较差,建议重新选择模型
RandomForest + XGBoost + CatBoost + NaiveBayes + NeuralNet,5,0.8709,0.8958,0.1042,0.11,0.1,0.9,0.7841,0.2159,0.2874,0.5792,较差,建议重新选择模型
RandomForest + LightGBM + CatBoost + SVM + NaiveBayes,5,0.8753,0.9029,0.0971,0.105,0.0975,0.9025,0.7975,0.2025,0.2816,0.5785,较差,建议重新选择模型
DecisionTree + CatBoost + SVM + NaiveBayes + NeuralNet,5,0.8457,0.8902,0.1098,0.145,0.1075,0.8925,0.719,0.281,0.3111,0.5784,中等,堆叠法
DecisionTree + XGBoost + CatBoost + NaiveBayes + NeuralNet,5,0.8462,0.8804,0.1196,0.14,0.11,0.89,0.727,0.273,0.3105,0.5783,中等,堆叠法
DecisionTree + XGBoost + LightGBM + SVM + NeuralNet,5,0.8471,0.8872,0.1128,0.14,0.11,0.89,0.7232,0.2768,0.3092,0.5782,中等,堆叠法
LightGBM + Logistic + SVM + NaiveBayes + NeuralNet,5,0.8604,0.8883,0.1117,0.12,0.105,0.895,0.7667,0.2333,0.2952,0.5778,较差,建议重新选择模型
XGBoost + CatBoost + Logistic + NaiveBayes + NeuralNet,5,0.8575,0.8907,0.1093,0.125,0.1075,0.8925,0.7543,0.2457,0.2979,0.5777,较差,建议重新选择模型
DecisionTree + LightGBM + CatBoost + SVM + NeuralNet,5,0.8522,0.8978,0.1022,0.135,0.1075,0.8925,0.7328,0.2672,0.3031,0.5776,中等,堆叠法
LightGBM + CatBoost + Logistic + SVM + NaiveBayes,5,0.8619,0.8972,0.1028,0.12,0.105,0.895,0.767,0.233,0.2924,0.5772,较差,建议重新选择模型
RandomForest + LightGBM + Logistic + NaiveBayes + NeuralNet,5,0.8648,0.9008,0.0992,0.115,0.1025,0.8975,0.7725,0.2275,0.2893,0.5771,较差,建议重新选择模型
RandomForest + LightGBM + CatBoost + SVM + NeuralNet,5,0.8769,0.9097,0.0903,0.1,0.1,0.9,0.8036,0.1964,0.2764,0.5767,较差,建议重新选择模型
RandomForest + XGBoost + SVM + NaiveBayes + NeuralNet,5,0.8654,0.9042,0.0958,0.115,0.1025,0.8975,0.7766,0.2234,0.2874,0.5764,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + NaiveBayes + NeuralNet,5,0.8451,0.8929,0.1071,0.14,0.11,0.89,0.7261,0.2739,0.3069,0.576,中等,堆叠法
XGBoost + LightGBM + Logistic + SVM + NeuralNet,5,0.8584,0.8933,0.1067,0.12,0.11,0.89,0.7625,0.2375,0.2935,0.576,较差,建议重新选择模型
DecisionTree + LightGBM + Logistic + SVM + NaiveBayes,5,0.836,0.8819,0.1181,0.15,0.115,0.885,0.7079,0.2921,0.3158,0.5759,中等,堆叠法
DecisionTree + RandomForest + LightGBM + SVM + NeuralNet,5,0.851,0.905,0.095,0.135,0.1075,0.8925,0.7343,0.2657,0.3006,0.5758,中等,堆叠法
XGBoost + LightGBM + CatBoost + Logistic + NaiveBayes,5,0.8624,0.8975,0.1025,0.115,0.1075,0.8925,0.7743,0.2257,0.2889,0.5756,较差,建议重新选择模型
DecisionTree + RandomForest + CatBoost + NaiveBayes + NeuralNet,5,0.8501,0.9059,0.0941,0.135,0.1075,0.8925,0.7372,0.2628,0.2998,0.575,较差,建议重新选择模型
DecisionTree + XGBoost + CatBoost + SVM + NaiveBayes,5,0.8421,0.8871,0.1129,0.14,0.115,0.885,0.728,0.272,0.3073,0.5747,中等,堆叠法
XGBoost + LightGBM + CatBoost + Logistic + NeuralNet,5,0.864,0.9027,0.0973,0.11,0.11,0.89,0.781,0.219,0.284,0.574,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + LightGBM + NaiveBayes,5,0.85,0.9014,0.0986,0.13,0.11,0.89,0.7461,0.2539,0.2974,0.5737,较差,建议重新选择模型
DecisionTree + RandomForest + LightGBM + CatBoost + NaiveBayes,5,0.855,0.9085,0.0915,0.125,0.1075,0.8925,0.757,0.243,0.292,0.5735,较差,建议重新选择模型
RandomForest + XGBoost + LightGBM + Logistic + NaiveBayes,5,0.8612,0.9089,0.0911,0.115,0.1075,0.8925,0.7729,0.2271,0.2858,0.5735,较差,建议重新选择模型
DecisionTree + XGBoost + LightGBM + Logistic + NaiveBayes,5,0.8365,0.8893,0.1107,0.145,0.1175,0.8825,0.7159,0.2841,0.31,0.5733,中等,堆叠法
DecisionTree + RandomForest + XGBoost + SVM + NaiveBayes,5,0.8409,0.8948,0.1052,0.14,0.115,0.885,0.7286,0.2714,0.3048,0.5729,中等,堆叠法
DecisionTree + LightGBM + CatBoost + Logistic + NaiveBayes,5,0.8416,0.9005,0.0995,0.14,0.115,0.885,0.7266,0.2734,0.3035,0.5726,中等,堆叠法
RandomForest + CatBoost + SVM + NaiveBayes + NeuralNet,5,0.8704,0.9219,0.0781,0.105,0.1025,0.8975,0.7978,0.2022,0.2749,0.5726,较差,建议重新选择模型
LightGBM + CatBoost + Logistic + SVM + NeuralNet,5,0.8635,0.9092,0.0908,0.11,0.11,0.89,0.7826,0.2174,0.2817,0.5726,较差,建议重新选择模型
RandomForest + XGBoost + LightGBM + CatBoost + NeuralNet,5,0.8774,0.9137,0.0863,0.09,0.105,0.895,0.8215,0.1785,0.2676,0.5725,较差,建议重新选择模型
DecisionTree + RandomForest + CatBoost + SVM + NaiveBayes,5,0.846,0.9067,0.0933,0.135,0.1125,0.8875,0.7391,0.2609,0.2982,0.5721,较差,建议重新选择模型
RandomForest + XGBoost + LightGBM + Logistic + NeuralNet,5,0.8628,0.9141,0.0859,0.11,0.11,0.89,0.7805,0.2195,0.2807,0.5718,较差,建议重新选择模型
RandomForest + XGBoost + CatBoost + SVM + NaiveBayes,5,0.8668,0.9142,0.0858,0.105,0.1075,0.8925,0.7975,0.2025,0.2762,0.5715,较差,建议重新选择模型
RandomForest + XGBoost + LightGBM + CatBoost + NaiveBayes,5,0.8759,0.9137,0.0863,0.09,0.105,0.895,0.8243,0.1757,0.267,0.5714,较差,建议重新选择模型
DecisionTree + RandomForest + LightGBM + Logistic + NaiveBayes,5,0.8404,0.906,0.094,0.14,0.115,0.885,0.7261,0.2739,0.302,0.5712,中等,堆叠法
XGBoost + CatBoost + Logistic + SVM + NaiveBayes,5,0.8534,0.9056,0.0944,0.12,0.115,0.885,0.767,0.233,0.2879,0.5707,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + LightGBM + NeuralNet,5,0.8516,0.9161,0.0839,0.125,0.1125,0.8875,0.7529,0.2471,0.2896,0.5706,较差,建议重新选择模型
DecisionTree + RandomForest + SVM + NaiveBayes + NeuralNet,5,0.8445,0.9119,0.0881,0.135,0.1125,0.8875,0.7392,0.2608,0.2966,0.5706,较差,建议重新选择模型
DecisionTree + RandomForest + LightGBM + CatBoost + NeuralNet,5,0.8566,0.9232,0.0768,0.12,0.11,0.89,0.7633,0.2367,0.2844,0.5705,较差,建议重新选择模型
RandomForest + LightGBM + CatBoost + Logistic + NaiveBayes,5,0.8663,0.9213,0.0787,0.105,0.1075,0.8925,0.7944,0.2056,0.2747,0.5705,较差,建议重新选择模型
DecisionTree + XGBoost + LightGBM + CatBoost + NaiveBayes,5,0.8511,0.9034,0.0966,0.12,0.115,0.885,0.7665,0.2335,0.2887,0.5699,较差,建议重新选择模型
DecisionTree + XGBoost + Logistic + NaiveBayes + NeuralNet,5,0.8316,0.8919,0.1081,0.145,0.1225,0.8775,0.7161,0.2839,0.3082,0.5699,中等,堆叠法
XGBoost + Logistic + SVM + NaiveBayes + NeuralNet,5,0.8519,0.9056,0.0944,0.12,0.115,0.885,0.7667,0.2333,0.288,0.5699,较差,建议重新选择模型
RandomForest + XGBoost + CatBoost + SVM + NeuralNet,5,0.8684,0.921,0.079,0.1,0.11,0.89,0.8036,0.1964,0.271,0.5697,较差,建议重新选择模型
RandomForest + LightGBM + Logistic + SVM + NaiveBayes,5,0.8607,0.9178,0.0822,0.11,0.11,0.89,0.7867,0.2133,0.2783,0.5695,较差,建议重新选择模型
RandomForest + XGBoost + Logistic + NaiveBayes + NeuralNet,5,0.8563,0.9166,0.0834,0.115,0.1125,0.8875,0.7725,0.2275,0.2825,0.5694,较差,建议重新选择模型
DecisionTree + XGBoost + CatBoost + SVM + NeuralNet,5,0.8437,0.9086,0.0914,0.13,0.12,0.88,0.7429,0.2571,0.2938,0.5688,较差,建议重新选择模型
RandomForest + LightGBM + CatBoost + Logistic + NeuralNet,5,0.8679,0.9265,0.0735,0.1,0.11,0.89,0.8015,0.1985,0.2698,0.5688,较差,建议重新选择模型
DecisionTree + CatBoost + Logistic + NaiveBayes + NeuralNet,5,0.8367,0.909,0.091,0.14,0.12,0.88,0.7271,0.2729,0.2999,0.5683,较差,建议重新选择模型
DecisionTree + XGBoost + LightGBM + Logistic + NeuralNet,5,0.8381,0.9092,0.0908,0.135,0.1225,0.8775,0.7323,0.2677,0.2968,0.5674,较差,建议重新选择模型
DecisionTree + XGBoost + Logistic + SVM + NaiveBayes,5,0.8275,0.8902,0.1098,0.145,0.1275,0.8725,0.718,0.282,0.3073,0.5674,中等,堆叠法
DecisionTree + LightGBM + Logistic + SVM + NeuralNet,5,0.8376,0.9086,0.0914,0.135,0.1225,0.8775,0.7332,0.2668,0.2968,0.5672,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + SVM + NeuralNet,5,0.8425,0.9163,0.0837,0.13,0.12,0.88,0.7444,0.2556,0.2912,0.5669,较差,建议重新选择模型
DecisionTree + XGBoost + LightGBM + CatBoost + NeuralNet,5,0.8527,0.9182,0.0818,0.115,0.1175,0.8825,0.7725,0.2275,0.2811,0.5669,较差,建议重新选择模型
DecisionTree + LightGBM + CatBoost + Logistic + NeuralNet,5,0.8432,0.9204,0.0796,0.13,0.12,0.88,0.7424,0.2576,0.2904,0.5668,较差,建议重新选择模型
DecisionTree + RandomForest + CatBoost + SVM + NeuralNet,5,0.8476,0.9282,0.0718,0.125,0.1175,0.8825,0.7543,0.2457,0.2847,0.5661,较差,建议重新选择模型
DecisionTree + CatBoost + Logistic + SVM + NaiveBayes,5,0.8326,0.9061,0.0939,0.14,0.125,0.875,0.7281,0.2719,0.2995,0.5661,较差,建议重新选择模型
XGBoost + CatBoost + Logistic + SVM + NeuralNet,5,0.855,0.9175,0.0825,0.11,0.12,0.88,0.7826,0.2174,0.2772,0.5661,较差,建议重新选择模型
CatBoost + Logistic + SVM + NaiveBayes + NeuralNet,5,0.857,0.9273,0.0727,0.11,0.115,0.885,0.7876,0.2124,0.2743,0.5656,较差,建议重新选择模型
RandomForest + CatBoost + Logistic + NaiveBayes + NeuralNet,5,0.8614,0.9349,0.0651,0.105,0.1125,0.8875,0.7942,0.2058,0.2697,0.5655,较差,建议重新选择模型
DecisionTree + RandomForest + LightGBM + Logistic + NeuralNet,5,0.842,0.926,0.074,0.13,0.12,0.88,0.7429,0.2571,0.2886,0.5653,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + CatBoost + NaiveBayes,5,0.8465,0.9179,0.0821,0.12,0.12,0.88,0.7671,0.2329,0.2832,0.5649,较差,建议重新选择模型
RandomForest + LightGBM + Logistic + SVM + NeuralNet,5,0.8623,0.9297,0.0703,0.1,0.115,0.885,0.8032,0.1968,0.2674,0.5649,较差,建议重新选择模型
DecisionTree + XGBoost + CatBoost + Logistic + NaiveBayes,5,0.8331,0.9069,0.0931,0.135,0.1275,0.8725,0.7366,0.2634,0.2956,0.5643,较差,建议重新选择模型
RandomForest + XGBoost + CatBoost + Logistic + NaiveBayes,5,0.8578,0.9282,0.0718,0.105,0.1175,0.8825,0.7944,0.2056,0.2706,0.5642,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + Logistic + NaiveBayes,5,0.8319,0.913,0.087,0.135,0.1275,0.8725,0.7362,0.2638,0.2939,0.5629,较差,建议重新选择模型
DecisionTree + RandomForest + LightGBM + CatBoost + SVM,5,0.8525,0.9437,0.0563,0.115,0.1175,0.8825,0.7729,0.2271,0.2733,0.5629,较差,建议重新选择模型
RandomForest + XGBoost + Logistic + SVM + NaiveBayes,5,0.8522,0.9266,0.0734,0.11,0.12,0.88,0.7867,0.2133,0.2737,0.5629,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + LightGBM + SVM,5,0.8474,0.9377,0.0623,0.12,0.12,0.88,0.7632,0.2368,0.278,0.5627,较差,建议重新选择模型
RandomForest + XGBoost + CatBoost + Logistic + NeuralNet,5,0.8594,0.9334,0.0666,0.1,0.12,0.88,0.8015,0.1985,0.2657,0.5625,较差,建议重新选择模型
XGBoost + LightGBM + CatBoost + Logistic + SVM,5,0.8599,0.9373,0.0627,0.1,0.12,0.88,0.8015,0.1985,0.2645,0.5622,较差,建议重新选择模型
DecisionTree + RandomForest + CatBoost + Logistic + NaiveBayes,5,0.837,0.9254,0.0746,0.13,0.125,0.875,0.7471,0.2529,0.287,0.562,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + CatBoost + NeuralNet,5,0.8481,0.9327,0.0673,0.115,0.1225,0.8775,0.7734,0.2266,0.2755,0.5618,较差,建议重新选择模型
DecisionTree + RandomForest + Logistic + NaiveBayes + NeuralNet,5,0.8355,0.9291,0.0709,0.13,0.125,0.875,0.7461,0.2539,0.286,0.5608,较差,建议重新选择模型
RandomForest + XGBoost + LightGBM + Logistic + SVM,5,0.8587,0.9438,0.0562,0.1,0.12,0.88,0.8025,0.1975,0.2623,0.5605,较差,建议重新选择模型
DecisionTree + XGBoost + LightGBM + Logistic + SVM,5,0.834,0.9273,0.0727,0.13,0.13,0.87,0.7419,0.2581,0.2864,0.5602,较差,建议重新选择模型
RandomForest + XGBoost + LightGBM + CatBoost + SVM,5,0.8733,0.9518,0.0482,0.08,0.115,0.885,0.8426,0.1574,0.2469,0.5601,较差,建议重新选择模型
DecisionTree + LightGBM + CatBoost + Logistic + SVM,5,0.8391,0.9373,0.0627,0.125,0.1275,0.8725,0.7513,0.2487,0.2806,0.5598,较差,建议重新选择模型
RandomForest + CatBoost + Logistic + SVM + NaiveBayes,5,0.8573,0.9438,0.0562,0.1,0.12,0.88,0.8077,0.1923,0.2613,0.5593,较差,建议重新选择模型
DecisionTree + RandomForest + Logistic + SVM + NaiveBayes,5,0.8314,0.9214,0.0786,0.13,0.13,0.87,0.7488,0.2512,0.2868,0.5591,较差,建议重新选择模型
DecisionTree + Logistic + SVM + NaiveBayes + NeuralNet,5,0.8311,0.9214,0.0786,0.13,0.13,0.87,0.7488,0.2512,0.2868,0.559,较差,建议重新选择模型
DecisionTree + RandomForest + LightGBM + Logistic + SVM,5,0.8379,0.938,0.062,0.125,0.1275,0.8725,0.7533,0.2467,0.2799,0.5589,较差,建议重新选择模型
DecisionTree + XGBoost + Logistic + SVM + NeuralNet,5,0.8291,0.9169,0.0831,0.13,0.135,0.865,0.7433,0.2567,0.2883,0.5587,较差,建议重新选择模型
DecisionTree + XGBoost + CatBoost + Logistic + NeuralNet,5,0.8347,0.9268,0.0732,0.125,0.1325,0.8675,0.7525,0.2475,0.2825,0.5586,较差,建议重新选择模型
DecisionTree + XGBoost + LightGBM + CatBoost + SVM,5,0.8486,0.9446,0.0554,0.11,0.125,0.875,0.7812,0.2188,0.2684,0.5585,较差,建议重新选择模型
RandomForest + XGBoost + Logistic + SVM + NeuralNet,5,0.8538,0.9386,0.0614,0.1,0.125,0.875,0.8032,0.1968,0.2628,0.5583,较差,建议重新选择模型
RandomForest + LightGBM + CatBoost + Logistic + SVM,5,0.8638,0.9551,0.0449,0.09,0.12,0.88,0.8227,0.1773,0.2519,0.5579,较差,建议重新选择模型
DecisionTree + CatBoost + Logistic + SVM + NeuralNet,5,0.8341,0.9328,0.0672,0.125,0.1325,0.8675,0.7529,0.2471,0.2806,0.5574,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + Logistic + NeuralNet,5,0.8335,0.9329,0.0671,0.125,0.1325,0.8675,0.7529,0.2471,0.2805,0.557,较差,建议重新选择模型
DecisionTree + RandomForest + CatBoost + Logistic + NeuralNet,5,0.8386,0.9453,0.0547,0.12,0.13,0.87,0.7633,0.2367,0.2737,0.5562,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + CatBoost + SVM,5,0.844,0.9461,0.0539,0.11,0.13,0.87,0.7829,0.2171,0.2666,0.5553,较差,建议重新选择模型
RandomForest + CatBoost + Logistic + SVM + NeuralNet,5,0.8589,0.9557,0.0443,0.09,0.125,0.875,0.8237,0.1763,0.2506,0.5547,较差,建议重新选择模型
DecisionTree + XGBoost + CatBoost + Logistic + SVM,5,0.8305,0.9367,0.0633,0.12,0.14,0.86,0.7613,0.2387,0.2747,0.5526,较差,建议重新选择模型
RandomForest + XGBoost + CatBoost + Logistic + SVM,5,0.8553,0.955,0.045,0.09,0.13,0.87,0.8227,0.1773,0.25,0.5526,较差,建议重新选择模型
DecisionTree + RandomForest + LightGBM + CatBoost + Logistic,5,0.8435,0.9663,0.0337,0.11,0.13,0.87,0.7825,0.2175,0.2606,0.5521,较差,建议重新选择模型
RandomForest + XGBoost + LightGBM + CatBoost + Logistic,5,0.8643,0.9697,0.0303,0.08,0.125,0.875,0.8411,0.1589,0.2399,0.5521,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + LightGBM + Logistic,5,0.8384,0.9597,0.0403,0.115,0.1325,0.8675,0.7723,0.2277,0.2656,0.552,较差,建议重新选择模型
RandomForest + Logistic + SVM + NaiveBayes + NeuralNet,5,0.8558,0.9625,0.0375,0.09,0.125,0.875,0.8268,0.1732,0.2479,0.5519,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + Logistic + SVM,5,0.8294,0.938,0.062,0.12,0.14,0.86,0.7633,0.2367,0.2739,0.5517,较差,建议重新选择模型
DecisionTree + RandomForest + CatBoost + Logistic + SVM,5,0.8345,0.9492,0.0508,0.115,0.1375,0.8625,0.773,0.227,0.2676,0.5511,较差,建议重新选择模型
DecisionTree + RandomForest + Logistic + SVM + NeuralNet,5,0.833,0.9481,0.0519,0.115,0.1375,0.8625,0.7745,0.2255,0.2677,0.5503,较差,建议重新选择模型
DecisionTree + XGBoost + LightGBM + CatBoost + Logistic,5,0.8396,0.9682,0.0318,0.105,0.1375,0.8625,0.7914,0.2086,0.2553,0.5474,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + CatBoost + Logistic,5,0.835,0.9643,0.0357,0.105,0.1425,0.8575,0.7925,0.2075,0.2552,0.5451,较差,建议重新选择模型
DecisionTree + RandomForest + XGBoost + LightGBM + CatBoost,5,0.853,0.9851,0.0149,0.085,0.1325,0.8675,0.8326,0.1674,0.237,0.545,较差,建议重新选择模型
