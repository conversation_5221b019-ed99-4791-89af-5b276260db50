﻿模型组合,组合大小,平均性能得分,平均Q统计量,平均Q统计量多样性,平均不一致性,平均双错度量,平均双错多样性,平均相关系数,平均相关性多样性,综合多样性得分,综合得分,多样性等级,推荐集成方法
LightGBM + NeuralNet,2,0.8866,0.6757,0.3243,0.175,0.05,0.95,0.6508,0.3492,0.4096,0.6481,中等,堆叠法
LightGBM + NaiveBayes,2,0.8826,0.6757,0.3243,0.175,0.05,0.95,0.6574,0.3426,0.4083,0.6455,中等,堆叠法
XGBoost + NeuralNet,2,0.8653,0.7647,0.2353,0.175,0.075,0.925,0.6508,0.3492,0.3779,0.6216,中等,堆叠法
XGB<PERSON><PERSON> + <PERSON><PERSON><PERSON><PERSON><PERSON>,2,0.8614,0.7647,0.2353,0.175,0.075,0.925,0.6574,0.3426,0.3766,0.619,中等,堆叠法
CatBoost + NeuralNet,2,0.878,0.8235,0.1765,0.15,0.075,0.925,0.7043,0.2957,0.3421,0.6101,中等,堆叠法
<PERSON>Boost + Naive<PERSON>ayes,2,0.874,0.8235,0.1765,0.15,0.075,0.925,0.7165,0.2835,0.3396,0.6068,中等,堆叠法
Light<PERSON>M + SVM,2,0.8763,0.8416,0.1584,0.15,0.075,0.925,0.7035,0.2965,0.3368,0.6066,中等,堆叠法
DecisionTree + NaiveBayes,2,0.8093,0.7705,0.2295,0.225,0.1,0.9,0.5743,0.4257,0.4015,0.6054,中等,堆叠法
XGBoost + SVM,2,0.855,0.8605,0.1395,0.15,0.1,0.9,0.7035,0.2965,0.3262,0.5906,中等,堆叠法
DecisionTree + SVM,2,0.803,0.8367,0.1633,0.2,0.125,0.875,0.596,0.404,0.3648,0.5839,中等,堆叠法
CatBoost + SVM,2,0.8677,0.9077,0.0923,0.125,0.1,0.9,0.7497,0.2503,0.2953,0.5815,较差,建议重新选择模型
LightGBM + Logistic,2,0.8538,0.92,0.08,0.15,0.1,0.9,0.7,0.3,0.309,0.5814,中等,堆叠法
SVM + NaiveBayes,2,0.8601,0.9077,0.0923,0.125,0.1,0.9,0.7777,0.2223,0.2897,0.5749,较差,建议重新选择模型
XGBoost + Logistic,2,0.8325,0.8954,0.1046,0.15,0.125,0.875,0.7,0.3,0.3114,0.5719,中等,堆叠法
RandomForest + NeuralNet,2,0.8751,0.9394,0.0606,0.1,0.1,0.9,0.7995,0.2005,0.2683,0.5717,较差,建议重新选择模型
RandomForest + NaiveBayes,2,0.8712,0.9394,0.0606,0.1,0.1,0.9,0.8026,0.1974,0.2677,0.5694,较差,建议重新选择模型
NaiveBayes + NeuralNet,2,0.8704,0.9394,0.0606,0.1,0.1,0.9,0.8026,0.1974,0.2677,0.569,较差,建议重新选择模型
DecisionTree + RandomForest,2,0.8141,0.9178,0.0822,0.175,0.125,0.875,0.6591,0.3409,0.3203,0.5672,中等,堆叠法
DecisionTree + NeuralNet,2,0.8133,0.9178,0.0822,0.175,0.125,0.875,0.6591,0.3409,0.3203,0.5668,中等,堆叠法
RandomForest + LightGBM,2,0.8874,0.9701,0.0299,0.075,0.1,0.9,0.8511,0.1489,0.2412,0.5643,较差,建议重新选择模型
CatBoost + Logistic,2,0.8452,0.9481,0.0519,0.125,0.125,0.875,0.7509,0.2491,0.2779,0.5615,较差,建议重新选择模型
Logistic + NaiveBayes,2,0.8375,0.9481,0.0519,0.125,0.125,0.875,0.7586,0.2414,0.2764,0.557,较差,建议重新选择模型
DecisionTree + LightGBM,2,0.8256,1.0,0.0,0.15,0.125,0.875,0.7035,0.2965,0.2793,0.5524,较差,建议重新选择模型
RandomForest + XGBoost,2,0.8661,0.9753,0.0247,0.075,0.125,0.875,0.8511,0.1489,0.2347,0.5504,较差,建议重新选择模型
RandomForest + SVM,2,0.8648,0.9753,0.0247,0.075,0.125,0.875,0.8604,0.1396,0.2328,0.5488,较差,建议重新选择模型
SVM + NeuralNet,2,0.864,0.9753,0.0247,0.075,0.125,0.875,0.8604,0.1396,0.2328,0.5484,较差,建议重新选择模型
RandomForest + CatBoost,2,0.8788,0.988,0.012,0.05,0.125,0.875,0.9048,0.0952,0.2127,0.5457,较差,建议重新选择模型
XGBoost + LightGBM,2,0.8776,1.0,0.0,0.05,0.125,0.875,0.9,0.1,0.21,0.5438,较差,建议重新选择模型
LightGBM + CatBoost,2,0.8903,1.0,0.0,0.025,0.125,0.875,0.9512,0.0488,0.1923,0.5413,较差,建议重新选择模型
DecisionTree + CatBoost,2,0.817,1.0,0.0,0.125,0.15,0.85,0.7497,0.2503,0.2576,0.5373,较差,建议重新选择模型
DecisionTree + Logistic,2,0.7805,0.9188,0.0812,0.15,0.175,0.825,0.7035,0.2965,0.2937,0.5371,较差,建议重新选择模型
RandomForest + Logistic,2,0.8423,1.0,0.0,0.075,0.15,0.85,0.8511,0.1489,0.2223,0.5323,较差,建议重新选择模型
Logistic + NeuralNet,2,0.8415,1.0,0.0,0.075,0.15,0.85,0.8511,0.1489,0.2223,0.5319,较差,建议重新选择模型
XGBoost + CatBoost,2,0.869,1.0,0.0,0.025,0.15,0.85,0.9512,0.0488,0.1873,0.5281,较差,建议重新选择模型
DecisionTree + XGBoost,2,0.8043,1.0,0.0,0.1,0.175,0.825,0.804,0.196,0.2342,0.5193,较差,建议重新选择模型
Logistic + SVM,2,0.8312,1.0,0.0,0.05,0.175,0.825,0.9045,0.0955,0.1991,0.5152,较差,建议重新选择模型
