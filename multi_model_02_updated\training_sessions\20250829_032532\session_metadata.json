{"session_id": "20250829_032532", "session_name": "训练_N-2_20250829_032532", "description": "自动创建的训练会话，基于数据文件: N-2", "created_time": "2025-08-29T03:25:32.574349", "last_modified": "2025-08-29T03:28:55.690372", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_032532.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\DecisionTree_single_032532.joblib", "save_time": "2025-08-29T03:25:32.853189"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_032533.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\RandomForest_single_032533.joblib", "save_time": "2025-08-29T03:25:33.084024"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_032533.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\XGBoost_single_032533.joblib", "save_time": "2025-08-29T03:25:33.316832"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_032533.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\LightGBM_single_032533.joblib", "save_time": "2025-08-29T03:25:33.478899"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_032534.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\CatBoost_single_032534.joblib", "save_time": "2025-08-29T03:25:34.158538"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_032535.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\Logistic_single_032535.joblib", "save_time": "2025-08-29T03:25:35.583184"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_032535.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\SVM_single_032535.joblib", "save_time": "2025-08-29T03:25:35.714393"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_032535.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\KNN_single_032535.joblib", "save_time": "2025-08-29T03:25:35.845312"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_032535.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\NaiveBayes_single_032535.joblib", "save_time": "2025-08-29T03:25:35.972288"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_032536.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\NeuralNet_single_032536.joblib", "save_time": "2025-08-29T03:25:36.221206"}, {"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_032830.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\DecisionTree_single_032830.joblib", "save_time": "2025-08-29T03:28:30.047392"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_032830.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\RandomForest_single_032830.joblib", "save_time": "2025-08-29T03:28:30.855728"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_032831.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\XGBoost_single_032831.joblib", "save_time": "2025-08-29T03:28:31.617358"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_032832.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\LightGBM_single_032832.joblib", "save_time": "2025-08-29T03:28:32.367391"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_032853.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\CatBoost_single_032853.joblib", "save_time": "2025-08-29T03:28:53.751163"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_032854.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\Logistic_single_032854.joblib", "save_time": "2025-08-29T03:28:54.707975"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_032855.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_032532\\models\\SVM_single_032855.joblib", "save_time": "2025-08-29T03:28:55.688373"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}