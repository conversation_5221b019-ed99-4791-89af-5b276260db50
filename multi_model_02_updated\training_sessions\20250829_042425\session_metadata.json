{"session_id": "20250829_042425", "session_name": "训练_N-2_20250829_042425", "description": "自动创建的训练会话，基于数据文件: N-2", "created_time": "2025-08-29T04:24:25.821141", "last_modified": "2025-08-29T04:35:40.167915", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_042425.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\DecisionTree_single_042425.joblib", "save_time": "2025-08-29T04:24:25.961990"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_042426.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\RandomForest_single_042426.joblib", "save_time": "2025-08-29T04:24:26.209965"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_042426.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\XGBoost_single_042426.joblib", "save_time": "2025-08-29T04:24:26.484290"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_042426.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\LightGBM_single_042426.joblib", "save_time": "2025-08-29T04:24:26.650219"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_042427.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\CatBoost_single_042427.joblib", "save_time": "2025-08-29T04:24:27.458329"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_042427.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\Logistic_single_042427.joblib", "save_time": "2025-08-29T04:24:27.589605"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_042427.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\SVM_single_042427.joblib", "save_time": "2025-08-29T04:24:27.720060"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_042427.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\KNN_single_042427.joblib", "save_time": "2025-08-29T04:24:27.865508"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_042427.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NaiveBayes_single_042427.joblib", "save_time": "2025-08-29T04:24:27.999103"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_042428.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NeuralNet_single_042428.joblib", "save_time": "2025-08-29T04:24:28.250820"}, {"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_042505.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\DecisionTree_single_042505.joblib", "save_time": "2025-08-29T04:25:05.435032"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_042506.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\RandomForest_single_042506.joblib", "save_time": "2025-08-29T04:25:06.254184"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_042507.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\XGBoost_single_042507.joblib", "save_time": "2025-08-29T04:25:07.067658"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_042507.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\LightGBM_single_042507.joblib", "save_time": "2025-08-29T04:25:07.832844"}, {"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_042514.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\DecisionTree_single_042514.joblib", "save_time": "2025-08-29T04:25:14.748809"}, {"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_042515.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\DecisionTree_single_042515.joblib", "save_time": "2025-08-29T04:25:15.446144"}, {"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_042515.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\DecisionTree_single_042515.joblib", "save_time": "2025-08-29T04:25:15.724929"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_042515.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\RandomForest_single_042515.joblib", "save_time": "2025-08-29T04:25:15.944615"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_042518.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\RandomForest_single_042518.joblib", "save_time": "2025-08-29T04:25:18.761708"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_042519.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\XGBoost_single_042519.joblib", "save_time": "2025-08-29T04:25:19.775570"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_042519.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\RandomForest_single_042519.joblib", "save_time": "2025-08-29T04:25:19.893752"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_042522.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\XGBoost_single_042522.joblib", "save_time": "2025-08-29T04:25:22.030419"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_042523.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\LightGBM_single_042523.joblib", "save_time": "2025-08-29T04:25:23.366420"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_042523.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\XGBoost_single_042523.joblib", "save_time": "2025-08-29T04:25:23.844349"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_042525.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\LightGBM_single_042525.joblib", "save_time": "2025-08-29T04:25:25.962693"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_042527.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\Logistic_single_042527.joblib", "save_time": "2025-08-29T04:25:27.353651"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_042527.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\LightGBM_single_042527.joblib", "save_time": "2025-08-29T04:25:27.795985"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_042529.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\CatBoost_single_042529.joblib", "save_time": "2025-08-29T04:25:29.038417"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_042531.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\SVM_single_042531.joblib", "save_time": "2025-08-29T04:25:31.617950"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_042532.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\Logistic_single_042532.joblib", "save_time": "2025-08-29T04:25:32.116675"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_042532.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\Logistic_single_042532.joblib", "save_time": "2025-08-29T04:25:32.958879"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_042535.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\SVM_single_042535.joblib", "save_time": "2025-08-29T04:25:35.819229"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_042536.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\SVM_single_042536.joblib", "save_time": "2025-08-29T04:25:36.122635"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_042550.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NaiveBayes_single_042550.joblib", "save_time": "2025-08-29T04:25:50.556481"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_042551.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NeuralNet_single_042551.joblib", "save_time": "2025-08-29T04:25:51.657397"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_042552.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\CatBoost_single_042552.joblib", "save_time": "2025-08-29T04:25:52.876615"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_042553.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NaiveBayes_single_042553.joblib", "save_time": "2025-08-29T04:25:53.377756"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_042553.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NaiveBayes_single_042553.joblib", "save_time": "2025-08-29T04:25:53.568382"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_042556.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\Logistic_single_042556.joblib", "save_time": "2025-08-29T04:25:56.213087"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_042559.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\SVM_single_042559.joblib", "save_time": "2025-08-29T04:25:59.062735"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_042559.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NeuralNet_single_042559.joblib", "save_time": "2025-08-29T04:25:59.481375"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_042559.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NeuralNet_single_042559.joblib", "save_time": "2025-08-29T04:25:59.638679"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_042617.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NaiveBayes_single_042617.joblib", "save_time": "2025-08-29T04:26:17.151805"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_042618.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NeuralNet_single_042618.joblib", "save_time": "2025-08-29T04:26:18.007111"}, {"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_042940.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\DecisionTree_single_042940.joblib", "save_time": "2025-08-29T04:29:40.689307"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_042941.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\RandomForest_single_042941.joblib", "save_time": "2025-08-29T04:29:41.509536"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_042942.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\XGBoost_single_042942.joblib", "save_time": "2025-08-29T04:29:42.364246"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_042943.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\LightGBM_single_042943.joblib", "save_time": "2025-08-29T04:29:43.195214"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_043004.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\CatBoost_single_043004.joblib", "save_time": "2025-08-29T04:30:04.301528"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_043005.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\Logistic_single_043005.joblib", "save_time": "2025-08-29T04:30:05.092849"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_043005.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\SVM_single_043005.joblib", "save_time": "2025-08-29T04:30:05.937569"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_043021.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NaiveBayes_single_043021.joblib", "save_time": "2025-08-29T04:30:21.818040"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_043022.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NeuralNet_single_043022.joblib", "save_time": "2025-08-29T04:30:22.966364"}, {"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_043309.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\DecisionTree_single_043309.joblib", "save_time": "2025-08-29T04:33:09.576796"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_043310.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\RandomForest_single_043310.joblib", "save_time": "2025-08-29T04:33:10.387718"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_043311.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\XGBoost_single_043311.joblib", "save_time": "2025-08-29T04:33:11.244122"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_043312.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\LightGBM_single_043312.joblib", "save_time": "2025-08-29T04:33:12.031697"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_043333.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\CatBoost_single_043333.joblib", "save_time": "2025-08-29T04:33:33.479864"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_043334.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\Logistic_single_043334.joblib", "save_time": "2025-08-29T04:33:34.294729"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_043335.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\SVM_single_043335.joblib", "save_time": "2025-08-29T04:33:35.153412"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_043351.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NaiveBayes_single_043351.joblib", "save_time": "2025-08-29T04:33:51.032372"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_043352.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NeuralNet_single_043352.joblib", "save_time": "2025-08-29T04:33:52.132721"}, {"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_043458.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\DecisionTree_single_043458.joblib", "save_time": "2025-08-29T04:34:58.296789"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_043459.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\RandomForest_single_043459.joblib", "save_time": "2025-08-29T04:34:59.132175"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_043459.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\XGBoost_single_043459.joblib", "save_time": "2025-08-29T04:34:59.946431"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_043500.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\LightGBM_single_043500.joblib", "save_time": "2025-08-29T04:35:00.704485"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_043521.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\CatBoost_single_043521.joblib", "save_time": "2025-08-29T04:35:21.958087"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_043522.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\Logistic_single_043522.joblib", "save_time": "2025-08-29T04:35:22.685393"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_043523.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\SVM_single_043523.joblib", "save_time": "2025-08-29T04:35:23.403118"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_043539.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NaiveBayes_single_043539.joblib", "save_time": "2025-08-29T04:35:39.137776"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_043540.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250829_042425\\models\\NeuralNet_single_043540.joblib", "save_time": "2025-08-29T04:35:40.160612"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}