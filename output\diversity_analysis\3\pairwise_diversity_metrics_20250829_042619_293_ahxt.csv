﻿模型对,Q统计量,Q统计量多样性,不一致性度量,双错度量,双错多样性,相关系数,相关性多样性,综合多样性得分,多样性等级
DecisionTree vs RandomForest,0.9178,0.0822,0.175,0.125,0.875,0.6591,0.3409,0.3203,中等
DecisionTree vs XGBoost,1.0,0.0,0.1,0.175,0.825,0.804,0.196,0.2342,较差
DecisionTree vs LightGBM,1.0,0.0,0.15,0.125,0.875,0.7035,0.2965,0.2793,较差
DecisionTree vs CatBoost,1.0,0.0,0.125,0.15,0.85,0.7497,0.2503,0.2576,较差
DecisionTree vs Logistic,0.9188,0.0812,0.15,0.175,0.825,0.7035,0.2965,0.2937,较差
DecisionTree vs SVM,0.8367,0.1633,0.2,0.125,0.875,0.596,0.404,0.3648,中等
Decision<PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON>,0.7705,0.2295,0.225,0.1,0.9,0.5743,0.4257,0.4015,中等
DecisionTree vs NeuralNet,0.9178,0.0822,0.175,0.125,0.875,0.6591,0.3409,0.3203,中等
RandomForest vs XGBoost,0.9753,0.0247,0.075,0.125,0.875,0.8511,0.1489,0.2347,较差
RandomForest vs LightGBM,0.9701,0.0299,0.075,0.1,0.9,0.8511,0.1489,0.2412,较差
RandomForest vs CatBoost,0.988,0.012,0.05,0.125,0.875,0.9048,0.0952,0.2127,较差
RandomForest vs Logistic,1.0,0.0,0.075,0.15,0.85,0.8511,0.1489,0.2223,较差
RandomForest vs SVM,0.9753,0.0247,0.075,0.125,0.875,0.8604,0.1396,0.2328,较差
RandomForest vs NaiveBayes,0.9394,0.0606,0.1,0.1,0.9,0.8026,0.1974,0.2677,较差
RandomForest vs NeuralNet,0.9394,0.0606,0.1,0.1,0.9,0.7995,0.2005,0.2683,较差
XGBoost vs LightGBM,1.0,0.0,0.05,0.125,0.875,0.9,0.1,0.21,较差
XGBoost vs CatBoost,1.0,0.0,0.025,0.15,0.85,0.9512,0.0488,0.1873,较差
XGBoost vs Logistic,0.8954,0.1046,0.15,0.125,0.875,0.7,0.3,0.3114,中等
XGBoost vs SVM,0.8605,0.1395,0.15,0.1,0.9,0.7035,0.2965,0.3262,中等
XGBoost vs NaiveBayes,0.7647,0.2353,0.175,0.075,0.925,0.6574,0.3426,0.3766,中等
XGBoost vs NeuralNet,0.7647,0.2353,0.175,0.075,0.925,0.6508,0.3492,0.3779,中等
LightGBM vs CatBoost,1.0,0.0,0.025,0.125,0.875,0.9512,0.0488,0.1923,较差
LightGBM vs Logistic,0.92,0.08,0.15,0.1,0.9,0.7,0.3,0.309,中等
LightGBM vs SVM,0.8416,0.1584,0.15,0.075,0.925,0.7035,0.2965,0.3368,中等
LightGBM vs NaiveBayes,0.6757,0.3243,0.175,0.05,0.95,0.6574,0.3426,0.4083,中等
LightGBM vs NeuralNet,0.6757,0.3243,0.175,0.05,0.95,0.6508,0.3492,0.4096,中等
CatBoost vs Logistic,0.9481,0.0519,0.125,0.125,0.875,0.7509,0.2491,0.2779,较差
CatBoost vs SVM,0.9077,0.0923,0.125,0.1,0.9,0.7497,0.2503,0.2953,较差
CatBoost vs NaiveBayes,0.8235,0.1765,0.15,0.075,0.925,0.7165,0.2835,0.3396,中等
CatBoost vs NeuralNet,0.8235,0.1765,0.15,0.075,0.925,0.7043,0.2957,0.3421,中等
Logistic vs SVM,1.0,0.0,0.05,0.175,0.825,0.9045,0.0955,0.1991,较差
Logistic vs NaiveBayes,0.9481,0.0519,0.125,0.125,0.875,0.7586,0.2414,0.2764,较差
Logistic vs NeuralNet,1.0,0.0,0.075,0.15,0.85,0.8511,0.1489,0.2223,较差
SVM vs NaiveBayes,0.9077,0.0923,0.125,0.1,0.9,0.7777,0.2223,0.2897,较差
SVM vs NeuralNet,0.9753,0.0247,0.075,0.125,0.875,0.8604,0.1396,0.2328,较差
NaiveBayes vs NeuralNet,0.9394,0.0606,0.1,0.1,0.9,0.8026,0.1974,0.2677,较差
