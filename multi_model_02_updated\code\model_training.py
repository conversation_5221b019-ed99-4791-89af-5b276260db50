#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import time
import os
import numpy as np
from pathlib import Path
from joblib import dump
from sklearn.metrics import accuracy_score, roc_auc_score, confusion_matrix, classification_report, average_precision_score
from sklearn.model_selection import train_test_split
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from xgboost import XGBClassifier
from lightgbm import LGBMClassifier
from catboost import CatBoostClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier
from config import CACHE_PATH, MODEL_DISPLAY_NAMES, RANDOM_SEED, detect_gpu_availability, OPTIMIZED_GPU_CONFIG
from logger import get_logger

# 尝试导入会话管理功能
try:
    from session_utils import get_current_session, save_to_session
except ImportError:
    # 如果无法导入会话管理，提供空函数
    def get_current_session():
        return None

    def save_to_session(data, data_type, name, **kwargs):
        return None

logger = get_logger(__name__)

# 确保缓存目录存在
CACHE_PATH.mkdir(parents=True, exist_ok=True)


class ModelTrainer:
    """
    统一的模型训练器类，消除重复代码
    """

    def __init__(self, model_name, model_class, default_params=None):
        """
        初始化模型训练器

        Args:
            model_name: 模型名称
            model_class: 模型类
            default_params: 默认参数
        """
        self.model_name = model_name
        self.model_class = model_class
        self.default_params = default_params or {}

    def _apply_gpu_and_parallel_params(self, params, use_gpu=True, n_jobs=-1):
        """
        应用GPU和并行参数

        Args:
            params: 参数字典
            use_gpu: 是否使用GPU
            n_jobs: 并行作业数
        """
        # 获取GPU配置信息
        try:
            gpu_info = detect_gpu_availability()
            use_gpu_enabled = OPTIMIZED_GPU_CONFIG.get('use_gpu', False) and use_gpu
        except:
            gpu_info = {'xgboost_gpu': False, 'lightgbm_gpu': False, 'catboost_gpu': False}
            use_gpu_enabled = False
        
        # 应用GPU配置
        if self.model_name == 'XGBoost':
            if use_gpu_enabled and gpu_info.get('xgboost_gpu', False):
                params['device'] = 'cuda'
                params['tree_method'] = 'hist'
                logger.info("XGBoost使用GPU加速")
            else:
                params['device'] = 'cpu'
                params['tree_method'] = 'hist'
        elif self.model_name == 'LightGBM':
            if use_gpu_enabled and gpu_info.get('lightgbm_gpu', False):
                # 使用GPU加速
                params['device'] = 'gpu'
                params['n_jobs'] = 1
                params['verbose'] = -1
                params['force_row_wise'] = True
                params['deterministic'] = True
                params['num_threads'] = 1
                logger.info("LightGBM使用GPU加速")
            else:
                # 使用CPU模式
                params['device'] = 'cpu'
                params['n_jobs'] = n_jobs if n_jobs > 0 else 1
                params['verbose'] = -1
                params['force_row_wise'] = True
                params['deterministic'] = True
                params['num_threads'] = params['n_jobs']
        elif self.model_name == 'CatBoost':
            if use_gpu_enabled and gpu_info.get('catboost_gpu', False):
                params['task_type'] = 'GPU'
                params['devices'] = '0'
                logger.info("CatBoost使用GPU加速")
            else:
                params['task_type'] = 'CPU'

        # 应用并行参数设置
        if n_jobs is not None and n_jobs > 0:
            if self.model_name in ['RandomForest']:
                params['n_jobs'] = min(4, n_jobs)  # 限制并行数避免资源竞争
            elif self.model_name == 'XGBoost' and params.get('device') == 'cpu':
                params['nthread'] = min(4, n_jobs)
            elif self.model_name in ['SVM', 'Logistic']:
                # 某些sklearn模型支持n_jobs
                if hasattr(self.model_class(), 'n_jobs'):
                    params['n_jobs'] = min(4, n_jobs)
            # 注意：KNN不支持n_jobs参数，已经在默认参数中移除

    def train_and_evaluate(self, X_train, y_train, X_test, y_test, params=None, use_gpu=True, n_jobs=-1):
        """
        训练和评估模型的统一方法

        Args:
            X_train, y_train: 训练数据
            X_test, y_test: 测试数据
            params: 自定义参数
            use_gpu: 是否使用GPU（仅对XGBoost有效）
            n_jobs: 并行作业数（对支持的模型有效）

        Returns:
            训练好的模型
        """
        start_time = time.time()

        # 合并参数
        final_params = self.default_params.copy()
        if params:
            # 处理Pipeline参数前缀（用于Logistic、SVM、KNN、NeuralNet）
            if self.model_name in ['Logistic', 'SVM', 'KNN', 'NeuralNet']:
                # 移除'clf__'前缀
                cleaned_params = {}
                for k, v in params.items():
                    if k.startswith('clf__'):
                        cleaned_params[k[5:]] = v  # 去掉'clf__'
                    else:
                        cleaned_params[k] = v
                final_params.update(cleaned_params)
            else:
                final_params.update(params)

        # 处理GPU和并行参数
        self._apply_gpu_and_parallel_params(final_params, use_gpu, n_jobs)

        # 针对二分类不平衡，按需为部分模型注入权重参数
        try:
            labels, counts = np.unique(y_train, return_counts=True)
            if len(labels) == 2 and counts.min() > 0:
                neg_count = counts[labels.tolist().index(0)] if 0 in labels else counts.max()
                pos_count = counts[labels.tolist().index(1)] if 1 in labels else counts.min()
                if pos_count > 0:
                    spw = float(neg_count) / float(pos_count)
                    # XGBoost/LightGBM: 使用 scale_pos_weight
                    if self.model_name in ['XGBoost', 'LightGBM'] and 'scale_pos_weight' not in final_params:
                        final_params['scale_pos_weight'] = max(spw, 1.0)
                        logger.info(f"[{self.model_name}] 自动设置 scale_pos_weight={final_params['scale_pos_weight']:.3f} 以处理不平衡")
                    # CatBoost: 使用自动类别权重
                    if self.model_name == 'CatBoost' and ('class_weights' not in final_params and 'auto_class_weights' not in final_params):
                        final_params['auto_class_weights'] = 'Balanced'
                        logger.info("[CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡")
        except Exception as e:
            logger.warning(f"不平衡权重自动设置失败: {e}")

        # 创建模型
        # 为需要标准化的模型使用Pipeline
        if self.model_name in ['Logistic', 'SVM', 'KNN', 'NeuralNet']:
            from sklearn.pipeline import Pipeline
            from sklearn.preprocessing import StandardScaler
            
            # 创建Pipeline
            model = Pipeline([
                ('scaler', StandardScaler()),
                ('classifier', self.model_class(**final_params))
            ])
            
            # 如果有scaler实例，使用它而不是创建新的
            if hasattr(self, '_scaler') and self._scaler is not None:
                logger.info(f"[{self.model_name}] 使用传入的scaler实例")
                model.set_params(scaler=self._scaler)
            
            logger.info(f"[{self.model_name}] 使用Pipeline进行标准化")
        else:
            model = self.model_class(**final_params)

        # 训练模型
        try:
            # 为KNN添加简单的超时保护
            if self.model_name == 'KNN':
                timeout_seconds = 10  # 10秒超时
                
                # 使用线程来检测超时
                import threading
                result = {'completed': False, 'error': None}
                
                def train_thread():
                    try:
                        model.fit(X_train, y_train)
                        result['completed'] = True
                    except Exception as e:
                        result['error'] = e
                
                thread = threading.Thread(target=train_thread)
                thread.daemon = True
                thread.start()
                thread.join(timeout=timeout_seconds)
                
                if thread.is_alive():
                    # 超时了，尝试使用更简单的参数
                    logger.warning(f"[KNN] 训练超时，使用简化参数")
                    simple_params = final_params.copy()
                    simple_params.pop('n_jobs', None)
                    simple_params.pop('algorithm', None)
                    simple_params['n_neighbors'] = min(3, len(X_train))
                    model = self.model_class(**simple_params)
                    if self.model_name in ['Logistic', 'SVM', 'KNN', 'NeuralNet']:
                        from sklearn.pipeline import Pipeline
                        from sklearn.preprocessing import StandardScaler
                        model = Pipeline([
                            ('scaler', StandardScaler()),
                            ('classifier', model)
                        ])
                    model.fit(X_train, y_train)
                elif result['error']:
                    raise result['error']
            else:
                model.fit(X_train, y_train)
        except Exception as e:
            logger.error(f"[{self.model_name}] 模型训练失败: {e}")
            # 尝试使用更简单的参数重新训练
            if self.model_name == 'KNN':
                logger.info(f"[{self.model_name}] 尝试使用简化参数重新训练...")
                # 移除可能导致问题的参数
                simple_params = final_params.copy()
                simple_params.pop('n_jobs', None)  # KNN不支持n_jobs
                simple_params.pop('algorithm', None)  # 使用默认算法
                simple_params['n_neighbors'] = min(5, len(X_train))  # 确保邻居数不超过样本数
                model = self.model_class(**simple_params)
                if self.model_name in ['Logistic', 'SVM', 'KNN', 'NeuralNet']:
                    from sklearn.pipeline import Pipeline
                    from sklearn.preprocessing import StandardScaler
                    model = Pipeline([
                        ('scaler', StandardScaler()),
                        ('classifier', model)
                    ])
                model.fit(X_train, y_train)
            else:
                raise e

        # 预测
        try:
            y_pred = model.predict(X_test)
        except Exception as e:
            logger.error(f"[{self.model_name}] 预测失败: {e}")
            raise e
            
        # 连续分数优先：predict_proba -> decision_function -> None
        y_score = None
        try:
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(X_test)
                y_score = proba[:, 1] if getattr(proba, 'ndim', 1) > 1 else proba
            elif hasattr(model, 'decision_function'):
                score = model.decision_function(X_test)
                y_score = score
        except Exception as e:
            logger.warning(f"[{self.model_name}] 获取预测分数失败: {e}")
            y_score = None

        # 计算指标
        accuracy = accuracy_score(y_test, y_pred)
        auc = roc_auc_score(y_test, y_score) if y_score is not None else None
        auprc = average_precision_score(y_test, y_score) if y_score is not None else None

        # 输出结果
        display_name = MODEL_DISPLAY_NAMES.get(self.model_name, self.model_name)
        logger.info(f"模型名称: {display_name}")
        logger.info(f"准确率: {accuracy:.4f}")
        if auc is not None:
            logger.info(f"AUC: {auc:.4f}")
        if auprc is not None:
            logger.info(f"AUPRC: {auprc:.4f}")
        logger.info("混淆矩阵:")
        logger.info(f"\n{confusion_matrix(y_test, y_pred)}")
        logger.info("\n分类报告:")
        logger.info(f"\n{classification_report(y_test, y_pred)}")

        end_time = time.time()
        logger.info(f"训练时间: {end_time - start_time:.2f} 秒")

        # 缓存结果（传递scaler信息，如果有的话）
        scaler = getattr(self, '_scaler', None)
        self._cache_results(model, y_test, y_pred, y_score, X_test, scaler)

        return model

    def _cache_results(self, model, y_true, y_pred, y_score, X_test, scaler=None):
        """缓存模型结果"""
        # 确保所有数据都不为None
        if y_true is None or y_pred is None or X_test is None:
            logger.error(f"模型 {self.model_name} 的关键数据为None，无法保存")
            return

        # 构建完整的结果数据
        result = {
            'model': model,
            'y_true': y_true,
            'y_pred': y_pred,
            # 统一命名：y_score表示用于阈值无关指标的连续分数
            'y_score': y_score,
            'X_test': X_test,
            'scaler': scaler  # 新增：缓存训练时使用的scaler
        }

        # 添加特征名称到结果中
        if hasattr(X_test, 'columns'):
            result['feature_names'] = list(X_test.columns)
        else:
            # 如果没有列名，创建默认特征名称
            n_features = X_test.shape[1] if hasattr(X_test, 'shape') else len(X_test[0])
            result['feature_names'] = [f'feature_{i}' for i in range(n_features)]

        # 添加性能指标
        try:
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, average_precision_score, roc_auc_score
            metrics = {
                'accuracy': accuracy_score(y_true, y_pred),
                'precision': precision_score(y_true, y_pred, average='weighted', zero_division=0),
                'recall': recall_score(y_true, y_pred, average='weighted', zero_division=0),
                'f1_score': f1_score(y_true, y_pred, average='weighted', zero_division=0)
            }

            # 如果是二分类，添加AUC与AUPRC（仅当有连续分数时）
            if 'y_score' in result and result['y_score'] is not None and len(set(y_true)) == 2:
                try:
                    y_score_vec = result['y_score']
                    metrics['auc'] = roc_auc_score(y_true, y_score_vec)
                    metrics['auc_pr'] = average_precision_score(y_true, y_score_vec)
                except Exception:
                    metrics['auc'] = None
                    metrics['auc_pr'] = None

            result['metrics'] = metrics
            logger.info(f"模型 {self.model_name} 性能: 准确率={metrics['accuracy']:.4f}")

        except Exception as e:
            logger.warning(f"计算性能指标失败: {e}")
            result['metrics'] = {}

        # 优先使用会话管理系统保存
        current_session = get_current_session()
        if current_session:
            try:
                session_path = save_to_session(
                    result, 'model', self.model_name,
                    model_type='single',
                    additional_data={
                        'training_time': getattr(self, '_training_time', None),
                        'model_params': getattr(model, 'get_params', lambda: {})(),
                        'data_shape': X_test.shape if hasattr(X_test, 'shape') else None,
                        'n_classes': len(set(y_true))
                    }
                )

                if session_path:
                    logger.info(f"模型 {self.model_name} 已保存到会话: {session_path}")

                    # 同时保存到传统缓存以保持兼容性
                    cache_file = CACHE_PATH / f"{self.model_name}_results.joblib"
                    dump(result, cache_file)
                    logger.info(f"模型 {self.model_name} 同时保存到缓存: {cache_file}")

                    return
            except Exception as e:
                logger.warning(f"保存到会话失败，使用传统方式: {e}")

        # 传统缓存方式作为备选
        cache_file = CACHE_PATH / f"{self.model_name}_results.joblib"
        dump(result, cache_file)
        logger.info(f"模型 {self.model_name} 的结果已缓存到: {cache_file}")

        # 保存特征名称（如果X_test是DataFrame）
        try:
            feature_names_file = CACHE_PATH / f"{self.model_name}_feature_names.joblib"
            dump(result['feature_names'], feature_names_file)
            logger.info(f"特征名称已缓存到: {feature_names_file}")
        except Exception as e:
            logger.warning(f"保存特征名称失败: {e}")


# 模型训练器实例
MODEL_TRAINERS = {
    'DecisionTree': ModelTrainer('DecisionTree', DecisionTreeClassifier, {
        'random_state': RANDOM_SEED,
        'max_depth': 5,  # 限制深度防止过拟合
        'min_samples_split': 20,  # 增加分割所需最小样本数
        'min_samples_leaf': 10,   # 增加叶节点最小样本数
        'class_weight': 'balanced'  # 处理不平衡数据
    }),
    'RandomForest': ModelTrainer('RandomForest', RandomForestClassifier, {'random_state': RANDOM_SEED, 'class_weight': 'balanced'}),
    'XGBoost': ModelTrainer('XGBoost', XGBClassifier, {'random_state': RANDOM_SEED, 'use_label_encoder': False, 'eval_metric': 'logloss'}),
    'LightGBM': ModelTrainer('LightGBM', LGBMClassifier, {'random_state': RANDOM_SEED}),
    'CatBoost': ModelTrainer('CatBoost', CatBoostClassifier, {'random_state': RANDOM_SEED, 'verbose': 0}),
    'Logistic': ModelTrainer('Logistic', LogisticRegression, {'random_state': RANDOM_SEED, 'max_iter': 2000, 'class_weight': 'balanced'}),
    'SVM': ModelTrainer('SVM', SVC, {'random_state': RANDOM_SEED, 'probability': True, 'class_weight': 'balanced'}),
    'KNN': ModelTrainer('KNN', KNeighborsClassifier, {
        'n_neighbors': 5,  # 默认邻居数
        'weights': 'uniform',  # 权重计算方式
        'algorithm': 'auto'  # 自动选择算法
    }),
    'NaiveBayes': ModelTrainer('NaiveBayes', GaussianNB, {}),
    'NeuralNet': ModelTrainer('NeuralNet', MLPClassifier, {'random_state': RANDOM_SEED, 'max_iter': 2000})
}


# 保持向后兼容的训练函数（使用新的ModelTrainer类）
def train_decision_tree(X_train, y_train, X_test, y_test, params=None):
    """训练决策树模型"""
    return MODEL_TRAINERS['DecisionTree'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_random_forest(X_train, y_train, X_test, y_test, params=None):
    """训练随机森林模型"""
    return MODEL_TRAINERS['RandomForest'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_xgboost(X_train, y_train, X_test, y_test, params=None):
    """训练XGBoost模型"""
    return MODEL_TRAINERS['XGBoost'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_lightgbm(X_train, y_train, X_test, y_test, params=None):
    """训练LightGBM模型"""
    return MODEL_TRAINERS['LightGBM'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_catboost(X_train, y_train, X_test, y_test, params=None):
    """训练CatBoost模型"""
    return MODEL_TRAINERS['CatBoost'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_logistic(X_train, y_train, X_test, y_test, params=None):
    """训练逻辑回归模型"""
    return MODEL_TRAINERS['Logistic'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_svm(X_train, y_train, X_test, y_test, params=None):
    """训练SVM模型"""
    return MODEL_TRAINERS['SVM'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_knn(X_train, y_train, X_test, y_test, params=None):
    """训练KNN模型"""
    return MODEL_TRAINERS['KNN'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_naive_bayes(X_train, y_train, X_test, y_test, params=None):
    """训练朴素贝叶斯模型"""
    return MODEL_TRAINERS['NaiveBayes'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


def train_neural_net(X_train, y_train, X_test, y_test, params=None):
    """训练神经网络模型"""
    return MODEL_TRAINERS['NeuralNet'].train_and_evaluate(X_train, y_train, X_test, y_test, params)


# 兼容旧版本的train_and_evaluate函数
def train_and_evaluate(model, model_name, X_train, y_train, X_test, y_test):
    """
    兼容旧版本的训练和评估函数
    建议使用新的ModelTrainer类
    """
    logger.warning("使用了已弃用的train_and_evaluate函数，建议使用ModelTrainer类")

    start_time = time.time()
    model.fit(X_train, y_train)
    y_pred = model.predict(X_test)
    # 连续分数优先：predict_proba -> decision_function -> None
    y_score = None
    if hasattr(model, 'predict_proba'):
        proba = model.predict_proba(X_test)
        y_score = proba[:, 1] if getattr(proba, 'ndim', 1) > 1 else proba
    elif hasattr(model, 'decision_function'):
        y_score = model.decision_function(X_test)

    accuracy = accuracy_score(y_test, y_pred)
    auc = roc_auc_score(y_test, y_score) if y_score is not None else None

    display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)
    logger.info(f"模型名称: {display_name}")
    logger.info(f"准确率: {accuracy:.4f}")
    if auc is not None:
        logger.info(f"AUC: {auc:.4f}")
    logger.info("混淆矩阵:")
    logger.info(f"\n{confusion_matrix(y_test, y_pred)}")
    logger.info("\n分类报告:")
    logger.info(f"\n{classification_report(y_test, y_pred)}")

    end_time = time.time()
    logger.info(f"训练时间: {end_time - start_time:.2f} 秒")

    # 缓存模型结果
    result = {
        'model': model,
        'y_true': y_test,
        'y_pred': y_pred,
        'y_score': y_score,
        'X_test': X_test
    }
    cache_file = CACHE_PATH / f"{model_name}_results.joblib"
    dump(result, cache_file)
    logger.info(f"模型 {model_name} 的结果已缓存到: {cache_file}")

    # 保存特征名称（如果X_test是DataFrame）
    try:
        if hasattr(X_test, 'columns'):
            feature_names = list(X_test.columns)
            feature_names_file = CACHE_PATH / f"{model_name}_feature_names.joblib"
            dump(feature_names, feature_names_file)
            logger.info(f"特征名称已缓存到: {feature_names_file}")
    except Exception as e:
        logger.warning(f"保存特征名称失败: {e}")

    return model
