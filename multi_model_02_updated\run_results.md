2025-08-29 02:34:32 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-29 02:34:32 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-29 02:34:32 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 02:34:32 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 02:34:32 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 02:34:32 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 02:34:32 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 02:34:32 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 02:34:32 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 02:34:32 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
[I 2025-08-29 02:34:32,339] A new study created in memory with name: no-name-de9bf333-19c9-4107-a9a6-2c63cb02d723
2025-08-29 02:34:32 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-29 02:34:32 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-29 02:35:02 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 7, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-29 02:35:02 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 7, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
[I 2025-08-29 02:35:02,346] Trial 0 finished with value: 0.5 and parameters: {'clf__n_neighbors': 7, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}. Best is trial 0 with value: 0.5.
2025-08-29 02:35:02 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.5000
2025-08-29 02:35:02 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.5000
[I 2025-08-29 02:35:02,384] Trial 1 finished with value: 0.9699777346836171 and parameters: {'clf__n_neighbors': 14, 'clf__weights': 'distance', 'clf__algorithm': 'ball_tree', 'clf__p': 1}. Best is trial 1 with value: 0.9699777346836171.
2025-08-29 02:35:02 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9700
2025-08-29 02:35:02 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9700
2025-08-29 02:35:32 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'distance', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 02:35:32 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'distance', 'clf__algorithm': 'auto', 'clf__p': 2}
[I 2025-08-29 02:35:32,388] Trial 2 finished with value: 0.5 and parameters: {'clf__n_neighbors': 5, 'clf__weights': 'distance', 'clf__algorithm': 'auto', 'clf__p': 2}. Best is trial 1 with value: 0.9699777346836171.
[I 2025-08-29 02:35:32,415] Trial 3 finished with value: 0.9546954679307621 and parameters: {'clf__n_neighbors': 4, 'clf__weights': 'distance', 'clf__algorithm': 'ball_tree', 'clf__p': 2}. Best is trial 1 with value: 0.9699777346836171.
[I 2025-08-29 02:35:32,445] Trial 4 finished with value: 0.954575163398693 and parameters: {'clf__n_neighbors': 10, 'clf__weights': 'distance', 'clf__algorithm': 'kd_tree', 'clf__p': 2}. Best is trial 1 with value: 0.9699777346836171.
2025-08-29 02:36:02 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 13, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-29 02:36:02 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 13, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
[I 2025-08-29 02:36:02,448] Trial 5 finished with value: 0.5 and parameters: {'clf__n_neighbors': 13, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}. Best is trial 1 with value: 0.9699777346836171.
2025-08-29 02:36:32 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 3, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 02:36:32 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 3, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}
[I 2025-08-29 02:36:32,454] Trial 6 finished with value: 0.5 and parameters: {'clf__n_neighbors': 3, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}. Best is trial 1 with value: 0.9699777346836171.
2025-08-29 02:37:02 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 02:37:02 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}
[I 2025-08-29 02:37:02,464] Trial 7 finished with value: 0.5 and parameters: {'clf__n_neighbors': 5, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}. Best is trial 1 with value: 0.9699777346836171.
[I 2025-08-29 02:37:02,494] Trial 8 finished with value: 0.9509480715363068 and parameters: {'clf__n_neighbors': 4, 'clf__weights': 'uniform', 'clf__algorithm': 'ball_tree', 'clf__p': 2}. Best is trial 1 with value: 0.9699777346836171.
[I 2025-08-29 02:37:02,520] Trial 9 finished with value: 0.9510809451985924 and parameters: {'clf__n_neighbors': 7, 'clf__weights': 'distance', 'clf__algorithm': 'ball_tree', 'clf__p': 2}. Best is trial 1 with value: 0.9699777346836171.
[I 2025-08-29 02:37:02,559] Trial 10 finished with value: 0.9678481649069886 and parameters: {'clf__n_neighbors': 15, 'clf__weights': 'distance', 'clf__algorithm': 'kd_tree', 'clf__p': 1}. Best is trial 1 with value: 0.9699777346836171.
[I 2025-08-29 02:37:02,590] Trial 11 finished with value: 0.9678481649069886 and parameters: {'clf__n_neighbors': 15, 'clf__weights': 'distance', 'clf__algorithm': 'kd_tree', 'clf__p': 1}. Best is trial 1 with value: 0.9699777346836171.
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9700
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9700
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 14, 'clf__weights': 'distance', 'clf__algorithm': 'ball_tree', 'clf__p': 1}
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 14, 'clf__weights': 'distance', 'clf__algorithm': 'ball_tree', 'clf__p': 1}
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9700
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9700
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\optimization_history_20250829_023702.html
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\optimization_history_20250829_023702.html
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\param_importances_20250829_023702.html
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\param_importances_20250829_023702.html
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 150.39 秒
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 150.39 秒
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
[I 2025-08-29 02:37:02,737] A new study created in memory with name: no-name-05ce4239-ded7-412e-9962-1cde338c8884
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9509
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9509
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
[I 2025-08-29 02:37:02,763] A new study created in memory with name: no-name-dcd27ac1-ecf4-45db-9148-d52554612bd0
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 300}
2025-08-29 02:37:02 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 300}
[I 2025-08-29 02:37:07,692] Trial 0 finished with value: 0.9697083961789843 and parameters: {'clf__hidden_layer_sizes': (100,), 'clf__alpha': 0.0011406035189014205}. Best is trial 0 with value: 0.9697083961789843.
2025-08-29 02:37:07 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9697
2025-08-29 02:37:07 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9697
[I 2025-08-29 02:37:07,743] Trial 1 finished with value: 0.9697083961789843 and parameters: {'clf__hidden_layer_sizes': (100,), 'clf__alpha': 0.00284874943591449}. Best is trial 0 with value: 0.9697083961789843.
[I 2025-08-29 02:37:07,935] Trial 2 finished with value: 0.9721827192415426 and parameters: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.00017305226449400947}. Best is trial 2 with value: 0.9721827192415426.
2025-08-29 02:37:07 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9722
2025-08-29 02:37:07 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9722
[I 2025-08-29 02:37:08,426] Trial 5 finished with value: 0.9714968038497449 and parameters: {'clf__hidden_layer_sizes': (50, 50), 'clf__alpha': 0.004335886951576353}. Best is trial 2 with value: 0.9721827192415426.
[I 2025-08-29 02:37:08,537] Trial 3 finished with value: 0.9714968038497449 and parameters: {'clf__hidden_layer_sizes': (50, 50), 'clf__alpha': 0.00289743818612268}. Best is trial 2 with value: 0.9721827192415426.
[I 2025-08-29 02:37:08,652] Trial 4 finished with value: 0.970642102995044 and parameters: {'clf__hidden_layer_sizes': (50, 50), 'clf__alpha': 0.009372571887793106}. Best is trial 2 with value: 0.9721827192415426.
[I 2025-08-29 02:37:12,662] Trial 6 finished with value: 0.9697083961789843 and parameters: {'clf__hidden_layer_sizes': (100,), 'clf__alpha': 0.00739906496758633}. Best is trial 2 with value: 0.9721827192415426.
[I 2025-08-29 02:37:13,153] Trial 8 finished with value: 0.9721827192415426 and parameters: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.0008912141334489102}. Best is trial 2 with value: 0.9721827192415426.
[I 2025-08-29 02:37:13,491] Trial 9 finished with value: 0.9721827192415426 and parameters: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.0026850469097211235}. Best is trial 2 with value: 0.9721827192415426.
[I 2025-08-29 02:37:13,652] Trial 7 finished with value: 0.9714968038497449 and parameters: {'clf__hidden_layer_sizes': (50, 50), 'clf__alpha': 0.004270140625819077}. Best is trial 2 with value: 0.9721827192415426.
[I 2025-08-29 02:37:14,441] Trial 10 finished with value: 0.9714968038497449 and parameters: {'clf__hidden_layer_sizes': (50, 50), 'clf__alpha': 0.0021465892470204227}. Best is trial 2 with value: 0.9721827192415426.
[I 2025-08-29 02:37:14,710] Trial 11 finished with value: 0.9714968038497449 and parameters: {'clf__hidden_layer_sizes': (50, 50), 'clf__alpha': 0.003658668767993402}. Best is trial 2 with value: 0.9721827192415426.
[I 2025-08-29 02:37:18,036] Trial 12 finished with value: 0.9721827192415426 and parameters: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.007462586913491276}. Best is trial 2 with value: 0.9721827192415426.
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9722
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9722
[I 2025-08-29 02:37:18,558] Trial 13 finished with value: 0.9697083961789843 and parameters: {'clf__hidden_layer_sizes': (100,), 'clf__alpha': 0.0013787036235128585}. Best is trial 2 with value: 0.9721827192415426.
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9722
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9722
[I 2025-08-29 02:37:18,801] Trial 14 finished with value: 0.9697083961789843 and parameters: {'clf__hidden_layer_sizes': (100,), 'clf__alpha': 0.00011693515829462684}. Best is trial 2 with value: 0.9721827192415426.
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9722
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9722
[I 2025-08-29 02:37:18,926] Trial 15 finished with value: 0.9721827192415426 and parameters: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.007450977927785803}. Best is trial 2 with value: 0.9721827192415426.
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9722
2025-08-29 02:37:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9722
[I 2025-08-29 02:37:19,197] Trial 16 finished with value: 0.9721827192415426 and parameters: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.0004984438294179567}. Best is trial 2 with value: 0.9721827192415426.
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9722
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9722
[I 2025-08-29 02:37:19,235] Trial 17 finished with value: 0.9721827192415426 and parameters: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.0004581826431098707}. Best is trial 2 with value: 0.9721827192415426.
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9722
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9722
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.00017305226449400947}
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.00017305226449400947}
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9722
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9722
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\optimization_history_20250829_023719.html
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\optimization_history_20250829_023719.html
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\param_importances_20250829_023719.html
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\param_importances_20250829_023719.html
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 16.60 秒
2025-08-29 02:37:19 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 16.60 秒
✅ Matplotlib渲染优化完成
✅ 平滑渲染设置完成
✅ 平滑渲染设置完成
✅ 平滑渲染设置完成
✅ 平滑渲染设置完成
✅ 平滑渲染设置完成
✅ 平滑渲染设置完成
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
Exception in Tkinter callback
Traceback (most recent call last):
  File "D:\anaconda\envs\multi_model\lib\tkinter\__init__.py", line 1892, in __call__
    return self.func(*args)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 215, in scroll_event_windows    
    return self.scroll_event_windows(event)
  File "D:\anaconda\envs\multi_model\lib\site-packages\matplotlib\backends\_backend_tk.py", line 346, in scroll_event_windows    
    w = event.widget.winfo_containing(event.x_root, event.y_root)
AttributeError: 'str' object has no attribute 'winfo_containing'
自动检测到目标列: label
成功加载  数据，训练集大小: (197, 16)
2025-08-29 02:42:33 - config_manager - INFO - 默认配置已初始化
2025-08-29 02:42:33 - config_manager - INFO - 默认配置已初始化
2025-08-29 02:42:33 - config_manager - INFO - 注册预处理器: current_scaler (StandardScaler)
2025-08-29 02:42:33 - config_manager - INFO - 注册预处理器: current_scaler (StandardScaler)
2025-08-29 02:42:33 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-29 02:42:33 - enhanced_ensemble_selector - INFO - 开始评估 10 个基模型...
2025-08-29 02:42:33 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 02:42:33 - enhanced_ensemble_selector - INFO -   评估模型: DecisionTree
2025-08-29 02:42:34 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 02:42:34 - model_training - INFO - 模型名称: Decision Tree
2025-08-29 02:42:34 - model_training - INFO - 准确率: 0.7250
2025-08-29 02:42:34 - model_training - INFO - 准确率: 0.7250
2025-08-29 02:42:34 - model_training - INFO - AUC: 0.8210
2025-08-29 02:42:34 - model_training - INFO - AUC: 0.8210
2025-08-29 02:42:34 - model_training - INFO - AUPRC: 0.6807
2025-08-29 02:42:34 - model_training - INFO - AUPRC: 0.6807
2025-08-29 02:42:34 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:34 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:34 - model_training - INFO -
[[15  8]
 [ 3 14]]
2025-08-29 02:42:34 - model_training - INFO -
[[15  8]
 [ 3 14]]
2025-08-29 02:42:34 - model_training - INFO -
分类报告:
2025-08-29 02:42:34 - model_training - INFO -
分类报告:
2025-08-29 02:42:34 - model_training - INFO -
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-29 02:42:34 - model_training - INFO -
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-29 02:42:34 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 02:42:34 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 02:42:34 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-29 02:42:34 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-29 02:42:34 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 02:42:34 - model_training - INFO - 模型 DecisionTree 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_results.joblib
2025-08-29 02:42:34 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_feature_names.joblib
2025-08-29 02:42:34 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\DecisionTree_feature_names.joblib
2025-08-29 02:42:34 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7523
2025-08-29 02:42:34 - enhanced_ensemble_selector - INFO -     DecisionTree - 性能得分: 0.7523
2025-08-29 02:42:34 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 02:42:34 - enhanced_ensemble_selector - INFO -   评估模型: RandomForest
2025-08-29 02:42:34 - model_training - INFO - 模型名称: Random Forest
2025-08-29 02:42:34 - model_training - INFO - 模型名称: Random Forest
2025-08-29 02:42:34 - model_training - INFO - 准确率: 0.8500
2025-08-29 02:42:34 - model_training - INFO - 准确率: 0.8500
2025-08-29 02:42:34 - model_training - INFO - AUC: 0.9501
2025-08-29 02:42:34 - model_training - INFO - AUC: 0.9501
2025-08-29 02:42:34 - model_training - INFO - AUPRC: 0.9410
2025-08-29 02:42:34 - model_training - INFO - AUPRC: 0.9410
2025-08-29 02:42:34 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:34 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:34 - model_training - INFO -
[[19  4]
 [ 2 15]]
2025-08-29 02:42:34 - model_training - INFO -
[[19  4]
 [ 2 15]]
2025-08-29 02:42:34 - model_training - INFO -
分类报告:
2025-08-29 02:42:34 - model_training - INFO -
分类报告:
2025-08-29 02:42:34 - model_training - INFO -
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 02:42:34 - model_training - INFO -
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-29 02:42:34 - model_training - INFO - 训练时间: 0.18 秒
2025-08-29 02:42:34 - model_training - INFO - 训练时间: 0.18 秒
2025-08-29 02:42:34 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 02:42:34 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-29 02:42:34 - model_training - INFO - 模型 RandomForest 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 02:42:34 - model_training - INFO - 模型 RandomForest 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_results.joblib
2025-08-29 02:42:34 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_feature_names.joblib
2025-08-29 02:42:34 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\RandomForest_feature_names.joblib
2025-08-29 02:42:35 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-29 02:42:35 - enhanced_ensemble_selector - INFO -     RandomForest - 性能得分: 0.8759
2025-08-29 02:42:35 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-29 02:42:35 - enhanced_ensemble_selector - INFO -   评估模型: XGBoost
2025-08-29 02:42:35 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 02:42:35 - model_training - INFO - XGBoost使用GPU加速
2025-08-29 02:42:35 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 02:42:35 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
[02:42:35] WARNING: D:\bld\xgboost-split_1744329010901\work\src\context.cc:196: XGBoost is not compiled with CUDA support.       
[02:42:35] WARNING: D:\bld\xgboost-split_1744329010901\work\src\learner.cc:740:
Parameters: { "use_label_encoder" } are not used.

2025-08-29 02:42:35 - model_training - INFO - 模型名称: XGBoost
2025-08-29 02:42:35 - model_training - INFO - 模型名称: XGBoost
2025-08-29 02:42:35 - model_training - INFO - 准确率: 0.8250
2025-08-29 02:42:35 - model_training - INFO - 准确率: 0.8250
2025-08-29 02:42:35 - model_training - INFO - AUC: 0.9437
2025-08-29 02:42:35 - model_training - INFO - AUC: 0.9437
2025-08-29 02:42:35 - model_training - INFO - AUPRC: 0.9387
2025-08-29 02:42:35 - model_training - INFO - AUPRC: 0.9387
2025-08-29 02:42:35 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:35 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:35 - model_training - INFO -
[[18  5]
 [ 2 15]]
2025-08-29 02:42:35 - model_training - INFO -
[[18  5]
 [ 2 15]]
2025-08-29 02:42:35 - model_training - INFO -
分类报告:
2025-08-29 02:42:35 - model_training - INFO -
分类报告:
2025-08-29 02:42:35 - model_training - INFO -
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 02:42:35 - model_training - INFO -
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-29 02:42:35 - model_training - INFO - 训练时间: 0.13 秒
2025-08-29 02:42:35 - model_training - INFO - 训练时间: 0.13 秒
2025-08-29 02:42:35 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-29 02:42:35 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-29 02:42:35 - model_training - INFO - 模型 XGBoost 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 02:42:35 - model_training - INFO - 模型 XGBoost 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_results.joblib
2025-08-29 02:42:35 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_feature_names.joblib
2025-08-29 02:42:35 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\XGBoost_feature_names.joblib
2025-08-29 02:42:36 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-29 02:42:36 - enhanced_ensemble_selector - INFO -     XGBoost - 性能得分: 0.8563
2025-08-29 02:42:36 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-29 02:42:36 - enhanced_ensemble_selector - INFO -   评估模型: LightGBM
2025-08-29 02:42:36 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 02:42:36 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-29 02:42:36 - model_training - INFO - 模型名称: LightGBM
2025-08-29 02:42:36 - model_training - INFO - 模型名称: LightGBM
2025-08-29 02:42:36 - model_training - INFO - 准确率: 0.8750
2025-08-29 02:42:36 - model_training - INFO - 准确率: 0.8750
2025-08-29 02:42:36 - model_training - INFO - AUC: 0.9642
2025-08-29 02:42:36 - model_training - INFO - AUC: 0.9642
2025-08-29 02:42:36 - model_training - INFO - AUPRC: 0.9541
2025-08-29 02:42:36 - model_training - INFO - AUPRC: 0.9541
2025-08-29 02:42:36 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:36 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:36 - model_training - INFO -
[[19  4]
 [ 1 16]]
2025-08-29 02:42:36 - model_training - INFO -
[[19  4]
 [ 1 16]]
2025-08-29 02:42:36 - model_training - INFO -
分类报告:
2025-08-29 02:42:36 - model_training - INFO -
分类报告:
2025-08-29 02:42:36 - model_training - INFO -
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 02:42:36 - model_training - INFO -
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-29 02:42:36 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 02:42:36 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 02:42:36 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 02:42:36 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-29 02:42:36 - model_training - INFO - 模型 LightGBM 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 02:42:36 - model_training - INFO - 模型 LightGBM 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_results.joblib
2025-08-29 02:42:36 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_feature_names.joblib
2025-08-29 02:42:36 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\LightGBM_feature_names.joblib
2025-08-29 02:42:36 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-29 02:42:36 - enhanced_ensemble_selector - INFO -     LightGBM - 性能得分: 0.8989
2025-08-29 02:42:36 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-29 02:42:36 - enhanced_ensemble_selector - INFO -   评估模型: CatBoost
2025-08-29 02:42:36 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 02:42:36 - model_training - INFO - CatBoost使用GPU加速
2025-08-29 02:42:36 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 02:42:36 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-29 02:42:56 - model_training - INFO - 模型名称: CatBoost
2025-08-29 02:42:56 - model_training - INFO - 模型名称: CatBoost
2025-08-29 02:42:56 - model_training - INFO - 准确率: 0.8500
2025-08-29 02:42:56 - model_training - INFO - 准确率: 0.8500
2025-08-29 02:42:56 - model_training - INFO - AUC: 0.9668
2025-08-29 02:42:56 - model_training - INFO - AUC: 0.9668
2025-08-29 02:42:56 - model_training - INFO - AUPRC: 0.9616
2025-08-29 02:42:56 - model_training - INFO - AUPRC: 0.9616
2025-08-29 02:42:56 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:56 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:56 - model_training - INFO -
[[18  5]
 [ 1 16]]
2025-08-29 02:42:56 - model_training - INFO -
[[18  5]
 [ 1 16]]
2025-08-29 02:42:56 - model_training - INFO -
分类报告:
2025-08-29 02:42:56 - model_training - INFO -
分类报告:
2025-08-29 02:42:56 - model_training - INFO -
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 02:42:56 - model_training - INFO -
              precision    recall  f1-score   support

           0       0.95      0.78      0.86        23
           1       0.76      0.94      0.84        17

    accuracy                           0.85        40
   macro avg       0.85      0.86      0.85        40
weighted avg       0.87      0.85      0.85        40

2025-08-29 02:42:56 - model_training - INFO - 训练时间: 19.46 秒
2025-08-29 02:42:56 - model_training - INFO - 训练时间: 19.46 秒
2025-08-29 02:42:56 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8500
2025-08-29 02:42:56 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8500
2025-08-29 02:42:56 - model_training - INFO - 模型 CatBoost 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 02:42:56 - model_training - INFO - 模型 CatBoost 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_results.joblib
2025-08-29 02:42:56 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_feature_names.joblib
2025-08-29 02:42:56 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\CatBoost_feature_names.joblib
2025-08-29 02:42:56 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8817
2025-08-29 02:42:56 - enhanced_ensemble_selector - INFO -     CatBoost - 性能得分: 0.8817
2025-08-29 02:42:56 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 02:42:56 - enhanced_ensemble_selector - INFO -   评估模型: Logistic
2025-08-29 02:42:57 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 02:42:57 - model_training - INFO - [Logistic] 使用传入的scaler实例
2025-08-29 02:42:57 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 02:42:57 - model_training - INFO - [Logistic] 使用Pipeline进行标准化
2025-08-29 02:42:57 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 02:42:57 - model_training - INFO - 模型名称: Logistic Regression
2025-08-29 02:42:57 - model_training - INFO - 准确率: 0.7750
2025-08-29 02:42:57 - model_training - INFO - 准确率: 0.7750
2025-08-29 02:42:57 - model_training - INFO - AUC: 0.9028
2025-08-29 02:42:57 - model_training - INFO - AUC: 0.9028
2025-08-29 02:42:57 - model_training - INFO - AUPRC: 0.8685
2025-08-29 02:42:57 - model_training - INFO - AUPRC: 0.8685
2025-08-29 02:42:57 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:57 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:57 - model_training - INFO -
[[17  6]
 [ 3 14]]
2025-08-29 02:42:57 - model_training - INFO -
[[17  6]
 [ 3 14]]
2025-08-29 02:42:57 - model_training - INFO -
分类报告:
2025-08-29 02:42:57 - model_training - INFO -
分类报告:
2025-08-29 02:42:57 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 02:42:57 - model_training - INFO -
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-29 02:42:57 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 02:42:57 - model_training - INFO - 训练时间: 0.12 秒
2025-08-29 02:42:57 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 02:42:57 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-29 02:42:57 - model_training - INFO - 模型 Logistic 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 02:42:57 - model_training - INFO - 模型 Logistic 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_results.joblib
2025-08-29 02:42:57 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_feature_names.joblib
2025-08-29 02:42:57 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\Logistic_feature_names.joblib
2025-08-29 02:42:57 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-29 02:42:57 - enhanced_ensemble_selector - INFO -     Logistic - 性能得分: 0.8087
2025-08-29 02:42:57 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-29 02:42:57 - enhanced_ensemble_selector - INFO -   评估模型: SVM
2025-08-29 02:42:58 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 02:42:58 - model_training - INFO - [SVM] 使用传入的scaler实例
2025-08-29 02:42:58 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 02:42:58 - model_training - INFO - [SVM] 使用Pipeline进行标准化
2025-08-29 02:42:58 - model_training - INFO - 模型名称: SVM
2025-08-29 02:42:58 - model_training - INFO - 模型名称: SVM
2025-08-29 02:42:58 - model_training - INFO - 准确率: 0.8250
2025-08-29 02:42:58 - model_training - INFO - 准确率: 0.8250
2025-08-29 02:42:58 - model_training - INFO - AUC: 0.9258
2025-08-29 02:42:58 - model_training - INFO - AUC: 0.9258
2025-08-29 02:42:58 - model_training - INFO - AUPRC: 0.9259
2025-08-29 02:42:58 - model_training - INFO - AUPRC: 0.9259
2025-08-29 02:42:58 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:58 - model_training - INFO - 混淆矩阵:
2025-08-29 02:42:58 - model_training - INFO -
[[17  6]
 [ 1 16]]
2025-08-29 02:42:58 - model_training - INFO -
[[17  6]
 [ 1 16]]
2025-08-29 02:42:58 - model_training - INFO -
分类报告:
2025-08-29 02:42:58 - model_training - INFO -
分类报告:
2025-08-29 02:42:58 - model_training - INFO -
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-29 02:42:58 - model_training - INFO -
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-29 02:42:58 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 02:42:58 - model_training - INFO - 训练时间: 0.11 秒
2025-08-29 02:42:58 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-29 02:42:58 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-29 02:42:58 - model_training - INFO - 模型 SVM 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 02:42:58 - model_training - INFO - 模型 SVM 的结果已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\SVM_results.joblib
2025-08-29 02:42:58 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\SVM_feature_names.joblib
2025-08-29 02:42:58 - model_training - INFO - 特征名称已缓存到: d:\Code\MM01U\multi_model_02_updated\cache\SVM_feature_names.joblib
2025-08-29 02:42:58 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-29 02:42:58 - enhanced_ensemble_selector - INFO -     SVM - 性能得分: 0.8537
2025-08-29 02:42:58 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 02:42:58 - enhanced_ensemble_selector - INFO -   评估模型: KNN
2025-08-29 02:42:58 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 02:42:58 - model_training - INFO - [KNN] 使用传入的scaler实例
2025-08-29 02:42:58 - model_training - INFO - [KNN] 使用Pipeline进行标准化
2025-08-29 02:42:58 - model_training - INFO - [KNN] 使用Pipeline进行标准化