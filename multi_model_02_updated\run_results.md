2025-08-29 03:07:17 - training_session_manager - INFO - 创建会话目录结构: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_030717
2025-08-29 03:07:17 - training_session_manager - INFO - 创建会话目录结构: d:\Code\MM01U\multi_model_02_updated\training_sessions\20250829_030717
2025-08-29 03:07:17 - training_session_manager - INFO - 创建训练会话: 训练_N-2_20250829_030717 (ID: 20250829_030717)
2025-08-29 03:07:17 - training_session_manager - INFO - 创建训练会话: 训练_N-2_20250829_030717 (ID: 20250829_030717)
2025-08-29 03:07:17 - training_session_manager - INFO - 创建新会话: 训练_N-2_20250829_030717
2025-08-29 03:07:17 - training_session_manager - INFO - 创建新会话: 训练_N-2_20250829_030717
2025-08-29 03:07:17 - session_utils - INFO - 创建新会话: 训练_N-2_20250829_030717 (ID: 20250829_030717)
2025-08-29 03:07:17 - session_utils - INFO - 创建新会话: 训练_N-2_20250829_030717 (ID: 20250829_030717)
自动检测到目标列: label
2025-08-29 03:07:17 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 16)
2025-08-29 03:07:17 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 16)
2025-08-29 03:07:17 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-29 03:07:17 - data_preprocessing - INFO - 应用了 standard 特征缩放

[03:07:17] 开始模型训练
[03:07:17] 使用标准训练模式
[03:07:17] 自动创建训练会话: 训练_N-2_20250829_030717
[03:07:17] 正在训练 DecisionTree...
[03:07:17] [DecisionTree] 使用数据预处理的scaler
[03:07:17] [DecisionTree] 性能配置: n_jobs=-1, GPU=禁用
[03:07:17] [DecisionTree] 使用调优后的参数
[03:07:17] DecisionTree 训练失败: local variable 'time' referenced before assignment
[03:07:17] 正在训练 RandomForest...
[03:07:17] [RandomForest] 使用数据预处理的scaler
[03:07:17] [RandomForest] 性能配置: n_jobs=-1, GPU=禁用
[03:07:17] [RandomForest] 使用调优后的参数
[03:07:17] RandomForest 训练失败: local variable 'time' referenced before assignment
[03:07:17] 正在训练 XGBoost...
[03:07:17] [XGBoost] 使用数据预处理的scaler
[03:07:17] [XGBoost] 性能配置: n_jobs=-1, GPU=启用
[03:07:17] [XGBoost] 使用调优后的参数
[03:07:17] XGBoost 训练失败: local variable 'time' referenced before assignment
[03:07:17] 正在训练 LightGBM...
[03:07:17] [LightGBM] 使用数据预处理的scaler
[03:07:17] [LightGBM] 性能配置: n_jobs=1, GPU=启用
[03:07:17] [LightGBM] 使用调优后的参数
[03:07:17] LightGBM 训练失败: local variable 'time' referenced before assignment
[03:07:17] 正在训练 CatBoost...
[03:07:17] [CatBoost] 使用数据预处理的scaler
[03:07:17] [CatBoost] 性能配置: n_jobs=1, GPU=启用
[03:07:17] [CatBoost] 使用调优后的参数
[03:07:17] CatBoost 训练失败: local variable 'time' referenced before assignment
[03:07:17] 正在训练 Logistic...
[03:07:17] [Logistic] 使用数据预处理的scaler
[03:07:17] [Logistic] 性能配置: n_jobs=4, GPU=禁用
[03:07:17] [Logistic] 使用调优后的参数
[03:07:17] Logistic 训练失败: local variable 'time' referenced before assignment
[03:07:17] 正在训练 SVM...
[03:07:17] [SVM] 使用数据预处理的scaler
[03:07:17] [SVM] 性能配置: n_jobs=4, GPU=禁用
[03:07:17] [SVM] 使用调优后的参数
[03:07:17] SVM 训练失败: local variable 'time' referenced before assignment
[03:07:17] 正在训练 KNN...
[03:07:17] [KNN] 使用数据预处理的scaler
[03:07:17] [KNN] 性能配置: n_jobs=4, GPU=禁用
[03:07:17] [KNN] 使用调优后的参数
[03:07:17] KNN 训练失败: local variable 'time' referenced before assignment
[03:07:17] 正在训练 NaiveBayes...
[03:07:17] [NaiveBayes] 使用数据预处理的scaler
[03:07:17] [NaiveBayes] 性能配置: n_jobs=-1, GPU=禁用
[03:07:17] [NaiveBayes] 使用调优后的参数
[03:07:17] NaiveBayes 训练失败: local variable 'time' referenced before assignment
[03:07:17] 正在训练 NeuralNet...
[03:07:17] [NeuralNet] 使用数据预处理的scaler
[03:07:17] [NeuralNet] 性能配置: n_jobs=4, GPU=禁用
[03:07:17] [NeuralNet] 使用调优后的参数
[03:07:17] NeuralNet 训练失败: local variable 'time' referenced before assignment
[03:07:17] 所有模型训练完成

现在我找到了问题的根源！在第201行，代码中有 `import time`，这在函数内部重新导入了 `time` 模块。但是问题在于，在第136行已经使用了 `start_time = time.time()`，然后在第201行又有 `import time`。

这种在函数内部重新导入模块的做法可能会导致作用域问题。让我检查完整的代码流程，看看是否存在逻辑错误。

现在我明白问题了！在第136行定义了 `start_time = time.time()`，但是在KNN的特殊处理中（第201-202行），又重新定义了 `start_time = time.time()`。这导致了两个问题：

1. 在第201行重新导入了 `time` 模块，这可能会影响作用域
2. 更重要的是，在第202行重新定义了 `start_time`，但这只在KNN的if分支中，对于其他模型，`start_time` 仍然是第136行定义的值

但是在第301行使用 `end_time - start_time` 时，如果出现异常或者其他问题，可能会导致变量引用错误。

让我修复这个问题：

现在让我运行一个简单的测试来验证修复是否有效：

编码问题，让我修复测试脚本：

太好了！测试成功了！现在让我清理测试文件并总结问题的解决方案：

## 问题分析和解决方案

我已经成功找到并修复了导致所有模型训练失败的问题。

### 问题根源

错误信息 `"local variable 'time' referenced before assignment"` 的原因是：

````python path=multi_model_02_updated/code/model_training.py mode=EXCERPT
def train_and_evaluate(self, X_train, y_train, X_test, y_test, params=None, use_gpu=True, n_jobs=-1):
    start_time = time.time()  # 第136行：首次定义start_time
    
    # ... 其他代码 ...
    
    # 训练模型
    try:
        # 为KNN添加简单的超时保护
        if self.model_name == 'KNN':
            import time              # 第201行：重新导入time模块（问题所在）
            start_time = time.time() # 第202行：重新定义start_time
````

### 具体问题

1. **重复导入模块**：在第201行的KNN特殊处理中重新导入了 `time` 模块，这在Python中会影响变量作用域
2. **变量重定义**：在第202行重新定义了 `start_time`，但这只在KNN分支中执行
3. **作用域混乱**：当Python解释器看到函数内部有 `import time` 时，可能会影响对全局 `time` 模块的引用

### 解决方案

我移除了KNN分支中不必要的 `import time` 语句和重复的 `start_time` 定义：

````python path=multi_model_02_updated/code/model_training.py mode=EXCERPT
# 修复后的代码
try:
    # 为KNN添加简单的超时保护
    if self.model_name == 'KNN':
        timeout_seconds = 10  # 10秒超时
        # 移除了: import time 和 start_time = time.time()
````

### 验证结果

测试显示修复成功：
- DecisionTree 模型训练成功，准确率 90%
- RandomForest 模型训练成功，准确率 93.33%
- 所有时间计算正常工作
- 没有出现 "local variable 'time' referenced before assignment" 错误

现在所有模型应该都能正常训练了。这个修复解决了变量作用域冲突的问题，确保 `time.time()` 调用能够正确引用全局导入的 `time` 模块。
