2025-08-29 03:19:05 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800}
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9202
2025-08-29 03:19:05 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9438
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9438
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9483
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 6, 'min_samples_split': 50, 'min_samples_leaf': 15, 'criterion': 'gini', 'class_weight': None, 'max_features': None}
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9483
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\optimization_history_20250829_031906.html
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\param_importances_20250829_031906.html
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.62 秒
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:19:06 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800}
2025-08-29 03:19:07 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9724
2025-08-29 03:19:11 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9741
2025-08-29 03:19:13 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9783
2025-08-29 03:19:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:19:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9783
2025-08-29 03:19:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:19:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9791
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9791
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9791
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 99, 'max_depth': 32, 'min_samples_split': 13, 'min_samples_leaf': 3, 'max_features': 'sqrt'}
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9791
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 实际执行试验次数: 20/50
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\optimization_history_20250829_031924.html
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\param_importances_20250829_031924.html
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 18.38 秒
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-29 03:19:24 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:25 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9784
2025-08-29 03:19:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:25 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:26 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9793
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 139, 'max_depth': 7, 'learning_rate': 0.29116576212848116, 'subsample': 0.5089809378074099, 'colsample_bytree': 0.9168158328299749}
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9793
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 实际执行试验次数: 11/50
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\optimization_history_20250829_031927.html
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\param_importances_20250829_031927.html
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 2.56 秒
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9775
2025-08-29 03:19:27 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9799
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9799
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 96, 'max_depth': 4, 'learning_rate': 0.16217936517334897, 'feature_fraction': 0.7159725093210578, 'bagging_fraction': 0.645614570099021}
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9799
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\optimization_history_20250829_031928.html
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\param_importances_20250829_031928.html
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.22 秒
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-29 03:19:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:33 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9699
2025-08-29 03:19:33 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:34 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9768
2025-08-29 03:19:34 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:36 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:38 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:41 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:46 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:49 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:53 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:55 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.9792
2025-08-29 03:19:55 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:19:59 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:02 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9810
2025-08-29 03:20:02 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:04 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:07 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:10 - hyperparameter_tuning - INFO - Trial 13: 发现更好的得分 0.9827
2025-08-29 03:20:10 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:13 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:26 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:31 - hyperparameter_tuning - INFO - Trial 17: 发现更好的得分 0.9842
2025-08-29 03:20:31 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:34 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:37 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:42 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:45 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:49 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:51 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:20:53 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:21:03 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:21:12 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9842
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 183, 'depth': 6, 'learning_rate': 0.14765398301499216, 'l2_leaf_reg': 2.24931553797329, 'bagging_temperature': 0.8703340675320387}
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9842
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 实际执行试验次数: 28/50
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\optimization_history_20250829_032120.html
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\param_importances_20250829_032120.html
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 111.66 秒
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9576
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9618
2025-08-29 03:21:20 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 9.632750328932904, 'clf__solver': 'lbfgs'}
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9635
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/50
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\optimization_history_20250829_032121.html
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\param_importances_20250829_032121.html
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.10 秒
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9560
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9619
2025-08-29 03:21:21 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9645
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - Trial 16: 发现更好的得分 0.9670
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - Trial 24: 发现更好的得分 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 8.181191598729157, 'clf__kernel': 'linear'}
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9687
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 实际执行试验次数: 40/50
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\optimization_history_20250829_032122.html
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\param_importances_20250829_032122.html
2025-08-29 03:21:22 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.52 秒
2025-08-29 03:21:23 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-29 03:21:23 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 03:21:23 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:21:23 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:21:23 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:21:23 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-29 03:21:53 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 7, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-29 03:21:53 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.5000
2025-08-29 03:21:53 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9453
2025-08-29 03:22:23 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'distance', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 03:22:53 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 13, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-29 03:23:23 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 3, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 03:23:53 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9453
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 14, 'clf__weights': 'distance', 'clf__algorithm': 'ball_tree', 'clf__p': 1}
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9453
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 实际执行试验次数: 12/50
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\optimization_history_20250829_032353.html
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\param_importances_20250829_032353.html
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 150.37 秒
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9263
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-29 03:23:53 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-29 03:23:58 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9541
2025-08-29 03:23:59 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9584
2025-08-29 03:24:05 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9601
2025-08-29 03:24:16 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:24:16 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9610
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9610
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9610
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9610
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9610
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9610
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.006974332966310803}
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9610
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/50
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\optimization_history_20250829_032417.html
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\param_importances_20250829_032417.html
2025-08-29 03:24:17 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 24.33 秒
