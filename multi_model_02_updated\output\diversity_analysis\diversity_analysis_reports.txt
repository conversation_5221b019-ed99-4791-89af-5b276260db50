集成学习模型组合多样性分析报告
============================================================

分析时间: 2025-08-29 04:35:44
选择策略: quantified
目标模型数量: 5

1. 模型性能概览
----------------------------------------

DecisionTree:
  性能得分: 0.7523
  AUC: 0.8210
  准确率: 0.7250
  F1分数: 0.7259

RandomForest:
  性能得分: 0.8759
  AUC: 0.9501
  准确率: 0.8500
  F1分数: 0.8508

XGBoost:
  性能得分: 0.8563
  AUC: 0.9437
  准确率: 0.8250
  F1分数: 0.8260

LightGBM:
  性能得分: 0.8989
  AUC: 0.9642
  准确率: 0.8750
  F1分数: 0.8757

CatBoost:
  性能得分: 0.8817
  AUC: 0.9668
  准确率: 0.8500
  F1分数: 0.8508

Logistic:
  性能得分: 0.8087
  AUC: 0.9028
  准确率: 0.7750
  F1分数: 0.7763

SVM:
  性能得分: 0.8537
  AUC: 0.9258
  准确率: 0.8250
  F1分数: 0.8255

NaiveBayes:
  性能得分: 0.8664
  AUC: 0.9156
  准确率: 0.8500
  F1分数: 0.8500

NeuralNet:
  性能得分: 0.8743
  AUC: 0.9437
  准确率: 0.8500
  F1分数: 0.8508


2. 模型间多样性分析
----------------------------------------

DecisionTree vs RandomForest:
  相关性: 0.8434
  多样性: 0.1566

DecisionTree vs XGBoost:
  相关性: 0.8907
  多样性: 0.1093

DecisionTree vs LightGBM:
  相关性: 0.8086
  多样性: 0.1914

DecisionTree vs CatBoost:
  相关性: 0.8593
  多样性: 0.1407

DecisionTree vs Logistic:
  相关性: 0.7779
  多样性: 0.2221

DecisionTree vs SVM:
  相关性: 0.7501
  多样性: 0.2499

DecisionTree vs NaiveBayes:
  相关性: 0.7138
  多样性: 0.2862

DecisionTree vs NeuralNet:
  相关性: 0.7780
  多样性: 0.2220

RandomForest vs XGBoost:
  相关性: 0.9160
  多样性: 0.0840

RandomForest vs LightGBM:
  相关性: 0.9277
  多样性: 0.0723

RandomForest vs CatBoost:
  相关性: 0.9458
  多样性: 0.0542

RandomForest vs Logistic:
  相关性: 0.9427
  多样性: 0.0573

RandomForest vs SVM:
  相关性: 0.9286
  多样性: 0.0714

RandomForest vs NaiveBayes:
  相关性: 0.8681
  多样性: 0.1319

RandomForest vs NeuralNet:
  相关性: 0.8999
  多样性: 0.1001

XGBoost vs LightGBM:
  相关性: 0.9272
  多样性: 0.0728

XGBoost vs CatBoost:
  相关性: 0.9606
  多样性: 0.0394

XGBoost vs Logistic:
  相关性: 0.8335
  多样性: 0.1665

XGBoost vs SVM:
  相关性: 0.8095
  多样性: 0.1905

XGBoost vs NaiveBayes:
  相关性: 0.7609
  多样性: 0.2391

XGBoost vs NeuralNet:
  相关性: 0.7835
  多样性: 0.2165

LightGBM vs CatBoost:
  相关性: 0.9824
  多样性: 0.0176

LightGBM vs Logistic:
  相关性: 0.8553
  多样性: 0.1447

LightGBM vs SVM:
  相关性: 0.8335
  多样性: 0.1665

LightGBM vs NaiveBayes:
  相关性: 0.7482
  多样性: 0.2518

LightGBM vs NeuralNet:
  相关性: 0.8064
  多样性: 0.1936

CatBoost vs Logistic:
  相关性: 0.8752
  多样性: 0.1248

CatBoost vs SVM:
  相关性: 0.8568
  多样性: 0.1432

CatBoost vs NaiveBayes:
  相关性: 0.7681
  多样性: 0.2319

CatBoost vs NeuralNet:
  相关性: 0.8424
  多样性: 0.1576

Logistic vs SVM:
  相关性: 0.9532
  多样性: 0.0468

Logistic vs NaiveBayes:
  相关性: 0.8243
  多样性: 0.1757

Logistic vs NeuralNet:
  相关性: 0.9276
  多样性: 0.0724

SVM vs NaiveBayes:
  相关性: 0.8760
  多样性: 0.1240

SVM vs NeuralNet:
  相关性: 0.9240
  多样性: 0.0760

NaiveBayes vs NeuralNet:
  相关性: 0.8506
  多样性: 0.1494


3. 最优模型组合
----------------------------------------

选中模型: XGBoost, LightGBM, SVM, NaiveBayes, NeuralNet

组合性能指标:
  平均性能: 0.8699
  多样性得分: 0.3236
  类型多样性: 0.8000


4. 量化多样性分析
----------------------------------------

Q统计量分析:
  平均多样性: 0.2872
  标准差: 0.0625

熵多样性: 0.2358