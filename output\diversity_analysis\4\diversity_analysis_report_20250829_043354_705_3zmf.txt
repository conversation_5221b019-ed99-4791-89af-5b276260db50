================================================================================
多样性分析综合报告
================================================================================
生成时间: 2025-08-29 04:33:55
分析模型数量: 9
模型列表: DecisionTree, RandomForest, XGBoost, LightGBM, CatBoost, Logistic, SVM, NaiveBayes, NeuralNet
样本数量: 40

1. 模型个体性能概览
----------------------------------------
  DecisionTree: 0.7523
  RandomForest: 0.8759
  XGBoost: 0.8563
  LightGBM: 0.8989
  CatBoost: 0.8817
  Logistic: 0.8087
  SVM: 0.8537
  NaiveBayes: 0.8664
  NeuralNet: 0.8743

2. 多样性指标统计
----------------------------------------
  Q统计量 (平均绝对值): 0.9117 ± 0.0913
  不一致性度量 (平均): 0.1208 ± 0.0498
  双错度量 (平均): 0.1146 ± 0.0314
  相关系数 (平均绝对值): 0.7631 ± 0.0982

3. 多样性质量评估
----------------------------------------
  整体多样性水平: 较差 (0.2872)

4. 建议
----------------------------------------
- Q统计量过高，模型间相关性较强，建议选择更多样化的算法
  - 不一致性度量较低，模型预测过于相似，建议增加算法多样性
  - 相关系数过高，模型预测高度相关，建议选择不同类型的算法

================================================================================