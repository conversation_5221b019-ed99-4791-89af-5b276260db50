2025-08-29 01:48:05 - delong_test - INFO - 开始<PERSON><PERSON><PERSON>检验，比较10个模型
2025-08-29 01:48:05 - delong_test - INFO - 比较DecisionTree vs RandomForest: AUC1=0.9322, AUC2=0.9540, p=0.662383
2025-08-29 01:48:05 - delong_test - INFO - 比较DecisionTree vs XGBoost: AUC1=0.9322, AUC2=0.9616, p=0.545708
2025-08-29 01:48:05 - delong_test - INFO - 比较DecisionTree vs LightGBM: AUC1=0.9322, AUC2=0.9591, p=0.586957
2025-08-29 01:48:05 - delong_test - INFO - 比较DecisionTree vs CatBoost: AUC1=0.9322, AUC2=0.9642, p=0.500633
2025-08-29 01:48:05 - delong_test - INFO - 比较DecisionTree vs Logistic: AUC1=0.9322, AUC2=0.9693, p=0.413976
2025-08-29 01:48:05 - delong_test - INFO - 比较DecisionTree vs SVM: AUC1=0.9322, AUC2=0.9744, p=0.351546
2025-08-29 01:48:05 - delong_test - INFO - 比较DecisionTree vs KNN: AUC1=0.9322, AUC2=0.9437, p=0.823845
2025-08-29 01:48:05 - delong_test - INFO - 比较DecisionTree vs NaiveBayes: AUC1=0.9322, AUC2=0.9309, p=0.981234
2025-08-29 01:48:05 - delong_test - INFO - 比较DecisionTree vs NeuralNet: AUC1=0.9322, AUC2=0.9821, p=0.244752
2025-08-29 01:48:05 - delong_test - INFO - 比较RandomForest vs XGBoost: AUC1=0.9540, AUC2=0.9616, p=0.854068
2025-08-29 01:48:05 - delong_test - INFO - 比较RandomForest vs LightGBM: AUC1=0.9540, AUC2=0.9591, p=0.904408
2025-08-29 01:48:05 - delong_test - INFO - 比较RandomForest vs CatBoost: AUC1=0.9540, AUC2=0.9642, p=0.799590
2025-08-29 01:48:05 - delong_test - INFO - 比较RandomForest vs Logistic: AUC1=0.9540, AUC2=0.9693, p=0.685029
2025-08-29 01:48:05 - delong_test - INFO - 比较RandomForest vs SVM: AUC1=0.9540, AUC2=0.9744, p=0.587479
2025-08-29 01:48:05 - delong_test - INFO - 比较RandomForest vs KNN: AUC1=0.9540, AUC2=0.9437, p=0.820969
2025-08-29 01:48:05 - delong_test - INFO - 比较RandomForest vs NaiveBayes: AUC1=0.9540, AUC2=0.9309, p=0.633178
2025-08-29 01:48:05 - delong_test - INFO - 比较RandomForest vs NeuralNet: AUC1=0.9540, AUC2=0.9821, p=0.418424
2025-08-29 01:48:05 - delong_test - INFO - 比较XGBoost vs LightGBM: AUC1=0.9616, AUC2=0.9591, p=0.950608
2025-08-29 01:48:05 - delong_test - INFO - 比较XGBoost vs CatBoost: AUC1=0.9616, AUC2=0.9642, p=0.947600
2025-08-29 01:48:05 - delong_test - INFO - 比较XGBoost vs Logistic: AUC1=0.9616, AUC2=0.9693, p=0.832867
2025-08-29 01:48:05 - delong_test - INFO - 比较XGBoost vs SVM: AUC1=0.9616, AUC2=0.9744, p=0.724169
2025-08-29 01:48:05 - delong_test - INFO - 比较XGBoost vs KNN: AUC1=0.9616, AUC2=0.9437, p=0.683962
2025-08-29 01:48:05 - delong_test - INFO - 比较XGBoost vs NaiveBayes: AUC1=0.9616, AUC2=0.9309, p=0.514499
2025-08-29 01:48:05 - delong_test - INFO - 比较XGBoost vs NeuralNet: AUC1=0.9616, AUC2=0.9821, p=0.537204
2025-08-29 01:48:05 - delong_test - INFO - 比较LightGBM vs CatBoost: AUC1=0.9591, AUC2=0.9642, p=0.897876
2025-08-29 01:48:05 - delong_test - INFO - 比较LightGBM vs Logistic: AUC1=0.9591, AUC2=0.9693, p=0.784229
2025-08-29 01:48:05 - delong_test - INFO - 比较LightGBM vs SVM: AUC1=0.9591, AUC2=0.9744, p=0.680320
2025-08-29 01:48:05 - delong_test - INFO - 比较LightGBM vs KNN: AUC1=0.9591, AUC2=0.9437, p=0.732031
2025-08-29 01:48:05 - delong_test - INFO - 比较LightGBM vs NaiveBayes: AUC1=0.9591, AUC2=0.9309, p=0.556665
2025-08-29 01:48:05 - delong_test - INFO - 比较LightGBM vs NeuralNet: AUC1=0.9591, AUC2=0.9821, p=0.501618
2025-08-29 01:48:05 - delong_test - INFO - 比较CatBoost vs Logistic: AUC1=0.9642, AUC2=0.9693, p=0.882880
2025-08-29 01:48:05 - delong_test - INFO - 比较CatBoost vs SVM: AUC1=0.9642, AUC2=0.9744, p=0.767440
2025-08-29 01:48:05 - delong_test - INFO - 比较CatBoost vs KNN: AUC1=0.9642, AUC2=0.9437, p=0.631315
2025-08-29 01:48:05 - delong_test - INFO - 比较CatBoost vs NaiveBayes: AUC1=0.9642, AUC2=0.9309, p=0.468161
2025-08-29 01:48:05 - delong_test - INFO - 比较CatBoost vs NeuralNet: AUC1=0.9642, AUC2=0.9821, p=0.568009
2025-08-29 01:48:05 - delong_test - INFO - 比较Logistic vs SVM: AUC1=0.9693, AUC2=0.9744, p=0.871771
2025-08-29 01:48:05 - delong_test - INFO - 比较Logistic vs KNN: AUC1=0.9693, AUC2=0.9437, p=0.525845
2025-08-29 01:48:05 - delong_test - INFO - 比较Logistic vs NaiveBayes: AUC1=0.9693, AUC2=0.9309, p=0.379788
2025-08-29 01:48:05 - delong_test - INFO - 比较Logistic vs NeuralNet: AUC1=0.9693, AUC2=0.9821, p=0.649267
2025-08-29 01:48:05 - delong_test - INFO - 比较SVM vs KNN: AUC1=0.9744, AUC2=0.9437, p=0.445276
2025-08-29 01:48:05 - delong_test - INFO - 比较SVM vs NaiveBayes: AUC1=0.9744, AUC2=0.9309, p=0.318421
2025-08-29 01:48:05 - delong_test - INFO - 比较SVM vs NeuralNet: AUC1=0.9744, AUC2=0.9821, p=0.783771
2025-08-29 01:48:05 - delong_test - INFO - 比较KNN vs NaiveBayes: AUC1=0.9437, AUC2=0.9309, p=0.798935
2025-08-29 01:48:05 - delong_test - INFO - 比较KNN vs NeuralNet: AUC1=0.9437, AUC2=0.9821, p=0.305736
2025-08-29 01:48:05 - delong_test - INFO - 比较NaiveBayes vs NeuralNet: AUC1=0.9309, AUC2=0.9821, p=0.212786
2025-08-29 01:48:05 - delong_test - INFO - DeLong检验完成，共45个比较
2025-08-29 03:25:36 - delong_test - INFO - 开始DeLong检验，比较10个模型
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs RandomForest: AUC1=0.9540, AUC2=0.9923, p=0.165713
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs XGBoost: AUC1=0.9540, AUC2=0.9949, p=0.133052
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs LightGBM: AUC1=0.9540, AUC2=0.9974, p=0.104314
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs CatBoost: AUC1=0.9540, AUC2=0.9949, p=0.133052
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs Logistic: AUC1=0.9540, AUC2=0.9872, p=0.250589
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs SVM: AUC1=0.9540, AUC2=0.9821, p=0.352786
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs KNN: AUC1=0.9540, AUC2=0.9847, p=0.302096
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs NaiveBayes: AUC1=0.9540, AUC2=0.9540, p=1.000000
2025-08-29 03:25:36 - delong_test - INFO - 比较DecisionTree vs NeuralNet: AUC1=0.9540, AUC2=0.9847, p=0.323391
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs XGBoost: AUC1=0.9923, AUC2=0.9949, p=0.799351
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs LightGBM: AUC1=0.9923, AUC2=0.9974, p=0.556514
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs CatBoost: AUC1=0.9923, AUC2=0.9949, p=0.799351
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs Logistic: AUC1=0.9923, AUC2=0.9872, p=0.715258
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs SVM: AUC1=0.9923, AUC2=0.9821, p=0.537918
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs KNN: AUC1=0.9923, AUC2=0.9847, p=0.623045
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs NaiveBayes: AUC1=0.9923, AUC2=0.9540, p=0.199202
2025-08-29 03:25:36 - delong_test - INFO - 比较RandomForest vs NeuralNet: AUC1=0.9923, AUC2=0.9847, p=0.670420
2025-08-29 03:25:36 - delong_test - INFO - 比较XGBoost vs LightGBM: AUC1=0.9949, AUC2=0.9974, p=0.722155
2025-08-29 03:25:36 - delong_test - INFO - 比较XGBoost vs CatBoost: AUC1=0.9949, AUC2=0.9949, p=1.000000
2025-08-29 03:25:36 - delong_test - INFO - 比较XGBoost vs Logistic: AUC1=0.9949, AUC2=0.9872, p=0.559288
2025-08-29 03:25:36 - delong_test - INFO - 比较XGBoost vs SVM: AUC1=0.9949, AUC2=0.9821, p=0.420414
2025-08-29 03:25:36 - delong_test - INFO - 比较XGBoost vs KNN: AUC1=0.9949, AUC2=0.9847, p=0.490094
2025-08-29 03:25:36 - delong_test - INFO - 比较XGBoost vs NaiveBayes: AUC1=0.9949, AUC2=0.9540, p=0.165092
2025-08-29 03:25:36 - delong_test - INFO - 比较XGBoost vs NeuralNet: AUC1=0.9949, AUC2=0.9847, p=0.555493
2025-08-29 03:25:36 - delong_test - INFO - 比较LightGBM vs CatBoost: AUC1=0.9974, AUC2=0.9949, p=0.722155
2025-08-29 03:25:36 - delong_test - INFO - 比较LightGBM vs Logistic: AUC1=0.9974, AUC2=0.9872, p=0.398956
2025-08-29 03:25:36 - delong_test - INFO - 比较LightGBM vs SVM: AUC1=0.9974, AUC2=0.9821, p=0.307730
2025-08-29 03:25:36 - delong_test - INFO - 比较LightGBM vs KNN: AUC1=0.9974, AUC2=0.9847, p=0.358749
2025-08-29 03:25:36 - delong_test - INFO - 比较LightGBM vs NaiveBayes: AUC1=0.9974, AUC2=0.9540, p=0.134368
2025-08-29 03:25:36 - delong_test - INFO - 比较LightGBM vs NeuralNet: AUC1=0.9974, AUC2=0.9847, p=0.441080
2025-08-29 03:25:36 - delong_test - INFO - 比较CatBoost vs Logistic: AUC1=0.9949, AUC2=0.9872, p=0.559288
2025-08-29 03:25:36 - delong_test - INFO - 比较CatBoost vs SVM: AUC1=0.9949, AUC2=0.9821, p=0.420414
2025-08-29 03:25:36 - delong_test - INFO - 比较CatBoost vs KNN: AUC1=0.9949, AUC2=0.9847, p=0.490094
2025-08-29 03:25:36 - delong_test - INFO - 比较CatBoost vs NaiveBayes: AUC1=0.9949, AUC2=0.9540, p=0.165092
2025-08-29 03:25:36 - delong_test - INFO - 比较CatBoost vs NeuralNet: AUC1=0.9949, AUC2=0.9847, p=0.555493
2025-08-29 03:25:36 - delong_test - INFO - 比较Logistic vs SVM: AUC1=0.9872, AUC2=0.9821, p=0.783711
2025-08-29 03:25:36 - delong_test - INFO - 比较Logistic vs KNN: AUC1=0.9872, AUC2=0.9847, p=0.885436
2025-08-29 03:25:36 - delong_test - INFO - 比较Logistic vs NaiveBayes: AUC1=0.9872, AUC2=0.9540, p=0.284327
2025-08-29 03:25:36 - delong_test - INFO - 比较Logistic vs NeuralNet: AUC1=0.9872, AUC2=0.9847, p=0.897797
2025-08-29 03:25:36 - delong_test - INFO - 比较SVM vs KNN: AUC1=0.9821, AUC2=0.9847, p=0.897520
2025-08-29 03:25:36 - delong_test - INFO - 比较SVM vs NaiveBayes: AUC1=0.9821, AUC2=0.9540, p=0.383828
2025-08-29 03:25:36 - delong_test - INFO - 比较SVM vs NeuralNet: AUC1=0.9821, AUC2=0.9847, p=0.906654
2025-08-29 03:25:36 - delong_test - INFO - 比较KNN vs NaiveBayes: AUC1=0.9847, AUC2=0.9540, p=0.334529
2025-08-29 03:25:36 - delong_test - INFO - 比较KNN vs NeuralNet: AUC1=0.9847, AUC2=0.9847, p=1.000000
2025-08-29 03:25:36 - delong_test - INFO - 比较NaiveBayes vs NeuralNet: AUC1=0.9540, AUC2=0.9847, p=0.353201
2025-08-29 03:25:36 - delong_test - INFO - DeLong检验完成，共45个比较
